<!DOCTYPE html>
<html class="client-nojs view-unknown theme-none skin-theme-clientpref-day" lang="en" dir="ltr">
<head>
<meta charset="UTF-8">
<title>Neutral items - The Backpack Battles Wiki</title>
<script>(function(){var className="client-js view-unknown theme-none skin-theme-clientpref-day";var cookie=document.cookie.match(/(?:^|; )commons_enmwclientpreferences=([^;]+)/);if(cookie){cookie[1].split('%2C').forEach(function(pref){className=className.replace(new RegExp('(^| )'+pref.replace(/-clientpref-\w+$|[^\w-]+/g,'')+'-clientpref-\\w+( |$)'),'$1'+pref+'$2');});}document.documentElement.className=className;}());RLCONF={"wgBreakFrames":false,"wgSeparatorTransformTable":["",""],"wgDigitTransformTable":["",""],"wgDefaultDateFormat":"dmy","wgMonthNames":["","January","February","March","April","May","June","July","August","September","October","November","December"],"wgRequestId":"7445e585590ac7c6a2138993","wgCanonicalNamespace":"","wgCanonicalSpecialPageName":false,"wgNamespaceNumber":0,"wgPageName":"Neutral_items","wgTitle":"Neutral items","wgCurRevisionId":10321,"wgRevisionId":10321,"wgArticleId":1352,"wgIsArticle":true,"wgIsRedirect":false,"wgAction":"view","wgUserName":null,"wgUserGroups":["*"],"wgCategories":["Items"],"wgPageViewLanguage":"en","wgPageContentLanguage":"en","wgPageContentModel":"wikitext","wgRelevantPageName":"Neutral_items","wgRelevantArticleId":1352,"wgIsProbablyEditable":false,"wgRelevantPageIsProbablyEditable":false,"wgRestrictionEdit":[],"wgRestrictionMove":[],"wgPageFormsTargetName":null,"wgPageFormsAutocompleteValues":[],"wgPageFormsAutocompleteOnAllChars":false,"wgPageFormsFieldProperties":[],"wgPageFormsCargoFields":[],"wgPageFormsDependentFields":[],"wgPageFormsCalendarValues":[],"wgPageFormsCalendarParams":[],"wgPageFormsCalendarHTML":null,"wgPageFormsGridValues":[],"wgPageFormsGridParams":[],"wgPageFormsContLangYes":null,"wgPageFormsContLangNo":null,"wgPageFormsContLangMonths":[],"wgPageFormsHeightForMinimizingInstances":800,"wgPageFormsDelayReload":false,"wgPageFormsShowOnSelect":[],"wgPageFormsScriptPath":"/mw-1.43/extensions/PageForms","edgValues":[],"wgPageFormsEDSettings":null,"wgAmericanDates":false,"wgCargoDefaultQueryLimit":2000,"wgCargoMonthNamesShort":["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],"wgMediaViewerOnClick":true,"wgMediaViewerEnabledByDefault":true,"wgCiteReferencePreviewsActive":true,"wgCheckUserClientHintsHeadersJsApi":["architecture","bitness","brands","fullVersionList","mobile","model","platform","platformVersion"]};
RLSTATE={"ext.globalCssJs.user.styles":"ready","site.styles":"ready","user.styles":"ready","ext.globalCssJs.user":"ready","user":"ready","user.options":"loading","skins.vector.styles.legacy":"ready","jquery.tablesorter.styles":"ready","jquery.makeCollapsible.styles":"ready","ext.CookieWarning.styles":"ready","oojs-ui-core.styles":"ready","oojs-ui.styles.indicators":"ready","mediawiki.widgets.styles":"ready","oojs-ui-core.icons":"ready","ext.removeredlinks.styles":"ready","ext.usergroupbadges.styles":"ready","ext.embedVideo.styles":"ready","ext.globalui.styles":"ready"};RLPAGEMODULES=["ext.cargo.main","site","mediawiki.page.ready","jquery.tablesorter","jquery.makeCollapsible","skins.vector.legacy.js","ext.CookieWarning","ext.removeredlinks","ext.themes.switcher","ext.gadget.interwikiDropdownButton","mmv.bootstrap","ext.checkUser.clientHints","ext.embedVideo.overlay","ext.cargo.purge","ext.globalui"];</script>
<script>(RLQ=window.RLQ||[]).push(function(){mw.loader.impl(function(){return["user.options@12s5i",function($,jQuery,require,module){mw.user.tokens.set({"patrolToken":"+\\","watchToken":"+\\","csrfToken":"+\\"});
}];});});</script>
<link rel="stylesheet" href="/load.php?lang=en&amp;modules=ext.CookieWarning.styles%7Cext.embedVideo.styles%7Cext.globalui.styles%7Cext.removeredlinks.styles%7Cext.usergroupbadges.styles%7Cjquery.makeCollapsible.styles%7Cjquery.tablesorter.styles%7Cmediawiki.widgets.styles%7Coojs-ui-core.icons%2Cstyles%7Coojs-ui.styles.indicators%7Cskins.vector.styles.legacy&amp;only=styles&amp;skin=vector">
<script async="" src="/load.php?lang=en&amp;modules=startup&amp;only=scripts&amp;raw=1&amp;skin=vector"></script>
<meta name="ResourceLoaderDynamicStyles" content="">
<link rel="stylesheet" href="/load.php?lang=en&amp;modules=site.styles&amp;only=styles&amp;skin=vector">
<script async src="/load.php?lang=en&skin=vector&modules=ext.themes.apply&only=scripts&skin=vector&raw=1"></script>
<meta name="generator" content="MediaWiki 1.43.1">
<meta name="robots" content="max-image-preview:standard">
<meta name="format-detection" content="telephone=no">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, minimum-scale=0.25, maximum-scale=5.0">
<style type="text/css">@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/vietnamese/wght/normal.woff2);unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/cyrillic/wght/normal.woff2);unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/cyrillic-ext/wght/normal.woff2);unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/latin-ext/wght/normal.woff2);unicode-range:U+0100-02AF,U+0304,U+0308,U+0329,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/latin/wght/normal.woff2);unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;font-display:swap;}</style>
<link rel="apple-touch-icon" href="/images/4/4a/Site-favicon.ico">
<link rel="icon" href="/images/4/4a/Site-favicon.ico">
<link rel="search" type="application/opensearchdescription+xml" href="/rest.php/v1/search" title="Backpack Battles Wiki (en)">
<link rel="EditURI" type="application/rsd+xml" href="https://backpackbattles.wiki.gg/api.php?action=rsd">
<link rel="canonical" href="https://backpackbattles.wiki.gg/wiki/Neutral_items">
<link rel="license" href="https://creativecommons.org/licenses/by-sa/4.0">
<link rel="alternate" type="application/atom+xml" title="Backpack Battles Wiki Atom feed" href="/wiki/Special:RecentChanges?feed=atom">
	<meta property="fb:app_id" content="1025655121420775" prefix="fb: http://www.facebook.com/2008/fbml">

	<meta property="og:type" content="article">

	<meta property="og:site_name" content="Backpack Battles Wiki">

	<meta property="og:title" content="Neutral items">

	<meta property="og:url" content="https://backpackbattles.wiki.gg/wiki/Neutral_items">

<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-P68LVFKN');</script>
<!-- End Google Tag Manager -->
</head>
<body class="wgg-dom-version-1_43 skin-vector-legacy mediawiki ltr sitedir-ltr mw-hide-empty-elt ns-0 ns-subject page-Neutral_items rootpage-Neutral_items skin-vector action-view skin--responsive"><header id="wgg-netbar">
	<div class="wgg-netbar__left">
		<div class="wgg-netbar__logo">
			<a href="/" aria-label="wiki.gg">
				<img height=25 alt="wiki.gg logo" src="https://commons.wiki.gg/images/2/27/Network_header_logo.svg">
			</a>
		</div>
	</div>

	<nav class="wgg-netbar__user-links" aria-label="Personal tools">
		<div id="p-personal" class="mw-portlet mw-portlet-personal"  >
		    
		    <ul>
		            <li id="pt-createaccount" class="mw-list-item wgg-netbar__desktop-only"><a data-mw="interface" class=""  href="/wiki/Special:CreateAccount?returnto=Neutral+items" title="You are encouraged to create an account and log in; however, it is not mandatory"><span>Create account</span></a></li>
		            <li id="pt-login" class="mw-list-item wgg-netbar__desktop-only"><a data-mw="interface" class=""  href="/wiki/Special:UserLogin?returnto=Neutral+items" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o"><span>Log in</span></a></li>
		    </ul>
		    
		</div>

		<div id="wgg-user-menu-overflow" class="wgg-netbar__dropdown wgg-netbar__mobile-only">
			<input type="checkbox"
				id="wgg-user-menu-overflow-checkbox"
				role="button"
				aria-haspopup="true"
				aria-labelledby="wgg-user-menu-overflow-label"
			>
			<label
				id="wgg-user-menu-overflow-label"
		        for="wgg-user-menu-overflow-checkbox"
				class="wgg-netbar__icon-button"
			><span class="wgg-netbar-icon wgg-netbar-icon--overflow"></span><span>Toggle personal tools menu</span></label>

			<div class="wgg-netbar__dropdown-content">
				<div id="p-user-menu" class="mw-portlet mw-portlet-user-menu"  >
				    
				    <ul>
				            <li id="pt-createaccount-overflow" class="mw-list-item wgg-netbar__mobile-only"><a data-mw="interface" class=""  href="/wiki/Special:CreateAccount?returnto=Neutral+items" title="You are encouraged to create an account and log in; however, it is not mandatory"><span>Create account</span></a></li>
				            <li id="pt-login-overflow" class="mw-list-item wgg-netbar__mobile-only"><a data-mw="interface" class=""  href="/wiki/Special:UserLogin?returnto=Neutral+items" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o"><span>Log in</span></a></li>
				    </ul>
				    
				</div>
			</div>
		</div>
</nav>

</header>

<div id="mw-page-base" class="noprint"></div>
<div id="mw-head-base" class="noprint"></div>
<div class="content-wrapper">
	<div id="mw-navigation">
		<h2>Navigation menu</h2>
		<div id="mw-head">
			<div id="left-navigation">
				
<div id="p-namespaces" class="mw-portlet mw-portlet-namespaces vector-menu-tabs vector-menu-tabs-legacy vectorTabs vector-menu" aria-labelledby="p-namespaces-label"  >
	<h3
		id="p-namespaces-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">Namespaces</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="ca-nstab-main" class="selected mw-list-item"><a href="/wiki/Neutral_items" title="View the content page [c]" accesskey="c"><span>Page</span></a></li><li id="ca-talk" class="new mw-list-item"><a href="/wiki/Talk:Neutral_items?action=edit&amp;redlink=1" rel="discussion" class="new" title="Discussion about the content page (page does not exist) [t]" accesskey="t"><span>Discussion</span></a></li>
		</ul>
		
	</div>
</div>

				
<div id="p-variants" class="mw-portlet mw-portlet-variants emptyPortlet vector-menu-dropdown vector-menu" aria-labelledby="p-variants-label"  >
	<input type="checkbox"
		id="p-variants-checkbox"
		role="button"
		aria-haspopup="true"
		data-event-name="ui.dropdown-p-variants"
		class="vector-menu-checkbox"
		aria-labelledby="p-variants-label"
	>
	<label
		id="p-variants-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">English</span>
	</label>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			
		</ul>
		
	</div>
</div>

			</div>
			<div id="right-navigation">
				
<div id="p-views" class="mw-portlet mw-portlet-views vector-menu-tabs vector-menu-tabs-legacy vectorTabs vector-menu" aria-labelledby="p-views-label"  >
	<h3
		id="p-views-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">Views</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="ca-view" class="selected mw-list-item"><a href="/wiki/Neutral_items"><span>Read</span></a></li><li id="ca-edit" class="mw-list-item"><a href="/wiki/Special:CreateAccount?warning=accountrequiredtoedit&amp;returnto=Neutral_items&amp;returntoquery=action%3Dedit" title="Edit this page [e]" accesskey="e"><span>Sign up to edit</span></a></li><li id="ca-viewsource" class="mw-list-item"><a href="/wiki/Neutral_items?action=edit" title="This page is protected.&#10;You can view its source [e]" accesskey="e"><span>View source</span></a></li><li id="ca-history" class="mw-list-item"><a href="/wiki/Neutral_items?action=history" title="Past revisions of this page [h]" accesskey="h"><span>History</span></a></li>
		</ul>
		
	</div>
</div>

				
<div id="p-cactions" class="mw-portlet mw-portlet-cactions vectorMenu vector-menu-dropdown vector-menu" aria-labelledby="p-cactions-label"  title="More options" >
	<input type="checkbox"
		id="p-cactions-checkbox"
		role="button"
		aria-haspopup="true"
		data-event-name="ui.dropdown-p-cactions"
		class="vector-menu-checkbox"
		aria-labelledby="p-cactions-label"
	>
	<label
		id="p-cactions-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">More</span>
	</label>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="ca-cargo-purge" class="mw-list-item"><a href="/wiki/Neutral_items?action=purge"><span>Purge cache</span></a></li>
		</ul>
		
	</div>
</div>

				
<div id="p-search" role="search" class="vector-search-box-vue  vector-search-box-show-thumbnail vector-search-box-auto-expand-width vector-search-box">
	<h3 >Search</h3>
	<form action="/wiki/Special:Search" id="searchform" class="vector-search-box-form">
		<div id="simpleSearch"
			class="vector-search-box-inner"
			 data-search-loc="header-navigation">
			<input class="vector-search-box-input"
				 type="search" name="search" placeholder="Search Backpack Battles Wiki" aria-label="Search Backpack Battles Wiki" autocapitalize="sentences" spellcheck="false" title="Search Backpack Battles Wiki [f]" accesskey="f" id="searchInput"
			>
			<input id="mw-searchButton"
				 class="searchButton mw-fallbackSearchButton" type="submit" name="fulltext" title="Search the pages for this text" value="Search">
			<input id="searchButton"
				 class="searchButton" type="submit" name="go" title="Go to a page with this exact name if it exists" value="Go">
		</div>
	</form>
</div>

			</div>
		</div>
		
<div id="mw-panel">
	<div id="p-logo" role="banner">
		<a class="mw-wiki-logo" href="/"
			title="Visit the main page"></a>
	</div>
	
<div id="p-Content" class="mw-portlet mw-portlet-Content vector-menu-portal portal vector-menu" aria-labelledby="p-Content-label"  >
	<h3
		id="p-Content-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">Content</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="n-mainpage-description" class="mw-list-item"><a href="/" title="Visit the main page [z]" accesskey="z"><span>Main page</span></a></li><li id="n-Backpack-Battles" class="mw-list-item"><a href="/wiki/Backpack_Battles"><span>Backpack Battles</span></a></li><li id="n-Items" class="mw-list-item"><a href="/wiki/Items"><span>Items</span></a></li><li id="n-Game-Mechanics" class="mw-list-item"><a href="/wiki/Game_Mechanics"><span>Game Mechanics</span></a></li><li id="n-Characters" class="mw-list-item"><a href="/wiki/Characters"><span>Characters</span></a></li><li id="n-Version-History" class="mw-list-item"><a href="/wiki/Version_History"><span>Version History</span></a></li>
		</ul>
		
	</div>
</div>

	
<div id="p-navigation" class="mw-portlet mw-portlet-navigation vector-menu-portal portal vector-menu" aria-labelledby="p-navigation-label"  >
	<h3
		id="p-navigation-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">Navigation</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="n-portal" class="mw-list-item"><a href="/wiki/Backpack_Battles_Wiki:Community_portal" title="About the project, what you can do, where to find things"><span>Community portal</span></a></li><li id="n-recentchanges" class="mw-list-item"><a href="/wiki/Special:RecentChanges" title="A list of recent changes in the wiki [r]" accesskey="r"><span>Recent changes</span></a></li><li id="n-randompage" class="mw-list-item"><a href="/wiki/Special:Random" title="Load a random page [x]" accesskey="x"><span>Random page</span></a></li><li id="n-help-mediawiki" class="mw-list-item"><a href="https://www.mediawiki.org/wiki/Special:MyLanguage/Help:Contents"><span>Help about MediaWiki</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-tb" class="mw-portlet mw-portlet-tb vector-menu-portal portal vector-menu" aria-labelledby="p-tb-label"  >
	<h3
		id="p-tb-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">Tools</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="t-whatlinkshere" class="mw-list-item"><a href="/wiki/Special:WhatLinksHere/Neutral_items" title="A list of all wiki pages that link here [j]" accesskey="j"><span>What links here</span></a></li><li id="t-recentchangeslinked" class="mw-list-item"><a href="/wiki/Special:RecentChangesLinked/Neutral_items" rel="nofollow" title="Recent changes in pages linked from this page [k]" accesskey="k"><span>Related changes</span></a></li><li id="t-newpage" class="mw-list-item"><a href="/wiki/Special:NewPage" accesskey="]"><span>New page</span></a></li><li id="t-specialpages" class="mw-list-item"><a href="/wiki/Special:SpecialPages" title="A list of all special pages [q]" accesskey="q"><span>Special pages</span></a></li><li id="t-print" class="mw-list-item"><a href="javascript:print();" rel="alternate" title="Printable version of this page [p]" accesskey="p"><span>Printable version</span></a></li><li id="t-permalink" class="mw-list-item"><a href="/wiki/Neutral_items?oldid=10321" title="Permanent link to this revision of this page"><span>Permanent link</span></a></li><li id="t-info" class="mw-list-item"><a href="/wiki/Neutral_items?action=info" title="More information about this page"><span>Page information</span></a></li><li id="t-cargopagevalueslink" class="mw-list-item"><a href="/wiki/Neutral_items?action=pagevalues" rel="cargo-pagevalues"><span>Cargo data</span></a></li>
		</ul>
		
	</div>
</div>

	
</div>

	</div>
	<div id="content" class="mw-body" role="main">
		<aside id="wikigg-sl-header" data-mw="WggShowcaseLayout">
        <div
            class="wikigg-showcase__unit wikigg-showcase__reserved--lb"
            data-wgg-unit-type="internal"
        ></div>
</aside>

		<div class="content-body">
			<main>
				<a id="top"></a>
				<div id="siteNotice"></div>
				<div class="mw-indicators">
				</div>
				<h1 id="firstHeading" class="firstHeading mw-first-heading"><span class="mw-page-title-main">Neutral items</span></h1>
				<div id="bodyContent" class="vector-body">
					<div id="siteSub" class="noprint">From Backpack Battles Wiki</div>
					<div id="contentSub"></div>
					<div id="contentSub2"></div>
					
					<div id="jump-to-nav"></div>
					<a class="mw-jump-link" href="#mw-head">Jump to navigation</a>
					<a class="mw-jump-link" href="#searchInput">Jump to search</a>
					<div id="mw-content-text" class="mw-body-content"><div class="mw-content-ltr mw-parser-output" lang="en" dir="ltr"><table class="styled items nostyle-list sortable">
<tbody><tr>
<th class="unsortable">
</th>
<th><b>Name</b>
</th>
<th class="unsortable"><b>Effect</b>
</th>
<th><b>Item Type</b>
</th>
<th><b>Rarity</b>
</th>
<th><b>Cost</b>
</th></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:AmuletofAlchemy.png" class="image"><img alt="AmuletofAlchemy.png" src="/images/thumb/b/b1/AmuletofAlchemy.png/96px-AmuletofAlchemy.png?656cbc" decoding="async" loading="lazy" width="96" height="100" data-file-width="166" data-file-height="172" /></a></div></div>
</td>
<td><a href="/wiki/Amulet_of_Alchemy" title="Amulet of Alchemy">Amulet of Alchemy</a>
</td>
<td>
<ul><li><b>Start of battle:</b> Gain 3 random <a href="/wiki/Buff" title="Buff">buffs</a>.</li>
<li><b><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Potion" title="Potion">Potion</a> consumed:</b> 70% chance to repeat its effect after 2.5s.</li></ul>
</td>
<td><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>6 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:AmuletofDarkness.png" class="image"><img alt="AmuletofDarkness.png" src="/images/thumb/d/d9/AmuletofDarkness.png/97px-AmuletofDarkness.png?db0f02" decoding="async" loading="lazy" width="97" height="100" data-file-width="164" data-file-height="169" /></a></div></div>
</td>
<td><a href="/wiki/Amulet_of_Darkness" title="Amulet of Darkness">Amulet of Darkness</a>
</td>
<td>
<ul><li><b>10 <a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png?3b764e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-damage dealt:</b> Inflict 1 random <a href="/wiki/Debuff" title="Debuff">debuff</a>.</li>
<li><b><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> item activates:</b> 30% chance to deal 5 <a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png?3b764e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-damage.</li></ul>
</td>
<td><a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png?33e5e5" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>6 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:AmuletofEnergy.png" class="image"><img alt="AmuletofEnergy.png" src="/images/thumb/7/76/AmuletofEnergy.png/94px-AmuletofEnergy.png?6e2ba1" decoding="async" loading="lazy" width="94" height="100" data-file-width="164" data-file-height="174" /></a></div></div>
</td>
<td><a href="/wiki/Amulet_of_Energy" title="Amulet of Energy">Amulet of Energy</a>
</td>
<td>
<ul><li><b>Start of battle:</b> The <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> item triggers 100% faster for 1s.</li></ul>
<p><br />
</p>
<ul><li><b><a href="/wiki/Buff" title="Buff">Buff</a> used:</b> Refund 25% of the used <a href="/wiki/Buff" title="Buff">buffs</a>.</li></ul>
</td>
<td><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>6 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:AmuletofFeasting.png" class="image"><img alt="AmuletofFeasting.png" src="/images/thumb/6/6f/AmuletofFeasting.png/94px-AmuletofFeasting.png?2a06b8" decoding="async" loading="lazy" width="94" height="100" data-file-width="164" data-file-height="174" /></a></div></div>
</td>
<td><a href="/wiki/Amulet_of_Feasting" title="Amulet of Feasting">Amulet of Feasting</a>
</td>
<td>
<ul><li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Food" title="Food">Food</a> triggers 40% faster.</li>
<li><b><a href="/wiki/Food" title="Food">Food</a> bought:</b> Restock with a random <a href="/wiki/Food" title="Food">Food</a>.</li></ul>
</td>
<td><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>6 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:AmuletofLife.png" class="image"><img alt="AmuletofLife.png" src="/images/thumb/3/3c/AmuletofLife.png/94px-AmuletofLife.png?44dec4" decoding="async" loading="lazy" width="94" height="100" data-file-width="164" data-file-height="174" /></a></div></div>
</td>
<td><a href="/wiki/Amulet_of_Life" title="Amulet of Life">Amulet of Life</a>
</td>
<td>
<ul><li><b>Start of battle:</b> Gain 20 maximum health.</li>
<li>Your <a href="/wiki/Heal" title="Heal">healing</a> is increased by 20%.</li></ul>
</td>
<td><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>6 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:AmuletofSteel.png" class="image"><img alt="AmuletofSteel.png" src="/images/thumb/6/62/AmuletofSteel.png/94px-AmuletofSteel.png?411a34" decoding="async" loading="lazy" width="94" height="100" data-file-width="164" data-file-height="174" /></a></div></div>
</td>
<td><a href="/wiki/Amulet_of_Steel" title="Amulet of Steel">Amulet of Steel</a>
</td>
<td>
<ul><li><b>Start of battle:</b> Gain 25 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li><b><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> items gained 35 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>:</b> Gain 1 <a href="/wiki/Empower" title="Empower"><img alt="Empower" src="/images/thumb/5/57/Empower.png/15px-Empower.png?31c0e4" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.</li></ul>
</td>
<td><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>6 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:AmuletoftheWild.png" class="image"><img alt="AmuletoftheWild.png" src="/images/thumb/5/55/AmuletoftheWild.png/94px-AmuletoftheWild.png?ca9820" decoding="async" loading="lazy" width="94" height="100" data-file-width="164" data-file-height="174" /></a></div></div>
</td>
<td><a href="/wiki/Amulet_of_the_Wild" title="Amulet of the Wild">Amulet of the Wild</a>
</td>
<td>
<ul><li><b>After 5s:</b> Trigger the <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Pet" title="Pet">Pet</a> and gain 4 <a href="/wiki/Spikes" title="Spikes"><img alt="Spikes" src="/images/thumb/9/9a/Spikes.png/15px-Spikes.png?ea75dd" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li>Return damage limit of <a href="/wiki/Spikes" title="Spikes"><img alt="Spikes" src="/images/thumb/9/9a/Spikes.png/15px-Spikes.png?ea75dd" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> against <a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a> and <a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png?c8ee7a" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a> attacks +50%.</li></ul>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>6 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:ArtifactStoneCold.png" class="image"><img alt="ArtifactStoneCold.png" src="/images/thumb/5/59/ArtifactStoneCold.png/88px-ArtifactStoneCold.png?f7e6ec" decoding="async" loading="lazy" width="88" height="100" data-file-width="100" data-file-height="113" /></a></div></div>
</td>
<td><a href="/wiki/Artifact_Stone:_Cold" title="Artifact Stone: Cold">Artifact Stone: Cold</a>
</td>
<td>
<ul><li>Can only be thrown once per battle.</li>
<li><b>On hit:</b> Inflict 3 <a href="/wiki/Cold" title="Cold"><img alt="Cold" src="/images/thumb/6/6b/Cold.png/15px-Cold.png?f89f07" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.</li>
<li><b><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Weapon" title="Weapon">Weapon</a> hits:</b> Inflict 1 <a href="/wiki/Cold" title="Cold"><img alt="Cold" src="/images/thumb/6/6b/Cold.png/15px-Cold.png?f89f07" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.</li></ul>
</td>
<td><a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Ice" title="Ice"><img alt="Ice" src="/images/thumb/5/52/Icon_Ice.png/15px-Icon_Ice.png?61b1d9" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png?c8ee7a" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>10 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:ArtifactStoneDeath.png" class="image"><img alt="ArtifactStoneDeath.png" src="/images/thumb/5/55/ArtifactStoneDeath.png/83px-ArtifactStoneDeath.png?354915" decoding="async" loading="lazy" width="83" height="100" data-file-width="117" data-file-height="141" /></a></div></div>
</td>
<td><a href="/wiki/Artifact_Stone:_Death" title="Artifact Stone: Death">Artifact Stone: Death</a>
</td>
<td>
<ul><li>Can only be thrown once per battle.</li>
<li><b>On hit:</b> Inflict <a href="/wiki/Fatigue" title="Fatigue">Fatigue</a> damage.</li>
<li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> items have +7% <a href="/wiki/Critical_hits" title="Critical hits">critical hit</a> chance per <a href="/wiki/Fatigue" title="Fatigue">Fatigue</a> level of your opponent.</li></ul>
</td>
<td><a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png?33e5e5" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png?c8ee7a" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>8 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:ArtifactStoneHeat.png" class="image"><img alt="ArtifactStoneHeat.png" src="/images/thumb/c/c2/ArtifactStoneHeat.png/86px-ArtifactStoneHeat.png?96be74" decoding="async" loading="lazy" width="86" height="100" data-file-width="98" data-file-height="114" /></a></div></div>
</td>
<td><a href="/wiki/Artifact_Stone:_Heat" title="Artifact Stone: Heat">Artifact Stone: Heat</a>
</td>
<td>
<ul><li>Can only be thrown once per battle.</li>
<li><b>On hit:</b> Gain 3 <a href="/wiki/Heat" title="Heat"><img alt="Heat" src="/images/thumb/c/c4/Heat.png/15px-Heat.png?4a5362" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.</li>
<li><b>10 <a href="/wiki/Heat" title="Heat"><img alt="Heat" src="/images/thumb/c/c4/Heat.png/15px-Heat.png?4a5362" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a> reached:</b> <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Weapon" title="Weapon">Weapons</a> gain 8 damage.</li></ul>
</td>
<td><a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Fire" title="Fire"><img alt="Fire" src="/images/thumb/0/0a/Icon_Fire.png/15px-Icon_Fire.png?54f5a7" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png?c8ee7a" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>9 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:BagofStones.png" class="image"><img alt="BagofStones.png" src="/images/thumb/9/99/BagofStones.png/100px-BagofStones.png?6080b9" decoding="async" loading="lazy" width="100" height="50" data-file-width="320" data-file-height="160" /></a></div></div>
</td>
<td><a href="/wiki/Bag_of_Stones" title="Bag of Stones">Bag of Stones</a>
</td>
<td>
<p><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> Stones above can be thrown repeatedly.
</p>
</td>
<td><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>3 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Bagtacular.png" class="image"><img alt="Bagtacular.png" src="/images/thumb/8/8a/Bagtacular.png/98px-Bagtacular.png?fcc182" decoding="async" loading="lazy" width="98" height="100" data-file-width="135" data-file-height="138" /></a></div></div>
</td>
<td><a href="/wiki/Bagtacular" title="Bagtacular">Bagtacular</a>
</td>
<td>
<ul><li><a href="/wiki/Fanny_Pack" title="Fanny Pack">Fanny Packs</a> give +10% trigger speed.</li>
<li><a href="/wiki/Stamina_Sack" title="Stamina Sack">Stamina Sacks</a> give 10% base stamina regeneration.</li>
<li><a href="/wiki/Potion_Belt" title="Potion Belt">Potion Belts</a> give 2 <a href="/wiki/Buff" title="Buff">buffs</a> when a <a href="/wiki/Potion" title="Potion">Potion</a> inside is consumed.</li>
<li><a href="/wiki/Protective_Purse" title="Protective Purse">Protective Purses</a> give +15 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li></ul>
</td>
<td><a href="/wiki/Skill" title="Skill">Skill</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Banana.png" class="image"><img alt="Banana.png" src="/images/thumb/6/69/Banana.png/100px-Banana.png?90a3c2" decoding="async" loading="lazy" width="100" height="100" data-file-width="320" data-file-height="320" /></a></div></div>
</td>
<td><a href="/wiki/Banana" title="Banana">Banana</a>
</td>
<td>
<p><b>Every 5s:</b> <a href="/wiki/Heal" title="Heal">Heal</a> for 4 and regenerate 1 stamina.
</p>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Food" title="Food">Food</a>
</td>
<td data-sort-value="1"><a href="/wiki/Common" title="Common">Common</a>
</td>
<td>3 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:BloodAmulet.png" class="image"><img alt="BloodAmulet.png" src="/images/thumb/5/59/BloodAmulet.png/91px-BloodAmulet.png?4f493d" decoding="async" loading="lazy" width="91" height="100" data-file-width="160" data-file-height="175" /></a></div></div>
</td>
<td><a href="/wiki/Blood_Amulet" title="Blood Amulet">Blood Amulet</a>
</td>
<td>
<p><b>Start of battle:</b> Gain 2 <a href="/wiki/Vampirism" title="Vampirism"><img alt="Vampirism" src="/images/thumb/3/33/Vampirism.png/15px-Vampirism.png?823c3c" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> and 20 maximum health.
</p>
</td>
<td><a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png?8cee0c" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>8 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:BloodGoobert.png" class="image"><img alt="BloodGoobert.png" src="/images/thumb/0/02/BloodGoobert.png/97px-BloodGoobert.png?bb918f" decoding="async" loading="lazy" width="97" height="100" data-file-width="301" data-file-height="309" /></a></div></div>
</td>
<td><a href="/wiki/Blood_Goobert" title="Blood Goobert">Blood Goobert</a>
</td>
<td>
<ul><li><b>Start of battle:</b> Gain 2 <a href="/wiki/Vampirism" title="Vampirism"><img alt="Vampirism" src="/images/thumb/3/33/Vampirism.png/15px-Vampirism.png?823c3c" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li><b>6 <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> item activations:</b> Deal 10 <a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png?3b764e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-damage with 100% lifesteal. Deal +1 for each <a href="/wiki/Vampirism" title="Vampirism"><img alt="Vampirism" src="/images/thumb/3/33/Vampirism.png/15px-Vampirism.png?823c3c" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li></ul>
</td>
<td><a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png?8cee0c" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Pet" title="Pet">Pet</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>15 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:BloodHarvester.png" class="image"><img alt="BloodHarvester.png" src="/images/thumb/0/0a/BloodHarvester.png/65px-BloodHarvester.png?180e09" decoding="async" loading="lazy" width="65" height="100" data-file-width="421" data-file-height="649" /></a></div></div>
</td>
<td><a href="/wiki/Blood_Harvester" title="Blood Harvester">Blood Harvester</a>
</td>
<td>
<ul><li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> Items give +100% <a href="/wiki/Vampirism" title="Vampirism"><img alt="Vampirism" src="/images/thumb/3/33/Vampirism.png/15px-Vampirism.png?823c3c" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li>Attacks 5% faster for every <a href="/wiki/Vampirism" title="Vampirism"><img alt="Vampirism" src="/images/thumb/3/33/Vampirism.png/15px-Vampirism.png?823c3c" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li></ul>
</td>
<td><a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png?8cee0c" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>7 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:BloodManipulation.png" class="image"><img alt="BloodManipulation.png" src="/images/thumb/0/07/BloodManipulation.png/89px-BloodManipulation.png?65e728" decoding="async" loading="lazy" width="89" height="100" data-file-width="132" data-file-height="149" /></a></div></div>
</td>
<td><a href="/wiki/Blood_Manipulation" title="Blood Manipulation">Blood Manipulation</a>
</td>
<td>
<ul><li>Deal 20% of your <a href="/wiki/Heal" title="Heal">healing</a> as <a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png?3b764e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-damage.</li>
<li><b>Every 3s:</b> Gain 1 <a href="/wiki/Vampirism" title="Vampirism"><img alt="Vampirism" src="/images/thumb/3/33/Vampirism.png/15px-Vampirism.png?823c3c" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li>Triggers 15% faster for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png?8cee0c" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-item.</li></ul>
</td>
<td><a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png?8cee0c" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Skill" title="Skill">Skill</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Bloodthorne.png" class="image"><img alt="Bloodthorne.png" src="/images/thumb/0/02/Bloodthorne.png/33px-Bloodthorne.png?8da1c8" decoding="async" loading="lazy" width="33" height="100" data-file-width="160" data-file-height="480" /></a></div></div>
</td>
<td><a href="/wiki/Bloodthorne" title="Bloodthorne">Bloodthorne</a>
</td>
<td>
<p><b>On hit:</b> Use 1 <a href="/wiki/Regeneration" title="Regeneration"><img alt="Regeneration" src="/images/thumb/5/53/Regeneration.png/15px-Regeneration.png?7bd3bc" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> to gain 1 <a href="/wiki/Vampirism" title="Vampirism"><img alt="Vampirism" src="/images/thumb/3/33/Vampirism.png/15px-Vampirism.png?823c3c" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> and 1 <a href="/wiki/Spikes" title="Spikes"><img alt="Spikes" src="/images/thumb/9/9a/Spikes.png/15px-Spikes.png?ea75dd" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>. Deals +1 damage per <a href="/wiki/Spikes" title="Spikes"><img alt="Spikes" src="/images/thumb/9/9a/Spikes.png/15px-Spikes.png?ea75dd" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> and <a href="/wiki/Vampirism" title="Vampirism"><img alt="Vampirism" src="/images/thumb/3/33/Vampirism.png/15px-Vampirism.png?823c3c" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.
</p>
</td>
<td><a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png?8cee0c" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>15 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:BloodyDagger.png" class="image"><img alt="BloodyDagger.png" src="/images/thumb/a/a1/BloodyDagger.png/50px-BloodyDagger.png?fa4eb6" decoding="async" loading="lazy" width="50" height="100" data-file-width="160" data-file-height="320" /></a></div></div>
</td>
<td><a href="/wiki/Bloody_Dagger" title="Bloody Dagger">Bloody Dagger</a>
</td>
<td>
<ul><li><b>On hit:</b> Gain 1 <a href="/wiki/Vampirism" title="Vampirism"><img alt="Vampirism" src="/images/thumb/3/33/Vampirism.png/15px-Vampirism.png?823c3c" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> (up to 5 per battle). <a href="/wiki/Heal" title="Heal">Heal</a> 4 per <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png?8cee0c" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-item.</li>
<li><b>On <a href="/wiki/Stun" title="Stun">stun</a>:</b> Triggers extra attack.</li></ul>
</td>
<td><a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png?8cee0c" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>12 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Blueberries.png" class="image"><img alt="Blueberries.png" src="/images/thumb/a/af/Blueberries.png/100px-Blueberries.png?f70ba7" decoding="async" loading="lazy" width="100" height="100" data-file-width="160" data-file-height="160" /></a></div></div>
</td>
<td><a href="/wiki/Blueberries" title="Blueberries">Blueberries</a>
</td>
<td>
<p><b>Every 3.5s:</b> Gain 1 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>. If you have at least 10 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>: gain 1 <a href="/wiki/Luck" title="Luck"><img alt="Luck" src="/images/thumb/f/f0/Luck.png/15px-Luck.png?9ec9f2" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> instead.
</p>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Food" title="Food">Food</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>2 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:BoxofProsperity.png" class="image"><img alt="BoxofProsperity.png" src="/images/thumb/9/94/BoxofProsperity.png/99px-BoxofProsperity.png?2f8f1a" decoding="async" loading="lazy" width="99" height="100" data-file-width="358" data-file-height="360" /></a></div></div>
</td>
<td><a href="/wiki/Box_of_Prosperity" title="Box of Prosperity">Box of Prosperity</a>
</td>
<td>
<ul><li>Add 4 backpack slots.</li>
<li><b>Shop entered:</b> If this has at least 2 <a href="/wiki/Godly" title="Godly">Godly</a> or <a href="/wiki/Unique" title="Unique">Unique</a> items inside, generate a chipped <a href="/wiki/Gemstone" title="Gemstone">Gemstone</a>.</li></ul>
</td>
<td><a href="/wiki/Bag" title="Bag">Bag</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:BoxofRiches.png" class="image"><img alt="BoxofRiches.png" src="/images/thumb/2/2a/BoxofRiches.png/100px-BoxofRiches.png?47d246" decoding="async" loading="lazy" width="100" height="60" data-file-width="292" data-file-height="175" /></a></div></div>
</td>
<td><a href="/wiki/Box_of_Riches" title="Box of Riches">Box of Riches</a>
</td>
<td>
<p>Shop entered: Generate a low-quality <a href="/wiki/Gemstone" title="Gemstone">gemstone</a>. <a href="/wiki/Gemstone" title="Gemstone">Gemstones</a> are offered in the shop.
</p>
</td>
<td><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:BPB_Amethyst_Line.png" class="image"><img alt="BPB Amethyst Line.png" src="/images/thumb/f/f2/BPB_Amethyst_Line.png/100px-BPB_Amethyst_Line.png?535ff2" decoding="async" loading="lazy" width="100" height="21" data-file-width="425" data-file-height="90" /></a></div></div>
</td>
<td><a href="/wiki/Amethyst" title="Amethyst">Amethyst</a>
</td>
<td>
<div style="font-size:100%; text-align:center;"><font color="gray">Weapon sockets:</font></div>
<p><b>On hit:</b> 20/30/45/65/100% chance to remove a random <a href="/wiki/Buff" title="Buff">buff</a> from your opponent.
</p>
<br /><div style="font-size:100%; text-align:center;"><font color="gray">Armor &amp; other sockets:</font></div>
<p>Reduce opponent's <a href="/wiki/Heal" title="Heal">healing</a> by 15/20/25/30/40%.
</p>
<br /><div style="font-size:100%; text-align:center;"><font color="gray">Backpack:</font></div>
<p><b>Every 4/3/2.5/2/1.2s:</b> Cleanse 1 <a href="/wiki/Debuff" title="Debuff">debuff</a>.
</p>
</td>
<td><a href="/wiki/Gemstone" title="Gemstone">Gemstone</a>
</td>
<td data-sort-value="0"><a href="/wiki/Varies?action=edit&amp;redlink=1" class="new" title="Varies (page does not exist)" rel="nofollow">Varies</a>
</td>
<td>1/2/4/8/16 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:BPB_Emerald_Line.png" class="image"><img alt="BPB Emerald Line.png" src="/images/thumb/f/fb/BPB_Emerald_Line.png/100px-BPB_Emerald_Line.png?9861b7" decoding="async" loading="lazy" width="100" height="21" data-file-width="425" data-file-height="90" /></a></div></div>
</td>
<td><a href="/wiki/Emerald" title="Emerald">Emerald</a>
</td>
<td>
<div style="font-size:100%; text-align:center;"><font color="gray">Weapon sockets:</font></div>
<p><b>On hit:</b> 35/50/80/80/100% chance to inflict 1/1/1/2/3 <a href="/wiki/Poison" title="Poison"><img alt="Poison" src="/images/thumb/0/05/Poison.png/15px-Poison.png?984131" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.
</p>
<br /><div style="font-size:100%; text-align:center;"><font color="gray">Armor &amp; other sockets:</font></div>
<p>10/15/20/25/35% chance to <a href="/wiki/Resist" title="Resist">resist</a> <a href="/wiki/Poison" title="Poison"><img alt="Poison" src="/images/thumb/0/05/Poison.png/15px-Poison.png?984131" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.
</p>
<br /><div style="font-size:100%; text-align:center;"><font color="gray">Backpack:</font></div>
<p><b>After 3/4/4/3.5/3s:</b> Gain 1/2/3/4/6 <a href="/wiki/Regeneration" title="Regeneration"><img alt="Regeneration" src="/images/thumb/5/53/Regeneration.png/15px-Regeneration.png?7bd3bc" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.
</p>
</td>
<td><a href="/wiki/Gemstone" title="Gemstone">Gemstone</a>
</td>
<td data-sort-value="0"><a href="/wiki/Varies?action=edit&amp;redlink=1" class="new" title="Varies (page does not exist)" rel="nofollow">Varies</a>
</td>
<td>1/2/4/8/16 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:BPB_Ruby_Line.png" class="image"><img alt="BPB Ruby Line.png" src="/images/thumb/2/2f/BPB_Ruby_Line.png/100px-BPB_Ruby_Line.png?b03e69" decoding="async" loading="lazy" width="100" height="21" data-file-width="425" data-file-height="90" /></a></div></div>
</td>
<td><a href="/wiki/Ruby" title="Ruby">Ruby</a>
</td>
<td>
<div style="font-size:100%; text-align:center;"><font color="gray">Weapon sockets:</font></div>
<p><b>On hit:</b> Gain 7/10/15/20/30% lifesteal.
</p>
<br /><div style="font-size:100%; text-align:center;"><font color="gray">Armor &amp; other sockets:</font></div>
<p>Your <a href="/wiki/Heal" title="Heal">healing</a> is increased by 10/15/20/25/35%.
</p>
<br /><div style="font-size:100%; text-align:center;"><font color="gray">Backpack:</font></div>
<p><b>After 5s:</b> Deal 4/6/10/15/30 <a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png?3b764e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-damage with 100% lifesteal.
</p>
</td>
<td><a href="/wiki/Gemstone" title="Gemstone">Gemstone</a>
</td>
<td data-sort-value="0"><a href="/wiki/Varies?action=edit&amp;redlink=1" class="new" title="Varies (page does not exist)" rel="nofollow">Varies</a>
</td>
<td>1/2/4/8/16 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:BPB_Sapphire_Line.png" class="image"><img alt="BPB Sapphire Line.png" src="/images/thumb/8/86/BPB_Sapphire_Line.png/100px-BPB_Sapphire_Line.png?330f8d" decoding="async" loading="lazy" width="100" height="21" data-file-width="425" data-file-height="90" /></a></div></div>
</td>
<td><a href="/wiki/Sapphire" title="Sapphire">Sapphire</a>
</td>
<td>
<div style="font-size:100%; text-align:center;"><font color="gray">Weapon sockets:</font></div>
<p><b>On hit:</b> 15/25/40/60/80% chance to ignore <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>, gain 1 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> and inflict 1 <a href="/wiki/Cold" title="Cold"><img alt="Cold" src="/images/thumb/6/6b/Cold.png/15px-Cold.png?f89f07" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.
</p>
<br /><div style="font-size:100%; text-align:center;"><font color="gray">Armor &amp; other sockets:</font></div>
<p><b>5 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> gained:</b> Gain 2/3/4/5/7 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.
</p>
<br /><div style="font-size:100%; text-align:center;"><font color="gray">Backpack:</font></div>
<p><b>After 3/4/4/3.5/3s:</b> Inflict 1/2/3/4/6 <a href="/wiki/Cold" title="Cold"><img alt="Cold" src="/images/thumb/6/6b/Cold.png/15px-Cold.png?f89f07" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.
</p>
</td>
<td><a href="/wiki/Gemstone" title="Gemstone">Gemstone</a>
</td>
<td data-sort-value="0"><a href="/wiki/Varies?action=edit&amp;redlink=1" class="new" title="Varies (page does not exist)" rel="nofollow">Varies</a>
</td>
<td>1/2/4/8/16 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:BPB_Topaz_Line.png" class="image"><img alt="BPB Topaz Line.png" src="/images/thumb/4/4e/BPB_Topaz_Line.png/100px-BPB_Topaz_Line.png?488599" decoding="async" loading="lazy" width="100" height="21" data-file-width="425" data-file-height="90" /></a></div></div>
</td>
<td><a href="/wiki/Topaz" title="Topaz">Topaz</a>
</td>
<td>
<div style="font-size:100%; text-align:center;"><font color="gray">Weapon sockets:</font></div>
<p>Attacks 10/15/20/25/35% faster.
</p>
<br /><div style="font-size:100%; text-align:center;"><font color="gray">Armor &amp; other sockets:</font></div>
<ul><li>10/15/20/30/35% chance to <a href="/wiki/Resist" title="Resist">resist</a> <a href="/wiki/Stun" title="Stun">stuns</a>.</li>
<li>4/6/8/10/15% chance resist <a href="/wiki/Critical_hits" title="Critical hits">critical hits</a>.</li></ul>
<br /><div style="font-size:100%; text-align:center;"><font color="gray">Backpack:</font></div>
<p>Base stamina regeneration +8/12/20/30/45%.
</p>
</td>
<td><a href="/wiki/Gemstone" title="Gemstone">Gemstone</a>
</td>
<td data-sort-value="0"><a href="/wiki/Varies?action=edit&amp;redlink=1" class="new" title="Varies (page does not exist)" rel="nofollow">Varies</a>
</td>
<td>1/2/4/8/16 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Broom.png" class="image"><img alt="Broom.png" src="/images/thumb/2/26/Broom.png/22px-Broom.png?972204" decoding="async" loading="lazy" width="22" height="100" data-file-width="141" data-file-height="617" /></a></div></div>
</td>
<td><a href="/wiki/Broom" title="Broom">Broom</a>
</td>
<td>
<ul><li><b>Opponent misses attack:</b> Gain +2 damage for the next attack.</li>
<li><b>On hit:</b> 33% chance to inflict 1 <a href="/wiki/Blind" title="Blind"><img alt="Blind" src="/images/thumb/6/68/Blind.png/15px-Blind.png?f2b891" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li></ul>
</td>
<td><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="1"><a href="/wiki/Common" title="Common">Common</a>
</td>
<td>4 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:BunchofCoins.png" class="image"><img alt="BunchofCoins.png" src="/images/thumb/a/ab/BunchofCoins.png/98px-BunchofCoins.png?2bb8bc" decoding="async" loading="lazy" width="98" height="100" data-file-width="142" data-file-height="145" /></a></div></div>
</td>
<td><a href="/wiki/Bunch_of_Coins" title="Bunch of Coins">Bunch of Coins</a>
</td>
<td>
<p>They don't do anything. But you can sell them for profit!
</p>
</td>
<td><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>9 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:BurningCoal.png" class="image"><img alt="BurningCoal.png" src="/images/b/bc/BurningCoal.png?3aa66a" decoding="async" loading="lazy" width="86" height="100" data-file-width="74" data-file-height="86" /></a></div></div>
</td>
<td><a href="/wiki/Burning_Coal" title="Burning Coal">Burning Coal</a>
</td>
<td>
<div style="font-size:100%; text-align:center;"><font color="gray">Weapon sockets:</font></div>
<p><b>On hit:</b> 12% chance to deal +6 damage and gain 1 <a href="/wiki/Heat" title="Heat"><img alt="Heat" src="/images/thumb/c/c4/Heat.png/15px-Heat.png?4a5362" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.
</p>
<br /><div style="font-size:100%; text-align:center;"><font color="gray">Armor &amp; other sockets:</font></div>
<p><b>Start of battle:</b> Gain 15 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.
<a href="/wiki/Resist" title="Resist">Resist</a> 5 <a href="/wiki/Cold" title="Cold"><img alt="Cold" src="/images/thumb/6/6b/Cold.png/15px-Cold.png?f89f07" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.
</p>
<br /><div style="font-size:100%; text-align:center;"><font color="gray">Backpack:</font></div>
<p><b>After 5s:</b> Gain 2 <a href="/wiki/Heat" title="Heat"><img alt="Heat" src="/images/thumb/c/c4/Heat.png/15px-Heat.png?4a5362" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>, cleanse 3 <a href="/wiki/Debuff" title="Debuff">debuffs</a>.
</p>
</td>
<td><a href="/wiki/Fire" title="Fire"><img alt="Fire" src="/images/thumb/0/0a/Icon_Fire.png/15px-Icon_Fire.png?54f5a7" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Gemstone" title="Gemstone">Gemstone</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>2 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:BurningTorch.png" class="image"><img alt="BurningTorch.png" src="/images/thumb/9/9c/BurningTorch.png/40px-BurningTorch.png?618279" decoding="async" loading="lazy" width="40" height="100" data-file-width="71" data-file-height="177" /></a></div></div>
</td>
<td><a href="/wiki/Burning_Torch" title="Burning Torch">Burning Torch</a>
</td>
<td>
<ul><li><b>Start of battle:</b> Gain 2 <a href="/wiki/Heat" title="Heat"><img alt="Heat" src="/images/thumb/c/c4/Heat.png/15px-Heat.png?4a5362" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.</li>
<li><b>On hit:</b> 30% chance to gain 1 damage.</li></ul>
</td>
<td><a href="/wiki/Fire" title="Fire"><img alt="Fire" src="/images/thumb/0/0a/Icon_Fire.png/15px-Icon_Fire.png?54f5a7" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:BuytheHolyLight.png" class="image"><img alt="BuytheHolyLight.png" src="/images/thumb/a/a1/BuytheHolyLight.png/97px-BuytheHolyLight.png?c2a240" decoding="async" loading="lazy" width="97" height="100" data-file-width="141" data-file-height="146" /></a></div></div>
</td>
<td><a href="/wiki/Buy_the_Holy_Light" title="Buy the Holy Light">Buy the Holy Light</a>
</td>
<td>
<ul><li><a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-Items have +15% chance to be on sale.</li>
<li><a href="/wiki/Oil_Lamp" title="Oil Lamp">Oil Lamps</a> and <a href="/wiki/Djinn_Lamp" title="Djinn Lamp">Djinn Lamps</a> gain <a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>.</li>
<li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-Items trigger 40% faster.</li></ul>
</td>
<td><a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Skill" title="Skill">Skill</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:CapofDiscomfort.png" class="image"><img alt="CapofDiscomfort.png" src="/images/thumb/a/a6/CapofDiscomfort.png/57px-CapofDiscomfort.png?14f1db" decoding="async" loading="lazy" width="57" height="100" data-file-width="178" data-file-height="309" /></a></div></div>
</td>
<td><a href="/wiki/Cap_of_Discomfort" title="Cap of Discomfort">Cap of Discomfort</a>
</td>
<td>
<ul><li><b>Start of battle:</b> Reduce damage taken by 25% for 5s.</li>
<li><b>Opponent gains <a href="/wiki/Buff" title="Buff">buff</a>:</b> 15% chance to <a href="/wiki/Nullify" title="Nullify">nullify</a> it.</li>
<li>Your opponent's <a href="/wiki/Heal" title="Heal">healing</a> is reduced by 30%.</li></ul>
</td>
<td><a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png?33e5e5" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Helmet" title="Helmet">Helmet</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>14 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:CapofResilience.png" class="image"><img alt="CapofResilience.png" src="/images/thumb/1/1a/CapofResilience.png/56px-CapofResilience.png?4dad8c" decoding="async" loading="lazy" width="56" height="100" data-file-width="166" data-file-height="295" /></a></div></div>
</td>
<td><a href="/wiki/Cap_of_Resilience" title="Cap of Resilience">Cap of Resilience</a>
</td>
<td>
<ul><li><b>Start of battle:</b> Reduce damage taken by 25% for 3s.</li>
<li>15% chance to resist <a href="/wiki/Critical_hits" title="Critical hits">critical hits</a>.</li>
<li>15% chance to resist <a href="/wiki/Stun" title="Stun">stuns</a>.</li></ul>
</td>
<td><a href="/wiki/Helmet" title="Helmet">Helmet</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>7 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Chtulhu.png" class="image"><img alt="Chtulhu.png" src="/images/thumb/b/b8/Chtulhu.png/100px-Chtulhu.png?65006a" decoding="async" loading="lazy" width="100" height="69" data-file-width="442" data-file-height="303" /></a></div></div>
</td>
<td><a href="/wiki/Chtulhu" title="Chtulhu">Chtulhu</a>
</td>
<td>
<ul><li><b>Every 3.3s:</b> Deal 10 <a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png?3b764e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-damage with 100% lifesteal and trigger a random <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Food" title="Food">Food</a>.</li>
<li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Food" title="Food">Food</a> gains <a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png?33e5e5" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>.</li>
<li>Triggers 15% faster for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png?33e5e5" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-item.</li></ul>
</td>
<td><a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png?33e5e5" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Pet" title="Pet">Pet</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>8 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:ClawsofAttack.png" class="image"><img alt="ClawsofAttack.png" src="/images/thumb/f/f4/ClawsofAttack.png/100px-ClawsofAttack.png?f98575" decoding="async" loading="lazy" width="100" height="55" data-file-width="332" data-file-height="181" /></a></div></div>
</td>
<td><a href="/wiki/Claws_of_Attack" title="Claws of Attack">Claws of Attack</a>
</td>
<td>
<ul><li>After 4 hits, gain 1 <a href="/wiki/Empower" title="Empower"><img alt="Empower" src="/images/thumb/5/57/Empower.png/15px-Empower.png?31c0e4" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.</li>
<li>Attacks 5% faster for every <a href="/wiki/Spikes" title="Spikes"><img alt="Spikes" src="/images/thumb/9/9a/Spikes.png/15px-Spikes.png?ea75dd" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li></ul>
</td>
<td><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>8 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:CorruptedArmor.png" class="image"><img alt="CorruptedArmor.png" src="/images/thumb/9/9c/CorruptedArmor.png/74px-CorruptedArmor.png?92b27b" decoding="async" loading="lazy" width="74" height="100" data-file-width="367" data-file-height="497" /></a></div></div>
</td>
<td><a href="/wiki/Corrupted_Armor" title="Corrupted Armor">Corrupted Armor</a>
</td>
<td>
<ul><li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-Items gain <a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png?33e5e5" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>.</li>
<li>10% chance for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png?33e5e5" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-item to protect <a href="/wiki/Debuff" title="Debuff">debuffs</a> on your opponent from being cleansed.</li>
<li><b>Start of battle:</b> Gain 85 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li><b>Every 2.4s:</b> Cleanse 2 <a href="/wiki/Debuff" title="Debuff">debuffs</a> and inflict them on your opponent.</li></ul>
</td>
<td><a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png?33e5e5" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Armor" title="Armor">Armor</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>20 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:CorruptedCrystal.png" class="image"><img alt="CorruptedCrystal.png" src="/images/thumb/a/a9/CorruptedCrystal.png/62px-CorruptedCrystal.png?827970" decoding="async" loading="lazy" width="62" height="100" data-file-width="67" data-file-height="108" /></a></div></div>
</td>
<td><a href="/wiki/Corrupted_Crystal" title="Corrupted Crystal">Corrupted Crystal</a>
</td>
<td>
<div style="font-size:100%; text-align:center;"><font color="gray">Weapon sockets:</font></div>
<p><b>Opponent below 30% health:</b> Deal +50% damage.
</p>
<br /><div style="font-size:100%; text-align:center;"><font color="gray">Armor &amp; other sockets:</font></div>
<p><b>7 <a href="/wiki/Debuff" title="Debuff">debuffs</a> inflicted:</b> Gain  6 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.
</p>
<br /><div style="font-size:100%; text-align:center;"><font color="gray">Backpack:</font></div>
<p><b>Every 5.5s:</b> Inflict <a href="/wiki/Fatigue" title="Fatigue">Fatigue</a> damage.
</p>
</td>
<td><a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png?33e5e5" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Gemstone" title="Gemstone">Gemstone</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>7 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Crossblades.png" class="image"><img alt="Crossblades.png" src="/images/thumb/8/8c/Crossblades.png/100px-Crossblades.png?62087d" decoding="async" loading="lazy" width="100" height="98" data-file-width="497" data-file-height="486" /></a></div></div>
</td>
<td><a href="/wiki/Crossblades" title="Crossblades">Crossblades</a>
</td>
<td>
<ul><li><b>Start of battle:</b> The <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Weapon" title="Weapon">Weapon</a> gains 10 damage. The <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Diamond" src="/images/thumb/e/ea/Diamond.png/15px-Diamond.png?f49dcb" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> item triggers 60% faster.</li>
<li><b>On hit:</b> Gain +1 damage and trigger 4% faster.</li></ul>
</td>
<td><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>38 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Cubert.png" class="image"><img alt="Cubert.png" src="/images/thumb/6/6a/Cubert.png/99px-Cubert.png?6e5743" decoding="async" loading="lazy" width="99" height="100" data-file-width="288" data-file-height="290" /></a></div></div>
</td>
<td><a href="/wiki/Cubert" title="Cubert">Cubert</a>
</td>
<td>
<ul><li><b><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> activates:</b> 35% chance to gain 1 <a href="/wiki/Regeneration" title="Regeneration"><img alt="Regeneration" src="/images/thumb/5/53/Regeneration.png/15px-Regeneration.png?7bd3bc" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li><b><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Diamond" src="/images/thumb/e/ea/Diamond.png/15px-Diamond.png?f49dcb" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> activates:</b> 30% chance to use 1 <a href="/wiki/Regeneration" title="Regeneration"><img alt="Regeneration" src="/images/thumb/5/53/Regeneration.png/15px-Regeneration.png?7bd3bc" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> to gain 1 <a href="/wiki/Empower" title="Empower"><img alt="Empower" src="/images/thumb/5/57/Empower.png/15px-Empower.png?31c0e4" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.</li></ul>
</td>
<td><a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Pet" title="Pet">Pet</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>8 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:CustomerCard.png" class="image"><img alt="CustomerCard.png" src="/images/thumb/5/56/CustomerCard.png/100px-CustomerCard.png?78d44c" decoding="async" loading="lazy" width="100" height="74" data-file-width="158" data-file-height="117" /></a></div></div>
</td>
<td><a href="/wiki/Customer_Card" title="Customer Card">Customer Card</a>
</td>
<td>
<p>Increases the rarity of 1 item in the shop every time it refreshes.
</p>
</td>
<td><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>4 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Dagger.png" class="image"><img alt="Dagger.png" src="/images/thumb/4/45/Dagger.png/50px-Dagger.png?9c578e" decoding="async" loading="lazy" width="50" height="100" data-file-width="160" data-file-height="320" /></a></div></div>
</td>
<td><a href="/wiki/Dagger" title="Dagger">Dagger</a>
</td>
<td>
<p><b>On <a href="/wiki/Stun" title="Stun">stun</a>:</b> Triggers extra attack.
</p>
</td>
<td><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>4 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:DancingDragon.png" class="image"><img alt="DancingDragon.png" src="/images/thumb/3/3d/DancingDragon.png/100px-DancingDragon.png?60afa4" decoding="async" loading="lazy" width="100" height="56" data-file-width="664" data-file-height="373" /></a></div></div>
</td>
<td><a href="/wiki/Dancing_Dragon" title="Dancing Dragon">Dancing Dragon</a>
</td>
<td>
<ul><li>You have a 2% chance to <a href="/wiki/Resist" title="Resist">resist</a> <a href="/wiki/Debuff" title="Debuff">debuffs</a> for each <a href="/wiki/Luck" title="Luck"><img alt="Luck" src="/images/thumb/f/f0/Luck.png/15px-Luck.png?9ec9f2" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li><b>Start of battle: </b> Gain 2 <a href="/wiki/Heat" title="Heat"><img alt="Heat" src="/images/thumb/c/c4/Heat.png/15px-Heat.png?4a5362" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a> and 2 <a href="/wiki/Luck" title="Luck"><img alt="Luck" src="/images/thumb/f/f0/Luck.png/15px-Luck.png?9ec9f2" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-item.</li>
<li>Deals +0.5 damage per <a href="/wiki/Heat" title="Heat"><img alt="Heat" src="/images/thumb/c/c4/Heat.png/15px-Heat.png?4a5362" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.</li></ul>
</td>
<td><a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Fire" title="Fire"><img alt="Fire" src="/images/thumb/0/0a/Icon_Fire.png/15px-Icon_Fire.png?54f5a7" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png?c8ee7a" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>9 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Darksaber.png" class="image"><img alt="Darksaber.png" src="/images/thumb/e/e8/Darksaber.png/23px-Darksaber.png?e8af7f" decoding="async" loading="lazy" width="23" height="100" data-file-width="149" data-file-height="633" /></a></div></div>
</td>
<td><a href="/wiki/Darksaber" title="Darksaber">Darksaber</a>
</td>
<td>
<ul><li><b>On attack:</b> Use 1 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> to inflict 1 <a href="/wiki/Blind" title="Blind"><img alt="Blind" src="/images/thumb/6/68/Blind.png/15px-Blind.png?f2b891" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li>Deals +0.5 damage for each <a href="/wiki/Debuff" title="Debuff">debuff</a> of your opponent.</li></ul>
</td>
<td><a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png?33e5e5" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>19 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:DivinePotion.png" class="image"><img alt="DivinePotion.png" src="/images/thumb/7/7e/DivinePotion.png/46px-DivinePotion.png?2d9d16" decoding="async" loading="lazy" width="46" height="100" data-file-width="143" data-file-height="307" /></a></div></div>
</td>
<td><a href="/wiki/Divine_Potion" title="Divine Potion">Divine Potion</a>
</td>
<td>
<p><b>You reached 10 <a href="/wiki/Debuff" title="Debuff">debuff</a>:</b> Consume this and cleanse 10 <a href="/wiki/Debuff" title="Debuff">debuffs</a>.
</p>
</td>
<td><a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Potion" title="Potion">Potion</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>7 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:DjinnLamp.png" class="image"><img alt="DjinnLamp.png" src="/images/thumb/8/80/DjinnLamp.png/100px-DjinnLamp.png?dfc77d" decoding="async" loading="lazy" width="100" height="50" data-file-width="322" data-file-height="160" /></a></div></div>
</td>
<td><a href="/wiki/Djinn_Lamp" title="Djinn Lamp">Djinn Lamp</a>
</td>
<td>
<ul><li><b>Every 1.6s:</b> Gain 1 <a href="/wiki/Luck" title="Luck"><img alt="Luck" src="/images/thumb/f/f0/Luck.png/15px-Luck.png?9ec9f2" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> or 1 <a href="/wiki/Spikes" title="Spikes"><img alt="Spikes" src="/images/thumb/9/9a/Spikes.png/15px-Spikes.png?ea75dd" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> or 1 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>, depending on what you have the least of.</li>
<li><b>Use </b>7 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>, 7 <a href="/wiki/Luck" title="Luck"><img alt="Luck" src="/images/thumb/f/f0/Luck.png/15px-Luck.png?9ec9f2" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>, 7 <a href="/wiki/Spikes" title="Spikes"><img alt="Spikes" src="/images/thumb/9/9a/Spikes.png/15px-Spikes.png?ea75dd" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>, 7 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> and 27 health: Give the <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Weapon" title="Weapon">Weapon</a> +27 damage (once).</li></ul>
</td>
<td><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>11 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Eggscalibur.png" class="image"><img alt="Eggscalibur.png" src="/images/thumb/7/76/Eggscalibur.png/100px-Eggscalibur.png?29e5d4" decoding="async" loading="lazy" width="100" height="71" data-file-width="448" data-file-height="317" /></a></div></div>
</td>
<td><a href="/wiki/Eggscalibur" title="Eggscalibur">Eggscalibur</a>
</td>
<td>
<ul><li><b>On attack:</b> Use 11 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>: Trigger all <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Food" title="Food">Food</a>.</li>
<li>Deals +1 damage for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Food" title="Food">Food</a>.</li></ul>
</td>
<td><a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>10 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:FalconBlade.png" class="image"><img alt="FalconBlade.png" src="/images/thumb/f/f2/FalconBlade.png/38px-FalconBlade.png?2b76d3" decoding="async" loading="lazy" width="38" height="100" data-file-width="190" data-file-height="497" /></a></div></div>
</td>
<td><a href="/wiki/Falcon_Blade" title="Falcon Blade">Falcon Blade</a>
</td>
<td>
<ul><li><b>Start of battle:</b> <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> items trigger 30% faster.</li>
<li>Attacks twice.</li></ul>
</td>
<td><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>19 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:FancyFencingRapier.png" class="image"><img alt="FancyFencingRapier.png" src="/images/thumb/d/d3/FancyFencingRapier.png/23px-FancyFencingRapier.png?81456b" decoding="async" loading="lazy" width="23" height="100" data-file-width="149" data-file-height="629" /></a></div></div>
</td>
<td><a href="/wiki/Fancy_Fencing_Rapier" title="Fancy Fencing Rapier">Fancy Fencing Rapier</a>
</td>
<td>
<ul><li><b>On hit:</b> Use 3 <a href="/wiki/Luck" title="Luck"><img alt="Luck" src="/images/thumb/f/f0/Luck.png/15px-Luck.png?9ec9f2" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> to gain 3 damage.</li>
<li><b>On miss:</b> Gain 3 <a href="/wiki/Luck" title="Luck"><img alt="Luck" src="/images/thumb/f/f0/Luck.png/15px-Luck.png?9ec9f2" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li></ul>
</td>
<td><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>12 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Fanfare.png" class="image"><img alt="Fanfare.png" src="/images/thumb/7/7f/Fanfare.png/100px-Fanfare.png?c192ef" decoding="async" loading="lazy" width="100" height="33" data-file-width="480" data-file-height="160" /></a></div></div>
</td>
<td><a href="/wiki/Fanfare" title="Fanfare">Fanfare</a>
</td>
<td>
<ul><li><b>Every 3s:</b> Randomly gain 1 <a href="/wiki/Empower" title="Empower"><img alt="Empower" src="/images/thumb/5/57/Empower.png/15px-Empower.png?31c0e4" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a> or gain 3 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> and remove 2 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> from opponent or remove 1 stamina from opponent.</li>
<li>Triggers 10% faster for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> item.</li></ul>
</td>
<td><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>7 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:FannyPack.png" class="image"><img alt="FannyPack.png" src="/images/thumb/f/f7/FannyPack.png/100px-FannyPack.png?c7f0bf" decoding="async" loading="lazy" width="100" height="63" data-file-width="459" data-file-height="291" /></a></div></div>
</td>
<td><a href="/wiki/Fanny_Pack" title="Fanny Pack">Fanny Pack</a>
</td>
<td>
<ul><li>Add 2 backpack slots.</li>
<li>Items inside trigger 10% faster.</li></ul>
</td>
<td><a href="/wiki/Bag" title="Bag">Bag</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>3 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:FlameBadge.png" class="image"><img alt="FlameBadge.png" src="/images/thumb/a/a7/FlameBadge.png/79px-FlameBadge.png?ee5b7f" decoding="async" loading="lazy" width="79" height="100" data-file-width="118" data-file-height="150" /></a></div></div>
</td>
<td><a href="/wiki/Flame_Badge" title="Flame Badge">Flame Badge</a>
</td>
<td>
<ul><li><a href="/wiki/Pyromancer" title="Pyromancer">Pyromancer</a> items are offered in the shop.</li>
<li><b>Shop entered:</b> 65% chance to spend 1 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /> and generate a <a href="/wiki/Flame" title="Flame">Flame</a>.</li>
<li><b>Start of battle:</b> Gain 6 <a href="/wiki/Heat" title="Heat"><img alt="Heat" src="/images/thumb/c/c4/Heat.png/15px-Heat.png?4a5362" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.</li></ul>
</td>
<td><a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Flute.png" class="image"><img alt="Flute.png" src="/images/thumb/d/db/Flute.png/100px-Flute.png?2a0b71" decoding="async" loading="lazy" width="100" height="33" data-file-width="480" data-file-height="160" /></a></div></div>
</td>
<td><a href="/wiki/Flute" title="Flute">Flute</a>
</td>
<td>
<ul><li><b>Every 4.7s:</b> Randomly gain 14 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> or 2 stamina or 2 <a href="/wiki/Luck" title="Luck"><img alt="Luck" src="/images/thumb/f/f0/Luck.png/15px-Luck.png?9ec9f2" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li>Triggers 10% faster for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> item.</li></ul>
</td>
<td><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>6 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Frostbite.png" class="image"><img alt="Frostbite.png" src="/images/thumb/e/ee/Frostbite.png/30px-Frostbite.png?b60bb0" decoding="async" loading="lazy" width="30" height="100" data-file-width="152" data-file-height="497" /></a></div></div>
</td>
<td><a href="/wiki/Frostbite" title="Frostbite">Frostbite</a>
</td>
<td>
<ul><li><b>On hit:</b> 60% chance to inflict 1 <a href="/wiki/Cold" title="Cold"><img alt="Cold" src="/images/thumb/6/6b/Cold.png/15px-Cold.png?f89f07" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.</li>
<li><b>Opponent reaches 30 <a href="/wiki/Cold" title="Cold"><img alt="Cold" src="/images/thumb/6/6b/Cold.png/15px-Cold.png?f89f07" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>:</b> Gain 5 <a href="/wiki/Vampirism" title="Vampirism"><img alt="Vampirism" src="/images/thumb/3/33/Vampirism.png/15px-Vampirism.png?823c3c" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> (once).</li>
<li>Deals +1 damage per <a href="/wiki/Vampirism" title="Vampirism"><img alt="Vampirism" src="/images/thumb/3/33/Vampirism.png/15px-Vampirism.png?823c3c" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> and +0.4 per <a href="/wiki/Cold" title="Cold"><img alt="Cold" src="/images/thumb/6/6b/Cold.png/15px-Cold.png?f89f07" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a> of your opponent.</li></ul>
</td>
<td><a href="/wiki/Ice" title="Ice"><img alt="Ice" src="/images/thumb/5/52/Icon_Ice.png/15px-Icon_Ice.png?61b1d9" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png?8cee0c" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>11 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:FrozenBuckler.png" class="image"><img alt="FrozenBuckler.png" src="/images/thumb/a/ad/FrozenBuckler.png/97px-FrozenBuckler.png?b6596e" decoding="async" loading="lazy" width="97" height="100" data-file-width="305" data-file-height="314" /></a></div></div>
</td>
<td><a href="/wiki/Frozen_Buckler" title="Frozen Buckler">Frozen Buckler</a>
</td>
<td>
<p><b>On attacked (<a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>):</b> 30% chance to prevent 7 damage, remove 0.5 stamina from opponent and inflict 1 <a href="/wiki/Cold" title="Cold"><img alt="Cold" src="/images/thumb/6/6b/Cold.png/15px-Cold.png?f89f07" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a> (up to 10).
</p>
</td>
<td><a href="/wiki/Ice" title="Ice"><img alt="Ice" src="/images/thumb/5/52/Icon_Ice.png/15px-Icon_Ice.png?61b1d9" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Shield" title="Shield">Shield</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>8 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Garlic.png" class="image"><img alt="Garlic.png" src="/images/thumb/c/cc/Garlic.png/45px-Garlic.png?1d9604" decoding="async" loading="lazy" width="45" height="100" data-file-width="142" data-file-height="311" /></a></div></div>
</td>
<td><a href="/wiki/Garlic" title="Garlic">Garlic</a>
</td>
<td>
<p><b>Every 4s:</b> Gain 3 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>. 30% chance to remove 1 <a href="/wiki/Vampirism" title="Vampirism"><img alt="Vampirism" src="/images/thumb/3/33/Vampirism.png/15px-Vampirism.png?823c3c" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> from your opponent.
</p>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Food" title="Food">Food</a>
</td>
<td data-sort-value="1"><a href="/wiki/Common" title="Common">Common</a>
</td>
<td>2 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:GingerbreadJerry.png" class="image"><img alt="GingerbreadJerry.png" src="/images/thumb/8/8c/GingerbreadJerry.png/55px-GingerbreadJerry.png?90994b" decoding="async" loading="lazy" width="55" height="100" data-file-width="166" data-file-height="303" /></a></div></div>
</td>
<td><a href="/wiki/Gingerbread_Jerry" title="Gingerbread Jerry">Gingerbread Jerry</a>
</td>
<td>
<ul><li><b>Start of battle:</b> Gain 40 maximum health.</li>
<li><b>Every 3s:</b> Use 1 <a href="/wiki/Luck" title="Luck"><img alt="Luck" src="/images/thumb/f/f0/Luck.png/15px-Luck.png?9ec9f2" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>, 1 <a href="/wiki/Heat" title="Heat"><img alt="Heat" src="/images/thumb/c/c4/Heat.png/15px-Heat.png?4a5362" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a> and 1 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>: Gain 1 <a href="/wiki/Empower" title="Empower"><img alt="Empower" src="/images/thumb/5/57/Empower.png/15px-Empower.png?31c0e4" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>, 3 <a href="/wiki/Regeneration" title="Regeneration"><img alt="Regeneration" src="/images/thumb/5/53/Regeneration.png/15px-Regeneration.png?7bd3bc" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> and 20 maximum health.</li></ul>
</td>
<td><a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Food" title="Food">Food</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>8 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:GirlPower.png" class="image"><img alt="GirlPower.png" src="/images/thumb/d/d6/GirlPower.png/90px-GirlPower.png?3a35f7" decoding="async" loading="lazy" width="90" height="100" data-file-width="133" data-file-height="147" /></a></div></div>
</td>
<td><a href="/wiki/Girl_Power" title="Girl Power">Girl Power</a>
</td>
<td>
<ul><li><b>Every 3.5s:</b> Gain 2 <a href="/wiki/Regeneration" title="Regeneration"><img alt="Regeneration" src="/images/thumb/5/53/Regeneration.png/15px-Regeneration.png?7bd3bc" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> or 2 <a href="/wiki/Empower" title="Empower"><img alt="Empower" src="/images/thumb/5/57/Empower.png/15px-Empower.png?31c0e4" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>, depending on which you have less of.</li>
<li>Triggers 20% faster for each distinct <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> Class item.</li></ul>
</td>
<td><a href="/wiki/Skill" title="Skill">Skill</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:GlovesofHaste.png" class="image"><img alt="GlovesofHaste.png" src="/images/thumb/7/74/GlovesofHaste.png/100px-GlovesofHaste.png?a0c2ec" decoding="async" loading="lazy" width="100" height="50" data-file-width="320" data-file-height="160" /></a></div></div>
</td>
<td><a href="/wiki/Gloves_of_Haste" title="Gloves of Haste">Gloves of Haste</a>
</td>
<td>
<p><b>Start of battle:</b> <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> items trigger 20% faster.
</p>
</td>
<td><a href="/wiki/Gloves" title="Gloves">Gloves</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>4 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:GlovesofPower.png" class="image"><img alt="GlovesofPower.png" src="/images/thumb/1/19/GlovesofPower.png/100px-GlovesofPower.png?53c6d5" decoding="async" loading="lazy" width="100" height="46" data-file-width="328" data-file-height="152" /></a></div></div>
</td>
<td><a href="/wiki/Gloves_of_Power" title="Gloves of Power">Gloves of Power</a>
</td>
<td>
<ul><li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Weapon" title="Weapon">Weapons</a> deal +20% damage but attack 10% slower.</li>
<li><b><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Weapon" title="Weapon">Weapon</a> hits:</b> gain 7 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li></ul>
</td>
<td><a href="/wiki/Gloves" title="Gloves">Gloves</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>10 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:GlowingCrown.png" class="image"><img alt="GlowingCrown.png" src="/images/thumb/5/55/GlowingCrown.png/100px-GlowingCrown.png?45bec0" decoding="async" loading="lazy" width="100" height="52" data-file-width="292" data-file-height="151" /></a></div></div>
</td>
<td><a href="/wiki/Glowing_Crown" title="Glowing Crown">Glowing Crown</a>
</td>
<td>
<ul><li><b>Every 2.4s:</b> Cleanse 1 <a href="/wiki/Blind" title="Blind"><img alt="Blind" src="/images/thumb/6/68/Blind.png/15px-Blind.png?f2b891" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> and <a href="/wiki/Heal" title="Heal">heal</a> for 5.</li>
<li><b>Use 10</b> <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a><b>:</b> Become <a href="/wiki/Invulnerability" title="Invulnerability">invulnerable</a> for 2s (once).</li></ul>
</td>
<td><a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Helmet" title="Helmet">Helmet</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>12 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Goobert.png" class="image"><img alt="Goobert.png" src="/images/thumb/c/c3/Goobert.png/97px-Goobert.png?c6fe15" decoding="async" loading="lazy" width="97" height="100" data-file-width="301" data-file-height="309" /></a></div></div>
</td>
<td><a href="/wiki/Goobert" title="Goobert">Goobert</a>
</td>
<td>
<p><b>5 <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> item activations:</b> <a href="/wiki/Heal" title="Heal">Heal</a> for 9.
</p>
</td>
<td><a href="/wiki/Pet" title="Pet">Pet</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>6 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Goobling.png" class="image"><img alt="Goobling.png" src="/images/thumb/8/8b/Goobling.png/100px-Goobling.png?259c4d" decoding="async" loading="lazy" width="100" height="95" data-file-width="152" data-file-height="144" /></a></div></div>
</td>
<td><a href="/wiki/Goobling" title="Goobling">Goobling</a>
</td>
<td>
<p><b>3 <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> item activations:</b> <a href="/wiki/Heal" title="Heal">Heal</a> for 4.
</p>
</td>
<td><a href="/wiki/Pet" title="Pet">Pet</a>
</td>
<td data-sort-value="1"><a href="/wiki/Common" title="Common">Common</a>
</td>
<td>2 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Hammer.png" class="image"><img alt="Hammer.png" src="/images/thumb/8/81/Hammer.png/100px-Hammer.png?38d36f" decoding="async" loading="lazy" width="100" height="100" data-file-width="480" data-file-height="480" /></a></div></div>
</td>
<td><a href="/wiki/Hammer" title="Hammer">Hammer</a>
</td>
<td>
<p><b>On hit:</b> 45% chance to <a href="/wiki/Stun" title="Stun">stun</a> your opponent for 0.5s.
</p>
</td>
<td><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>8 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Hardwood.png" class="image"><img alt="Hardwood.png" src="/images/thumb/e/ed/Hardwood.png/68px-Hardwood.png?e80c98" decoding="async" loading="lazy" width="68" height="100" data-file-width="113" data-file-height="165" /></a></div></div>
</td>
<td><a href="/wiki/Hardwood" title="Hardwood">Hardwood</a>
</td>
<td>
<ul><li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Common" title="Common">Common</a> <a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a> <a href="/wiki/Weapon" title="Weapon">Weapons</a> deal +150% damage.</li>
<li><b>Start of battle:</b> Gain 10 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Common" title="Common">Common</a> item.</li></ul>
</td>
<td><a href="/wiki/Skill" title="Skill">Skill</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:HealingHerbs.png" class="image"><img alt="HealingHerbs.png" src="/images/thumb/c/c4/HealingHerbs.png/100px-HealingHerbs.png?408827" decoding="async" loading="lazy" width="100" height="100" data-file-width="160" data-file-height="160" /></a></div></div>
</td>
<td><a href="/wiki/Healing_Herbs" title="Healing Herbs">Healing Herbs</a>
</td>
<td>
<p><b>Start of battle:</b> Gain 2 <a href="/wiki/Regeneration" title="Regeneration"><img alt="Regeneration" src="/images/thumb/5/53/Regeneration.png/15px-Regeneration.png?7bd3bc" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.
</p>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="1"><a href="/wiki/Common" title="Common">Common</a>
</td>
<td>4 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:HealthPotion.png" class="image"><img alt="HealthPotion.png" src="/images/thumb/d/d2/HealthPotion.png/46px-HealthPotion.png?4485b0" decoding="async" loading="lazy" width="46" height="100" data-file-width="101" data-file-height="217" /></a></div></div>
</td>
<td><a href="/wiki/Health_Potion" title="Health Potion">Health Potion</a>
</td>
<td>
<p><b>Health drops below 50%:</b> Consume this and <a href="/wiki/Heal" title="Heal">heal</a> for 12 and cleanse 4 <a href="/wiki/Poison" title="Poison"><img alt="Poison" src="/images/thumb/0/05/Poison.png/15px-Poison.png?984131" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.
</p>
</td>
<td><a href="/wiki/Potion" title="Potion">Potion</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>4 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:HeartContainer.png" class="image"><img alt="HeartContainer.png" src="/images/thumb/1/10/HeartContainer.png/98px-HeartContainer.png?4de6cb" decoding="async" loading="lazy" width="98" height="100" data-file-width="291" data-file-height="298" /></a></div></div>
</td>
<td><a href="/wiki/Heart_Container" title="Heart Container">Heart Container</a>
</td>
<td>
<ul><li><b>Every 3s:</b> Gain 1 <a href="/wiki/Regeneration" title="Regeneration"><img alt="Regeneration" src="/images/thumb/5/53/Regeneration.png/15px-Regeneration.png?7bd3bc" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li><b>Use 7 <a href="/wiki/Regeneration" title="Regeneration"><img alt="Regeneration" src="/images/thumb/5/53/Regeneration.png/15px-Regeneration.png?7bd3bc" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>:</b> Gain 100 maximum health, 2 <a href="/wiki/Empower" title="Empower"><img alt="Empower" src="/images/thumb/5/57/Empower.png/15px-Empower.png?31c0e4" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a> and your <a href="/wiki/Heal" title="Heal">healing</a> is increased by 15% (once).</li></ul>
</td>
<td><a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>12 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:HeartofDarkness.png" class="image"><img alt="HeartofDarkness.png" src="/images/thumb/f/fd/HeartofDarkness.png/100px-HeartofDarkness.png?933cef" decoding="async" loading="lazy" width="100" height="98" data-file-width="295" data-file-height="289" /></a></div></div>
</td>
<td><a href="/wiki/Heart_of_Darkness" title="Heart of Darkness">Heart of Darkness</a>
</td>
<td>
<ul><li><b>Every 4s:</b> Steal 2 <a href="/wiki/Buff" title="Buff">buffs</a>, prioritizing <a href="/wiki/Regeneration" title="Regeneration"><img alt="Regeneration" src="/images/thumb/5/53/Regeneration.png/15px-Regeneration.png?7bd3bc" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li>Triggers 20% faster for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png?33e5e5" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-item.</li>
<li><b>Use 7 <a href="/wiki/Regeneration" title="Regeneration"><img alt="Regeneration" src="/images/thumb/5/53/Regeneration.png/15px-Regeneration.png?7bd3bc" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>:</b> Gain 100 maximum health, 4 <a href="/wiki/Empower" title="Empower"><img alt="Empower" src="/images/thumb/5/57/Empower.png/15px-Empower.png?31c0e4" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a> and your opponent's <a href="/wiki/Heal" title="Heal">healing</a> is reduced by 40% (once).</li></ul>
</td>
<td><a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png?33e5e5" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>19 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:HeavyDrinking.png" class="image"><img alt="HeavyDrinking.png" src="/images/thumb/9/96/HeavyDrinking.png/100px-HeavyDrinking.png?b9d2af" decoding="async" loading="lazy" width="100" height="85" data-file-width="155" data-file-height="131" /></a></div></div>
</td>
<td><a href="/wiki/Heavy_Drinking" title="Heavy Drinking">Heavy Drinking</a>
</td>
<td>
<ul><li><b>Every 10s:</b> Trigger the effect of a random <a href="/wiki/Potion" title="Potion">Potion</a>.</li>
<li>Triggers 60% faster for each distinct <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Potion" title="Potion">Potion</a>.</li></ul>
</td>
<td><a href="/wiki/Skill" title="Skill">Skill</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:HeroicPotion.png" class="image"><img alt="HeroicPotion.png" src="/images/thumb/7/7b/HeroicPotion.png/51px-HeroicPotion.png?8acad0" decoding="async" loading="lazy" width="51" height="100" data-file-width="108" data-file-height="212" /></a></div></div>
</td>
<td><a href="/wiki/Heroic_Potion" title="Heroic Potion">Heroic Potion</a>
</td>
<td>
<p><b>Out of stamina:</b> Consume this and regenerate 2 stamina and gain 1 <a href="/wiki/Empower" title="Empower"><img alt="Empower" src="/images/thumb/5/57/Empower.png/15px-Empower.png?31c0e4" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.
</p>
</td>
<td><a href="/wiki/Potion" title="Potion">Potion</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>6 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:HeroLongsword.png" class="image"><img alt="HeroLongsword.png" src="/images/thumb/f/f2/HeroLongsword.png/39px-HeroLongsword.png?7611b5" decoding="async" loading="lazy" width="39" height="100" data-file-width="191" data-file-height="486" /></a></div></div>
</td>
<td><a href="/wiki/Hero_Longsword" title="Hero Longsword">Hero Longsword</a>
</td>
<td>
<p><b>Start of battle:</b> <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Weapon" title="Weapon">Weapons</a> gain 4 damage.
</p>
</td>
<td><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>19 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:HeroSword.png" class="image"><img alt="HeroSword.png" src="/images/thumb/9/9c/HeroSword.png/50px-HeroSword.png?d63678" decoding="async" loading="lazy" width="50" height="100" data-file-width="160" data-file-height="320" /></a></div></div>
</td>
<td><a href="/wiki/Hero_Sword" title="Hero Sword">Hero Sword</a>
</td>
<td>
<p><b>Start of battle:</b> <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Weapon" title="Weapon">Weapons</a> gain 1 damage.
</p>
</td>
<td><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>7 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:HolyArmor.png" class="image"><img alt="HolyArmor.png" src="/images/thumb/1/1b/HolyArmor.png/74px-HolyArmor.png?d4ea3c" decoding="async" loading="lazy" width="74" height="100" data-file-width="363" data-file-height="487" /></a></div></div>
</td>
<td><a href="/wiki/Holy_Armor" title="Holy Armor">Holy Armor</a>
</td>
<td>
<ul><li><b>Start of battle:</b> Gain 65 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>. Gain 2 <a href="/wiki/Regeneration" title="Regeneration"><img alt="Regeneration" src="/images/thumb/5/53/Regeneration.png/15px-Regeneration.png?7bd3bc" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-item.</li>
<li><b>Every 2.6s:</b> Cleanse 2 <a href="/wiki/Poison" title="Poison"><img alt="Poison" src="/images/thumb/0/05/Poison.png/15px-Poison.png?984131" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li></ul>
</td>
<td><a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Armor" title="Armor">Armor</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>13 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:HolySpear.png" class="image"><img alt="HolySpear.png" src="/images/thumb/a/a1/HolySpear.png/20px-HolySpear.png?acefa5" decoding="async" loading="lazy" width="20" height="100" data-file-width="132" data-file-height="641" /></a></div></div>
</td>
<td><a href="/wiki/Holy_Spear" title="Holy Spear">Holy Spear</a>
</td>
<td>
<ul><li><b>On hit:</b> Destroy 10 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> and cleanse 1 <a href="/wiki/Debuff" title="Debuff">debuff</a> for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> free slot or <a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-item in front of it.</li>
<li><b>Use 10 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>:</b> Become <a href="/wiki/Invulnerability" title="Invulnerability">invulnerable</a> and attack 100% faster for 3s (once).</li></ul>
</td>
<td><a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>18 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:HungryBlade.png" class="image"><img alt="HungryBlade.png" src="/images/thumb/c/c8/HungryBlade.png/33px-HungryBlade.png?a58c63" decoding="async" loading="lazy" width="33" height="100" data-file-width="160" data-file-height="480" /></a></div></div>
</td>
<td><a href="/wiki/Hungry_Blade" title="Hungry Blade">Hungry Blade</a>
</td>
<td>
<ul><li><b>Start of battle:</b> Gain 1 <a href="/wiki/Vampirism" title="Vampirism"><img alt="Vampirism" src="/images/thumb/3/33/Vampirism.png/15px-Vampirism.png?823c3c" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li><b>On hit:</b> Use 1 <a href="/wiki/Regeneration" title="Regeneration"><img alt="Regeneration" src="/images/thumb/5/53/Regeneration.png/15px-Regeneration.png?7bd3bc" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> to gain 1 <a href="/wiki/Vampirism" title="Vampirism"><img alt="Vampirism" src="/images/thumb/3/33/Vampirism.png/15px-Vampirism.png?823c3c" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li>Deals +1 maximum damage per <a href="/wiki/Vampirism" title="Vampirism"><img alt="Vampirism" src="/images/thumb/3/33/Vampirism.png/15px-Vampirism.png?823c3c" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li></ul>
</td>
<td><a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png?8cee0c" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>7 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:IceArmor.png" class="image"><img alt="IceArmor.png" src="/images/thumb/a/ad/IceArmor.png/74px-IceArmor.png?2534e0" decoding="async" loading="lazy" width="74" height="100" data-file-width="369" data-file-height="499" /></a></div></div>
</td>
<td><a href="/wiki/Ice_Armor" title="Ice Armor">Ice Armor</a>
</td>
<td>
<ul><li><b>Start of battle:</b> Gain 45 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> and inflict 4 <a href="/wiki/Cold" title="Cold"><img alt="Cold" src="/images/thumb/6/6b/Cold.png/15px-Cold.png?f89f07" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.</li>
<li><b>Every 5s:</b> Use 1 <a href="/wiki/Heat" title="Heat"><img alt="Heat" src="/images/thumb/c/c4/Heat.png/15px-Heat.png?4a5362" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a> to inflict 2 <a href="/wiki/Cold" title="Cold"><img alt="Cold" src="/images/thumb/6/6b/Cold.png/15px-Cold.png?f89f07" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a> and gain 10 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li></ul>
</td>
<td><a href="/wiki/Ice" title="Ice"><img alt="Ice" src="/images/thumb/5/52/Icon_Ice.png/15px-Icon_Ice.png?61b1d9" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Armor" title="Armor">Armor</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>11 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:ImpracticallyLargeGreatsword.png" class="image"><img alt="ImpracticallyLargeGreatsword.png" src="/images/thumb/5/5e/ImpracticallyLargeGreatsword.png/47px-ImpracticallyLargeGreatsword.png?a3cb92" decoding="async" loading="lazy" width="47" height="100" data-file-width="149" data-file-height="312" /></a></div></div>
</td>
<td><a href="/wiki/Impractically_Large_Greatsword" title="Impractically Large Greatsword">Impractically Large Greatsword</a>
</td>
<td>
<p>While you have at least 5 <a href="/wiki/Empower" title="Empower"><img alt="Empower" src="/images/thumb/5/57/Empower.png/15px-Empower.png?31c0e4" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>, decrease stamina usage to 2 and cooldown to 2s.
</p>
</td>
<td><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>14 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:InvestmentOpportunity.png" class="image"><img alt="InvestmentOpportunity.png" src="/images/thumb/a/a7/InvestmentOpportunity.png/100px-InvestmentOpportunity.png?edd557" decoding="async" loading="lazy" width="100" height="99" data-file-width="144" data-file-height="143" /></a></div></div>
</td>
<td><a href="/wiki/Investment_Opportunity" title="Investment Opportunity">Investment Opportunity</a>
</td>
<td>
<ul><li><b>Shop entered:</b> Gain 1 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />.</li>
<li><b><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> item used <a href="/wiki/Buff" title="Buff">buff</a>:</b> Gain 2 maximum health.</li></ul>
</td>
<td><a href="/wiki/Skill" title="Skill">Skill</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:ItsSlimeTime.png" class="image"><img alt="ItsSlimeTime.png" src="/images/thumb/c/c8/ItsSlimeTime.png/100px-ItsSlimeTime.png?67eb91" decoding="async" loading="lazy" width="100" height="88" data-file-width="147" data-file-height="130" /></a></div></div>
</td>
<td><a href="/wiki/It%27s_Slime_Time!" title="It&#39;s Slime Time!">It&#39;s Slime Time!</a>
</td>
<td>
<ul><li><a href="/wiki/Goobling" title="Goobling">Gooblings</a> are offered in the shop.</li>
<li><b>On buy:</b> Generate a <a href="/wiki/Goobling" title="Goobling">Goobling</a>.</li>
<li><b>Every 3s:</b> Advance all <a href="/wiki/Goobert" title="Goobert">Gooberts</a> and <a href="/wiki/Goobling" title="Goobling">Gooblings</a> by 1 activation.</li></ul>
</td>
<td><a href="/wiki/Skill" title="Skill">Skill</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:JustStats.png" class="image"><img alt="JustStats.png" src="/images/thumb/6/67/JustStats.png/100px-JustStats.png?87bd66" decoding="async" loading="lazy" width="100" height="99" data-file-width="156" data-file-height="154" /></a></div></div>
</td>
<td><a href="/wiki/Just_Stats" title="Just Stats">Just Stats</a>
</td>
<td>
<ul><li><b>Start of battle:</b> Gain 15% maximum health and 10% base stamina regeneration.</li>
<li>Always offered in round 4.</li></ul>
</td>
<td><a href="/wiki/Skill" title="Skill">Skill</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:JynxTorquilla.png" class="image"><img alt="JynxTorquilla.png" src="/images/thumb/9/9a/JynxTorquilla.png/100px-JynxTorquilla.png?e8b697" decoding="async" loading="lazy" width="100" height="82" data-file-width="312" data-file-height="257" /></a></div></div>
</td>
<td><a href="/wiki/Jynx_torquilla" title="Jynx torquilla">Jynx torquilla</a>
</td>
<td>
<p><b>Every 3s:</b> <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> items trigger 5% faster (up to 40%). Remove 1 <a href="/wiki/Luck" title="Luck"><img alt="Luck" src="/images/thumb/f/f0/Luck.png/15px-Luck.png?9ec9f2" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> from your opponent.
</p>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Pet" title="Pet">Pet</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>8 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Katana.png" class="image"><img alt="Katana.png" src="/images/thumb/e/e9/Katana.png/14px-Katana.png?9183cc" decoding="async" loading="lazy" width="14" height="100" data-file-width="91" data-file-height="647" /></a></div></div>
</td>
<td><a href="/wiki/Katana" title="Katana">Katana</a>
</td>
<td>
<p><b>On hit:</b> Remove 1 damage gained in battle from all opponent <a href="/wiki/Weapon" title="Weapon">Weapons</a> and gain 1 damage. If your opponent has at least 20 <a href="/wiki/Buff" title="Buff">buffs</a>, remove 2 of the type they have the most of.
</p>
</td>
<td><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>14 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:KingCrown.png" class="image"><img alt="KingCrown.png" src="/images/thumb/1/1a/KingCrown.png/96px-KingCrown.png?6bd401" decoding="async" loading="lazy" width="96" height="100" data-file-width="321" data-file-height="334" /></a></div></div>
</td>
<td><a href="/wiki/King_Crown" title="King Crown">King Crown</a>
</td>
<td>
<ul><li><b>Every 2.4s:</b> <a href="/wiki/Heal" title="Heal">Heal</a> for 5 and protect 1 <a href="/wiki/Buff" title="Buff">buff</a> from removal.</li>
<li><b>Use 10 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>:</b> Become <a href="/wiki/Invulnerability" title="Invulnerability">invulnerable</a> for 2.5s (once).</li>
<li>Effects of <a href="/wiki/Gemstone" title="Gemstone">Gemstones</a> socketed in this are increased by 50%.</li></ul>
</td>
<td><a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>17 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:KingGoobert.png" class="image"><img alt="KingGoobert.png" src="/images/thumb/f/f0/KingGoobert.png/67px-KingGoobert.png?1300ab" decoding="async" loading="lazy" width="67" height="100" data-file-width="323" data-file-height="484" /></a></div></div>
</td>
<td><a href="/wiki/King_Goobert" title="King Goobert">King Goobert</a>
</td>
<td>
<ul><li><b>6 <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> item activations:</b> Heal for 35, protect 3 <a href="/wiki/Buff" title="Buff">buffs</a> from removal and use 4 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> to become <a href="/wiki/Invulnerability" title="Invulnerability">invulnerable</a> for 1.5s (up to 3 times).</li>
<li>Effects of <a href="/wiki/Gemstone" title="Gemstone">Gemstones</a> socketed in this are increased by 50%.</li></ul>
</td>
<td><a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Pet" title="Pet">Pet</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>23 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:LeafBadge.png" class="image"><img alt="LeafBadge.png" src="/images/thumb/d/d3/LeafBadge.png/100px-LeafBadge.png?7545d6" decoding="async" loading="lazy" width="100" height="100" data-file-width="132" data-file-height="132" /></a></div></div>
</td>
<td><a href="/wiki/Leaf_Badge" title="Leaf Badge">Leaf Badge</a>
</td>
<td>
<ul><li><a href="/wiki/Ranger" title="Ranger">Ranger</a> items are offered in the shop.</li>
<li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> items gain 2% <a href="/wiki/Critical_hits" title="Critical hits">critical</a> hit chance for each <a href="/wiki/Luck" title="Luck"><img alt="Luck" src="/images/thumb/f/f0/Luck.png/15px-Luck.png?9ec9f2" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li><b>Every 2.2s:</b> Gain 1 <a href="/wiki/Luck" title="Luck"><img alt="Luck" src="/images/thumb/f/f0/Luck.png/15px-Luck.png?9ec9f2" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li></ul>
</td>
<td><a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:LeatherArmor.png" class="image"><img alt="LeatherArmor.png" src="/images/thumb/5/5c/LeatherArmor.png/100px-LeatherArmor.png?63e8dc" decoding="async" loading="lazy" width="100" height="100" data-file-width="480" data-file-height="480" /></a></div></div>
</td>
<td><a href="/wiki/Leather_Armor" title="Leather Armor">Leather Armor</a>
</td>
<td>
<p><b>Start of battle:</b> Gain 45 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>. <a href="/wiki/Resist" title="Resist">Resist</a> 3 <a href="/wiki/Debuff" title="Debuff">debuffs</a>.
</p>
</td>
<td><a href="/wiki/Armor" title="Armor">Armor</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>7 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:LeatherBag.png" class="image"><img alt="LeatherBag.png" src="/images/thumb/c/c0/LeatherBag.png/100px-LeatherBag.png?e378e9" decoding="async" loading="lazy" width="100" height="93" data-file-width="490" data-file-height="458" /></a></div></div>
</td>
<td><a href="/wiki/Leather_Bag" title="Leather Bag">Leather Bag</a>
</td>
<td>
<p>Add 4 backpack slots.
</p>
</td>
<td><a href="/wiki/Bag" title="Bag">Bag</a>
</td>
<td data-sort-value="1"><a href="/wiki/Common" title="Common">Common</a>
</td>
<td>4 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:LeatherBoots.png" class="image"><img alt="LeatherBoots.png" src="/images/thumb/5/57/LeatherBoots.png/69px-LeatherBoots.png?e51b16" decoding="async" loading="lazy" width="69" height="100" data-file-width="175" data-file-height="253" /></a></div></div>
</td>
<td><a href="/wiki/Leather_Boots" title="Leather Boots">Leather Boots</a>
</td>
<td>
<p><b>Health drops below 70%:</b> Gain 1 <a href="/wiki/Luck" title="Luck"><img alt="Luck" src="/images/thumb/f/f0/Luck.png/15px-Luck.png?9ec9f2" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>, 1 <a href="/wiki/Empower" title="Empower"><img alt="Empower" src="/images/thumb/5/57/Empower.png/15px-Empower.png?31c0e4" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a> and 15 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> (once).
</p>
</td>
<td><a href="/wiki/Shoes" title="Shoes">Shoes</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>6 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:LightGoobert.png" class="image"><img alt="LightGoobert.png" src="/images/thumb/c/c2/LightGoobert.png/97px-LightGoobert.png?254e91" decoding="async" loading="lazy" width="97" height="100" data-file-width="301" data-file-height="309" /></a></div></div>
</td>
<td><a href="/wiki/Light_Goobert" title="Light Goobert">Light Goobert</a>
</td>
<td>
<p><b>6 <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> item activations:</b> <a href="/wiki/Heal" title="Heal">Heal</a> for 20  and inflict 6 <a href="/wiki/Blind" title="Blind"><img alt="Blind" src="/images/thumb/6/68/Blind.png/15px-Blind.png?f2b891" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> for 3s.
</p>
</td>
<td><a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Pet" title="Pet">Pet</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>18 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Lightsaber.png" class="image"><img alt="Lightsaber.png" src="/images/thumb/4/49/Lightsaber.png/23px-Lightsaber.png?7a3baf" decoding="async" loading="lazy" width="23" height="100" data-file-width="148" data-file-height="621" /></a></div></div>
</td>
<td><a href="/wiki/Lightsaber" title="Lightsaber">Lightsaber</a>
</td>
<td>
<ul><li><b>Use 3 <a href="/wiki/Regeneration" title="Regeneration"><img alt="Regeneration" src="/images/thumb/5/53/Regeneration.png/15px-Regeneration.png?7bd3bc" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>:</b> Inflict 8 <a href="/wiki/Blind" title="Blind"><img alt="Blind" src="/images/thumb/6/68/Blind.png/15px-Blind.png?f2b891" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> for 6s (unstackable).</li>
<li>Deals <b>+1</b> damage for each <a href="/wiki/Blind" title="Blind"><img alt="Blind" src="/images/thumb/6/68/Blind.png/15px-Blind.png?f2b891" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> of your opponent.</li></ul>
</td>
<td><a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>12 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:LumpofCoal.png" class="image"><img alt="LumpofCoal.png" src="/images/5/58/LumpofCoal.png?ce71b4" decoding="async" loading="lazy" width="91" height="100" data-file-width="73" data-file-height="80" /></a></div></div>
</td>
<td><a href="/wiki/Lump_of_Coal" title="Lump of Coal">Lump of Coal</a>
</td>
<td>
<div style="font-size:100%; text-align:center;"><font color="gray">Weapon sockets:</font></div>
<p><b>On attack:</b> 70% chance to deal +1 damage.
</p>
<br /><div style="font-size:100%; text-align:center;"><font color="gray">Armor &amp; other sockets:</font></div>
<p><b>Start of battle:</b> Gain 8 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.
<a href="/wiki/Resist" title="Resist">Resist</a> 1 <a href="/wiki/Debuff" title="Debuff">debuff</a>.
</p>
<br /><div style="font-size:100%; text-align:center;"><font color="gray">Backpack:</font></div>
<p><b>After 3s:</b> Gain a random <a href="/wiki/Buff" title="Buff">buff</a>, inflict a random <a href="/wiki/Debuff" title="Debuff">debuff</a>.
</p>
</td>
<td><a href="/wiki/Gemstone" title="Gemstone">Gemstone</a>
</td>
<td data-sort-value="1"><a href="/wiki/Common" title="Common">Common</a>
</td>
<td>2 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:MagicStaff.png" class="image"><img alt="MagicStaff.png" src="/images/thumb/7/7b/MagicStaff.png/25px-MagicStaff.png?382f05" decoding="async" loading="lazy" width="25" height="100" data-file-width="160" data-file-height="632" /></a></div></div>
</td>
<td><a href="/wiki/Magic_Staff" title="Magic Staff">Magic Staff</a>
</td>
<td>
<p><b>On attack:</b> Use 3 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> to deal +6 damage and gain 2 damage.
</p>
</td>
<td><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png?c8ee7a" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>10 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:MagicTorch.png" class="image"><img alt="MagicTorch.png" src="/images/thumb/0/01/MagicTorch.png/43px-MagicTorch.png?301fb7" decoding="async" loading="lazy" width="43" height="100" data-file-width="140" data-file-height="324" /></a></div></div>
</td>
<td><a href="/wiki/Magic_Torch" title="Magic Torch">Magic Torch</a>
</td>
<td>
<p><b>On hit:</b> Use 1 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>: This and <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Weapon" title="Weapon">Weapons</a> gain 1 damage.
</p>
</td>
<td><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Fire" title="Fire"><img alt="Fire" src="/images/thumb/0/0a/Icon_Fire.png/15px-Icon_Fire.png?54f5a7" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>11 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:ManaMastery.png" class="image"><img alt="ManaMastery.png" src="/images/thumb/0/00/ManaMastery.png/100px-ManaMastery.png?f2cfaf" decoding="async" loading="lazy" width="100" height="100" data-file-width="142" data-file-height="142" /></a></div></div>
</td>
<td><a href="/wiki/Mana_Mastery" title="Mana Mastery">Mana Mastery</a>
</td>
<td>
<ul><li><a href="/wiki/Mana_Orb" title="Mana Orb">Mana Orbs</a> gain +17 random <a href="/wiki/Buff" title="Buff">buffs</a>.</li>
<li><b>Every 6s:</b> Gain 3 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li>Triggers 20% faster for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-item.</li></ul>
</td>
<td><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Skill" title="Skill">Skill</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:ManaOrb.png" class="image"><img alt="ManaOrb.png" src="/images/thumb/3/3d/ManaOrb.png/100px-ManaOrb.png?c746e7" decoding="async" loading="lazy" width="100" height="100" data-file-width="131" data-file-height="131" /></a></div></div>
</td>
<td><a href="/wiki/Mana_Orb" title="Mana Orb">Mana Orb</a>
</td>
<td>
<ul><li><b><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> item activates:</b> 50% chance to gain 1 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li><b>Use 35 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>:</b> Gain 17 random other <a href="/wiki/Buff" title="Buff">buffs</a> (once).</li></ul>
</td>
<td><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>6 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:ManaPotion.png" class="image"><img alt="ManaPotion.png" src="/images/thumb/c/c3/ManaPotion.png/48px-ManaPotion.png?a75fd1" decoding="async" loading="lazy" width="48" height="100" data-file-width="153" data-file-height="320" /></a></div></div>
</td>
<td><a href="/wiki/Mana_Potion" title="Mana Potion">Mana Potion</a>
</td>
<td>
<p><b><a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> used or health drops below 50%:</b> Consume this and gain 4 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> and 18 maximum health.
</p>
</td>
<td><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Potion" title="Potion">Potion</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>6 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Manathirst.png" class="image"><img alt="Manathirst.png" src="/images/thumb/5/53/Manathirst.png/33px-Manathirst.png?886b1a" decoding="async" loading="lazy" width="33" height="100" data-file-width="158" data-file-height="473" /></a></div></div>
</td>
<td><a href="/wiki/Manathirst" title="Manathirst">Manathirst</a>
</td>
<td>
<ul><li><b>On hit:</b> Gain 2 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li><b>30 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> gained:</b> Deal 10 <a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png?3b764e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-damage with 100% lifesteal. Deal +1 for each <a href="/wiki/Vampirism" title="Vampirism"><img alt="Vampirism" src="/images/thumb/3/33/Vampirism.png/15px-Vampirism.png?823c3c" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li></ul>
</td>
<td><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png?8cee0c" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>13 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Maneki-neko.png" class="image"><img alt="Maneki-neko.png" src="/images/thumb/1/1a/Maneki-neko.png/62px-Maneki-neko.png?5c2910" decoding="async" loading="lazy" width="62" height="100" data-file-width="199" data-file-height="318" /></a></div></div>
</td>
<td><a href="/wiki/Maneki-neko" title="Maneki-neko">Maneki-neko</a>
</td>
<td>
<ul><li>Sale chance +3%.</li></ul>
<br /><div style="font-size:100%; text-align:center;"><font color="gray">Value of <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> items &gt; 20 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></font></div>
<ul><li>15% chance to resist <a href="/wiki/Critical_hits" title="Critical hits">critical hits</a>.</li></ul>
<br /><div style="font-size:100%; text-align:center;"><font color="gray">Value of <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> items &gt; 40 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></font></div>
<ul><li><a href="/wiki/Godly" title="Godly">Godly</a> and <a href="/wiki/Unique" title="Unique">Unique</a> items trigger 15% faster.</li></ul>
</td>
<td><a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>10 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:MoonArmor.png" class="image"><img alt="MoonArmor.png" src="/images/thumb/c/c4/MoonArmor.png/75px-MoonArmor.png?a05ebe" decoding="async" loading="lazy" width="75" height="100" data-file-width="368" data-file-height="488" /></a></div></div>
</td>
<td><a href="/wiki/Moon_Armor" title="Moon Armor">Moon Armor</a>
</td>
<td>
<ul><li><b>Start of battle:</b> Gain 50 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> + 20 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-item.</li>
<li><b>Every 2.6s:</b> Gain 3 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> and <a href="/wiki/Reflect" title="Reflect">reflect</a> 2 <a href="/wiki/Debuff" title="Debuff">debuffs</a>.</li></ul>
</td>
<td><a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Armor" title="Armor">Armor</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>19 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:MoonShield.png" class="image"><img alt="MoonShield.png" src="/images/thumb/0/08/MoonShield.png/66px-MoonShield.png?933ba4" decoding="async" loading="lazy" width="66" height="100" data-file-width="296" data-file-height="447" /></a></div></div>
</td>
<td><a href="/wiki/Moon_Shield" title="Moon Shield">Moon Shield</a>
</td>
<td>
<ul><li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> items give +30% <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li><b><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> items gained 12 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>:</b> Gain 1 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li><b>On attacked (<a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>/<a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png?c8ee7a" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>):</b> 30% chance to prevent 12 damage and remove 0.7 stamina from your opponent.</li></ul>
</td>
<td><a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Shield" title="Shield">Shield</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>18 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:MoreStats.png" class="image"><img alt="MoreStats.png" src="/images/thumb/5/59/MoreStats.png/99px-MoreStats.png?4fbfd6" decoding="async" loading="lazy" width="99" height="100" data-file-width="173" data-file-height="175" /></a></div></div>
</td>
<td><a href="/wiki/More_Stats" title="More Stats">More Stats</a>
</td>
<td>
<ul><li><b>Start of battle:</b> Gain 15% maximum health. Your <a href="/wiki/Weapon" title="Weapon">Weapons</a> deal +5% damage.</li>
<li>Always offered in round 10.</li></ul>
</td>
<td><a href="/wiki/Skill" title="Skill">Skill</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:OilLamp.png" class="image"><img alt="OilLamp.png" src="/images/thumb/b/b9/OilLamp.png/43px-OilLamp.png?32a0e8" decoding="async" loading="lazy" width="43" height="100" data-file-width="138" data-file-height="316" /></a></div></div>
</td>
<td><a href="/wiki/Oil_Lamp" title="Oil Lamp">Oil Lamp</a>
</td>
<td>
<ul><li><b>Start of battle:</b> Gain 2 <a href="/wiki/Heat" title="Heat"><img alt="Heat" src="/images/thumb/c/c4/Heat.png/15px-Heat.png?4a5362" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.</li>
<li><b>Every 3.4s:</b> The <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Weapon" title="Weapon">Weapon</a> gains 1 damage and 5% <a href="/wiki/Accuracy" title="Accuracy">accuracy</a>.</li></ul>
</td>
<td><a href="/wiki/Fire" title="Fire"><img alt="Fire" src="/images/thumb/0/0a/Icon_Fire.png/15px-Icon_Fire.png?54f5a7" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>7 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Pan.png" class="image"><img alt="Pan.png" src="/images/thumb/c/ca/Pan.png/100px-Pan.png?a43964" decoding="async" loading="lazy" width="100" height="100" data-file-width="320" data-file-height="320" /></a></div></div>
</td>
<td><a href="/wiki/Pan" title="Pan">Pan</a>
</td>
<td>
<p>Deals +1 damage for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Food" title="Food">Food</a>.
</p>
</td>
<td><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="1"><a href="/wiki/Common" title="Common">Common</a>
</td>
<td>4 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Pandamonium.png" class="image"><img alt="Pandamonium.png" src="/images/thumb/a/a6/Pandamonium.png/100px-Pandamonium.png?8b4cc3" decoding="async" loading="lazy" width="100" height="35" data-file-width="479" data-file-height="169" /></a></div></div>
</td>
<td><a href="/wiki/Pandamonium" title="Pandamonium">Pandamonium</a>
</td>
<td>
<ul><li><b><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Food" title="Food">Food</a> activates</b>: Inflict 1 <a href="/wiki/Poison" title="Poison"><img alt="Poison" src="/images/thumb/0/05/Poison.png/15px-Poison.png?984131" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li></ul>
</td>
<td><a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png?33e5e5" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>11 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:PestilenceFlask.png" class="image"><img alt="PestilenceFlask.png" src="/images/thumb/1/13/PestilenceFlask.png/55px-PestilenceFlask.png?a47944" decoding="async" loading="lazy" width="55" height="100" data-file-width="97" data-file-height="176" /></a></div></div>
</td>
<td><a href="/wiki/Pestilence_Flask" title="Pestilence Flask">Pestilence Flask</a>
</td>
<td>
<p><b>Opponent <a href="/wiki/Heal" title="Heal">heals</a>:</b> Consume this and inflict 3 <a href="/wiki/Poison" title="Poison"><img alt="Poison" src="/images/thumb/0/05/Poison.png/15px-Poison.png?984131" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> and 1 <a href="/wiki/Poison" title="Poison"><img alt="Poison" src="/images/thumb/0/05/Poison.png/15px-Poison.png?984131" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> to yourself.
</p>
</td>
<td><a href="/wiki/Potion" title="Potion">Potion</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>7 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Piggybank.png" class="image"><img alt="Piggybank.png" src="/images/thumb/3/33/Piggybank.png/100px-Piggybank.png?48998a" decoding="async" loading="lazy" width="100" height="50" data-file-width="320" data-file-height="160" /></a></div></div>
</td>
<td><a href="/wiki/Piggybank" title="Piggybank">Piggybank</a>
</td>
<td>
<ul><li><b>Shop entered:</b> Gain 1 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />.</li>
<li><b>Start of battle:</b> Gain 2 maximum health for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> Start of battle item.</li></ul>
</td>
<td><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="1"><a href="/wiki/Common" title="Common">Common</a>
</td>
<td>3 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:PiggyPinata.png" class="image"><img alt="PiggyPinata.png" src="/images/thumb/4/4e/PiggyPinata.png/85px-PiggyPinata.png?6de2c1" decoding="async" loading="lazy" width="85" height="100" data-file-width="125" data-file-height="147" /></a></div></div>
</td>
<td><a href="/wiki/Piggy_Pinata" title="Piggy Pinata">Piggy Pinata</a>
</td>
<td>
<ul><li>Destroying a <a href="/wiki/Piggybank" title="Piggybank">Piggybank</a> generates items instead.</li>
<li><b>Shop entered:</b> <a href="/wiki/Piggybank" title="Piggybank">Piggybanks</a> have a 30% chance to explode.</li>
<li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> Items gain 5% <a href="/wiki/Critical_hits" title="Critical hits">critical hit</a> chance.</li></ul>
</td>
<td><a href="/wiki/Skill" title="Skill">Skill</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Pineapple.png" class="image"><img alt="Pineapple.png" src="/images/thumb/f/fb/Pineapple.png/39px-Pineapple.png?8bf553" decoding="async" loading="lazy" width="39" height="100" data-file-width="180" data-file-height="462" /></a></div></div>
</td>
<td><a href="/wiki/Pineapple" title="Pineapple">Pineapple</a>
</td>
<td>
<p><b>Every 3.3s:</b> Gain 1 <a href="/wiki/Spikes" title="Spikes"><img alt="Spikes" src="/images/thumb/9/9a/Spikes.png/15px-Spikes.png?ea75dd" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> and <a href="/wiki/Heal" title="Heal">heal</a> for 4.
</p>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Food" title="Food">Food</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>7 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:PlatinumCustomerCard.png" class="image"><img alt="PlatinumCustomerCard.png" src="/images/thumb/a/a1/PlatinumCustomerCard.png/100px-PlatinumCustomerCard.png?1f4932" decoding="async" loading="lazy" width="100" height="70" data-file-width="156" data-file-height="109" /></a></div></div>
</td>
<td><a href="/wiki/Platinum_Customer_Card" title="Platinum Customer Card">Platinum Customer Card</a>
</td>
<td>
<ul><li><b>Start of battle:</b> <a href="/wiki/Reflect" title="Reflect">Reflect</a> 2 <a href="/wiki/Debuff" title="Debuff">debuffs</a> for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Legendary" title="Legendary">Legendary</a>, <a href="/wiki/Godly" title="Godly">Godly</a> or <a href="/wiki/Unique" title="Unique">Unique</a> item.</li>
<li>20% chance to protect your <a href="/wiki/Buff" title="Buff">buffs</a> from removal.</li>
<li>Chance to find <a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-items +25%.</li>
<li>You can obtain +1 <a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-item.</li></ul>
</td>
<td><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>8 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:PocketSand.png" class="image"><img alt="PocketSand.png" src="/images/thumb/3/3a/PocketSand.png/100px-PocketSand.png?4ca6c2" decoding="async" loading="lazy" width="100" height="100" data-file-width="160" data-file-height="160" /></a></div></div>
</td>
<td><a href="/wiki/Pocket_Sand" title="Pocket Sand">Pocket Sand</a>
</td>
<td>
<p><b>Start of battle:</b> Inflict 1 <a href="/wiki/Blind" title="Blind"><img alt="Blind" src="/images/thumb/6/68/Blind.png/15px-Blind.png?f2b891" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.
</p>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="1"><a href="/wiki/Common" title="Common">Common</a>
</td>
<td>2 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:PoisonDagger.png" class="image"><img alt="PoisonDagger.png" src="/images/thumb/f/f4/PoisonDagger.png/50px-PoisonDagger.png?6c885a" decoding="async" loading="lazy" width="50" height="100" data-file-width="160" data-file-height="320" /></a></div></div>
</td>
<td><a href="/wiki/Poison_Dagger" title="Poison Dagger">Poison Dagger</a>
</td>
<td>
<ul><li><b>On hit:</b> Inflict 2 <a href="/wiki/Poison" title="Poison"><img alt="Poison" src="/images/thumb/0/05/Poison.png/15px-Poison.png?984131" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li><b>On <a href="/wiki/Stun" title="Stun">stun</a>:</b> Triggers extra attack.</li></ul>
</td>
<td><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>11 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Pop.png" class="image"><img alt="Pop.png" src="/images/thumb/7/71/Pop.png/100px-Pop.png?d5088e" decoding="async" loading="lazy" width="100" height="98" data-file-width="303" data-file-height="297" /></a></div></div>
</td>
<td><a href="/wiki/Pop" title="Pop">Pop</a>
</td>
<td>
<p>Attacks 3% faster for each <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> (up to 60%).
</p>
</td>
<td><a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png?3b764e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png?c8ee7a" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>, <a href="/wiki/Pet" title="Pet">Pet</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>6 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:PotionBelt.png" class="image"><img alt="PotionBelt.png" src="/images/thumb/9/94/PotionBelt.png/34px-PotionBelt.png?5d0ae9" decoding="async" loading="lazy" width="34" height="100" data-file-width="240" data-file-height="698" /></a></div></div>
</td>
<td><a href="/wiki/Potion_Belt" title="Potion Belt">Potion Belt</a>
</td>
<td>
<ul><li>Add 4 backpack slots.</li>
<li><b>First <a href="/wiki/Potion" title="Potion">Potion</a> inside consumed:</b> Gain a random <a href="/wiki/Buff" title="Buff">buff</a>.</li>
<li><b>4 <a href="/wiki/Potion" title="Potion">Potions</a> inside consumed:</b> Cleanse 8 <a href="/wiki/Debuff" title="Debuff">debuffs</a>.</li></ul>
</td>
<td><a href="/wiki/Bag" title="Bag">Bag</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:PoweroftheMoon.png" class="image"><img alt="PoweroftheMoon.png" src="/images/thumb/e/ea/PoweroftheMoon.png/99px-PoweroftheMoon.png?8f1f3c" decoding="async" loading="lazy" width="99" height="100" data-file-width="141" data-file-height="142" /></a></div></div>
</td>
<td><a href="/wiki/Power_of_the_Moon" title="Power of the Moon">Power of the Moon</a>
</td>
<td>
<ul><li><a href="/wiki/Fatigue" title="Fatigue">Fatigue</a> starts 4s earlier.</li>
<li><b><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Moon_Armor" title="Moon Armor">Moon Armor</a> activates:</b> Inflict 1 <a href="/wiki/Blind" title="Blind"><img alt="Blind" src="/images/thumb/6/68/Blind.png/15px-Blind.png?f2b891" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li><b><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Moon_Shield" title="Moon Shield">Moon Shield</a> activates:</b> Reflect 1 <a href="/wiki/Debuff" title="Debuff">debuff</a>.</li>
<li><b><a href="/wiki/Fatigue" title="Fatigue">Fatigue</a> starts:</b> <a href="/wiki/Heal" title="Heal">Heal</a> for 50% of your maximum health.</li></ul>
</td>
<td><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Skill" title="Skill">Skill</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Present.png" class="image"><img alt="Present.png" src="/images/thumb/0/02/Present.png/93px-Present.png?5862f4" decoding="async" loading="lazy" width="93" height="100" data-file-width="176" data-file-height="189" /></a></div></div>
</td>
<td><a href="/wiki/Present" title="Present">Present</a>
</td>
<td>
<ul><li><b>Shop entered:</b> Instead of gold, you receive items with a higher value.</li>
<li><b>Start of battle:</b> Gain 5 random  <a href="/wiki/Buff" title="Buff">buffs</a>.</li></ul>
</td>
<td><a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>10 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:PrismaticOrb.png" class="image"><img alt="PrismaticOrb.png" src="/images/thumb/2/2f/PrismaticOrb.png/100px-PrismaticOrb.png?e3c97b" decoding="async" loading="lazy" width="100" height="100" data-file-width="153" data-file-height="153" /></a></div></div>
</td>
<td><a href="/wiki/Prismatic_Orb" title="Prismatic Orb">Prismatic Orb</a>
</td>
<td>
<ul><li><b>Start of battle:</b> For each...</li>
<li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-item: Gain 2 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png?8cee0c" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-item: Gain 1 <a href="/wiki/Vampirism" title="Vampirism"><img alt="Vampirism" src="/images/thumb/3/33/Vampirism.png/15px-Vampirism.png?823c3c" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-item: Increase your <a href="/wiki/Heal" title="Heal">healing</a> by 4%.</li>
<li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png?33e5e5" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-item: Inflict a random <a href="/wiki/Debuff" title="Debuff">debuff</a>.</li></ul>
<p><br />
</p>
<ul><li><b>Every 8s:</b> Gain 1 of every type of <a href="/wiki/Buff" title="Buff">buff</a>.</li></ul>
</td>
<td><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png?8cee0c" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png?33e5e5" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>13 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:PrismaticSword.png" class="image"><img alt="PrismaticSword.png" src="/images/thumb/3/3f/PrismaticSword.png/36px-PrismaticSword.png?177744" decoding="async" loading="lazy" width="36" height="100" data-file-width="181" data-file-height="497" /></a></div></div>
</td>
<td><a href="/wiki/Prismatic_Sword" title="Prismatic Sword">Prismatic Sword</a>
</td>
<td>
<ul><li>For each...</li>
<li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-item: +8% attack speed.</li>
<li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png?8cee0c" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-item: +15% lifesteal.</li>
<li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-item: Gain +0.3 damage on hit.</li>
<li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png?33e5e5" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-item: +12% chance to inflict 4 random <a href="/wiki/Debuff" title="Debuff">debuffs</a> on hit.</li></ul>
</td>
<td><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png?8cee0c" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png?33e5e5" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>15 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:ProtectivePurse.png" class="image"><img alt="ProtectivePurse.png" src="/images/thumb/8/85/ProtectivePurse.png/98px-ProtectivePurse.png?6da35e" decoding="async" loading="lazy" width="98" height="100" data-file-width="245" data-file-height="251" /></a></div></div>
</td>
<td><a href="/wiki/Protective_Purse" title="Protective Purse">Protective Purse</a>
</td>
<td>
<ul><li>Add 1 backpack slot.</li>
<li><b>Start of battle:</b> Gain 15 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li></ul>
</td>
<td><a href="/wiki/Bag" title="Bag">Bag</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>2 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Pumpkin.png" class="image"><img alt="Pumpkin.png" src="/images/thumb/6/64/Pumpkin.png/100px-Pumpkin.png?3f68d8" decoding="async" loading="lazy" width="100" height="95" data-file-width="334" data-file-height="318" /></a></div></div>
</td>
<td><a href="/wiki/Pumpkin" title="Pumpkin">Pumpkin</a>
</td>
<td>
<ul><li><b>On hit:</b> 50% chance to <a href="/wiki/Stun" title="Stun">stun</a> for 0.5s.</li>
<li><b><a href="/wiki/Fatigue" title="Fatigue">Fatigue</a> starts:</b> gain 10 <a href="/wiki/Heat" title="Heat"><img alt="Heat" src="/images/thumb/c/c4/Heat.png/15px-Heat.png?4a5362" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.</li></ul>
</td>
<td><a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png?c8ee7a" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>, <a href="/wiki/Food" title="Food">Food</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>8 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:RainbowBadge.png" class="image"><img alt="RainbowBadge.png" src="/images/thumb/d/d2/RainbowBadge.png/100px-RainbowBadge.png?930831" decoding="async" loading="lazy" width="100" height="79" data-file-width="153" data-file-height="121" /></a></div></div>
</td>
<td><a href="/wiki/Rainbow_Badge" title="Rainbow Badge">Rainbow Badge</a>
</td>
<td>
<ul><li>Items of all classes are offered in the shop.</li>
<li><b>After 7s</b>: Gain 1 of every type of <a href="/wiki/Buff" title="Buff">buff</a>.</li></ul>
</td>
<td><a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:RipsawBlade.png" class="image"><img alt="RipsawBlade.png" src="/images/thumb/1/14/RipsawBlade.png/27px-RipsawBlade.png?3ce9e7" decoding="async" loading="lazy" width="27" height="100" data-file-width="173" data-file-height="633" /></a></div></div>
</td>
<td><a href="/wiki/Ripsaw_Blade" title="Ripsaw Blade">Ripsaw Blade</a>
</td>
<td>
<p><b>On hit:</b> Remove 1 damage gained in battle from all opponent <a href="/wiki/Weapon" title="Weapon">Weapons</a> and gain 0.5 damage.
</p>
</td>
<td><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>10 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:RubyEgg.png" class="image"><img alt="RubyEgg.png" src="/images/thumb/0/08/RubyEgg.png/85px-RubyEgg.png?da83b1" decoding="async" loading="lazy" width="85" height="100" data-file-width="255" data-file-height="298" /></a></div></div>
</td>
<td><a href="/wiki/Ruby_Egg" title="Ruby Egg">Ruby Egg</a>
</td>
<td>
<ul><li><b>Start of battle:</b> Gain 4 <a href="/wiki/Heat" title="Heat"><img alt="Heat" src="/images/thumb/c/c4/Heat.png/15px-Heat.png?4a5362" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>. <a href="/wiki/Reflect" title="Reflect">Reflect</a> 3 <a href="/wiki/Debuff" title="Debuff">debuffs</a>.</li>
<li>Hatches after 2 rounds in your backpack.</li></ul>
</td>
<td><a href="/wiki/Fire" title="Fire"><img alt="Fire" src="/images/thumb/0/0a/Icon_Fire.png/15px-Icon_Fire.png?54f5a7" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Pet" title="Pet">Pet</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>10 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:RubyWhelp.png" class="image"><img alt="RubyWhelp.png" src="/images/thumb/2/2a/RubyWhelp.png/95px-RubyWhelp.png?d40007" decoding="async" loading="lazy" width="95" height="100" data-file-width="316" data-file-height="333" /></a></div></div>
</td>
<td><a href="/wiki/Ruby_Whelp" title="Ruby Whelp">Ruby Whelp</a>
</td>
<td>
<p><b>Start of battle:</b> Gain 4 <a href="/wiki/Heat" title="Heat"><img alt="Heat" src="/images/thumb/c/c4/Heat.png/15px-Heat.png?4a5362" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>. <a href="/wiki/Reflect" title="Reflect">Reflect</a> 3 <a href="/wiki/Debuff" title="Debuff">debuffs</a>.
</p>
</td>
<td><a href="/wiki/Fire" title="Fire"><img alt="Fire" src="/images/thumb/0/0a/Icon_Fire.png/15px-Icon_Fire.png?54f5a7" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png?3b764e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png?c8ee7a" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>, <a href="/wiki/Pet" title="Pet">Pet</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>10 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:SackofSurprises.png" class="image"><img alt="SackofSurprises.png" src="/images/thumb/7/7f/SackofSurprises.png/100px-SackofSurprises.png?81eaa1" decoding="async" loading="lazy" width="100" height="100" data-file-width="345" data-file-height="344" /></a></div></div>
</td>
<td><a href="/wiki/Sack_of_Surprises" title="Sack of Surprises">Sack of Surprises</a>
</td>
<td>
<ul><li><b>Game started</b>: Replace this with random starting bags and items.</li></ul>
</td>
<td><a href="/wiki/Bag" title="Bag">Bag</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>10 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:SerpentStaff.png" class="image"><img alt="SerpentStaff.png" src="/images/thumb/2/25/SerpentStaff.png/30px-SerpentStaff.png?48236f" decoding="async" loading="lazy" width="30" height="100" data-file-width="194" data-file-height="647" /></a></div></div>
</td>
<td><a href="/wiki/Serpent_Staff" title="Serpent Staff">Serpent Staff</a>
</td>
<td>
<ul><li><b>On attack:</b> Use 4 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> to gain 2 damage and inflict 1 <a href="/wiki/Poison" title="Poison"><img alt="Poison" src="/images/thumb/0/05/Poison.png/15px-Poison.png?984131" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> for each 4 damage dealt.</li>
<li>You have 30% chance to duplicate <a href="/wiki/Poison" title="Poison"><img alt="Poison" src="/images/thumb/0/05/Poison.png/15px-Poison.png?984131" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> you inflict.</li></ul>
</td>
<td><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png?c8ee7a" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>17 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:ShellTotem.png" class="image"><img alt="ShellTotem.png" src="/images/thumb/b/b3/ShellTotem.png/51px-ShellTotem.png?3a371c" decoding="async" loading="lazy" width="51" height="100" data-file-width="160" data-file-height="314" /></a></div></div>
</td>
<td><a href="/wiki/Shell_Totem" title="Shell Totem">Shell Totem</a>
</td>
<td>
<ul><li><b>Every 3.4s:</b> If your health is above 70%, gain 1 <a href="/wiki/Empower" title="Empower"><img alt="Empower" src="/images/thumb/5/57/Empower.png/15px-Empower.png?31c0e4" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>. Otherwise, <a href="/wiki/Heal" title="Heal">heal</a> for 8.</li>
<li>Uses -15% stamina for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-item.</li></ul>
</td>
<td><a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:ShepherdsCrook.png" class="image"><img alt="ShepherdsCrook.png" src="/images/thumb/9/9e/ShepherdsCrook.png/46px-ShepherdsCrook.png?ae7440" decoding="async" loading="lazy" width="46" height="100" data-file-width="283" data-file-height="618" /></a></div></div>
</td>
<td><a href="/wiki/Shepherd%27s_Crook" class="mw-redirect" title="Shepherd&#39;s Crook">Shepherd&#39;s Crook</a>
</td>
<td>
<ul><li><b>Start of battle:</b> <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Weapon" title="Weapon">Weapons</a> gain 2 damage.</li>
<li>25% chance to protect your <a href="/wiki/Buff" title="Buff">buffs</a> from removal.</li>
<li>25% chance to <a href="/wiki/Resist" title="Resist">resist</a> <a href="/wiki/Blind" title="Blind"><img alt="Blind" src="/images/thumb/6/68/Blind.png/15px-Blind.png?f2b891" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> and <a href="/wiki/Cold" title="Cold"><img alt="Cold" src="/images/thumb/6/6b/Cold.png/15px-Cold.png?f89f07" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.</li></ul>
</td>
<td><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>8 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Shielded.png" class="image"><img alt="Shielded.png" src="/images/thumb/c/cb/Shielded.png/98px-Shielded.png?910ad1" decoding="async" loading="lazy" width="98" height="100" data-file-width="132" data-file-height="135" /></a></div></div>
</td>
<td><a href="/wiki/Shielded" title="Shielded">Shielded</a>
</td>
<td>
<ul><li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Shield" title="Shield">Shields</a> have +25% chance to block.</li>
<li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Armor" title="Armor">Armors</a> trigger 50% faster.</li></ul>
</td>
<td><a href="/wiki/Skill" title="Skill">Skill</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:ShieldofValor.png" class="image"><img alt="ShieldofValor.png" src="/images/thumb/9/9e/ShieldofValor.png/63px-ShieldofValor.png?441118" decoding="async" loading="lazy" width="63" height="100" data-file-width="284" data-file-height="452" /></a></div></div>
</td>
<td><a href="/wiki/Shield_of_Valor" title="Shield of Valor">Shield of Valor</a>
</td>
<td>
<ul><li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a>Items give 30% more <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li><b>On attacked (<a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>):</b> 30% chance to prevent 12 damage and remove 0.7 stamina from opponent.</li></ul>
</td>
<td><a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Shield" title="Shield">Shield</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>12 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:ShinyShell.png" class="image"><img alt="ShinyShell.png" src="/images/thumb/d/db/ShinyShell.png/100px-ShinyShell.png?7fcb1d" decoding="async" loading="lazy" width="100" height="89" data-file-width="186" data-file-height="165" /></a></div></div>
</td>
<td><a href="/wiki/Shiny_Shell" title="Shiny Shell">Shiny Shell</a>
</td>
<td>
<p><b>After 5s:</b> <a href="/wiki/Heal" title="Heal">Heal</a> for 5 + 3 for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-item.
</p>
</td>
<td><a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="1"><a href="/wiki/Common" title="Common">Common</a>
</td>
<td>2 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Shovel_item.png" class="image"><img alt="Shovel item.png" src="/images/thumb/d/d5/Shovel_item.png/23px-Shovel_item.png?b58fc9" decoding="async" loading="lazy" width="23" height="100" data-file-width="148" data-file-height="628" /></a></div></div>
</td>
<td><a href="/wiki/Shovel" title="Shovel">Shovel</a>
</td>
<td>
<ul><li><b>Shop entered:</b> Dig up a random item.</li>
<li><b>On hit:</b> 40% chance to inflict 1 <a href="/wiki/Blind" title="Blind"><img alt="Blind" src="/images/thumb/6/68/Blind.png/15px-Blind.png?f2b891" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li></ul>
</td>
<td><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>8 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:SkullBadge.png" class="image"><img alt="SkullBadge.png" src="/images/thumb/0/04/SkullBadge.png/79px-SkullBadge.png?d3d31d" decoding="async" loading="lazy" width="79" height="100" data-file-width="115" data-file-height="145" /></a></div></div>
</td>
<td><a href="/wiki/Skull_Badge" title="Skull Badge">Skull Badge</a>
</td>
<td>
<ul><li><a href="/wiki/Reaper" title="Reaper">Reaper</a> items are offered in the shop.</li>
<li><b>Every 1.5s:</b> Inflict a random <a href="/wiki/Debuff" title="Debuff">debuff</a>.</li></ul>
</td>
<td><a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:SmellyWall.png" class="image"><img alt="SmellyWall.png" src="/images/thumb/1/18/SmellyWall.png/88px-SmellyWall.png?89fb9d" decoding="async" loading="lazy" width="88" height="100" data-file-width="131" data-file-height="148" /></a></div></div>
</td>
<td><a href="/wiki/Smelly_Wall" title="Smelly Wall">Smelly Wall</a>
</td>
<td>
<ul><li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Garlic" title="Garlic">Garlic</a> gains +5 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li><b>20 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> gained:</b> Inflict 1 <a href="/wiki/Poison" title="Poison"><img alt="Poison" src="/images/thumb/0/05/Poison.png/15px-Poison.png?984131" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li></ul>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Skill" title="Skill">Skill</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:SmithingForDummies.png" class="image"><img alt="SmithingForDummies.png" src="/images/thumb/2/25/SmithingForDummies.png/100px-SmithingForDummies.png?700dbd" decoding="async" loading="lazy" width="100" height="86" data-file-width="163" data-file-height="140" /></a></div></div>
</td>
<td><a href="/wiki/Smithing_For_Dummies" title="Smithing For Dummies">Smithing For Dummies</a>
</td>
<td>
<ul><li><b>On buy:</b> Generate a <a href="/wiki/Whetstone" title="Whetstone">Whetstone</a>.</li>
<li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> Crafted <a href="/wiki/Weapon" title="Weapon">Weapons</a> deal +1 damage and use -25% stamina.</li></ul>
</td>
<td><a href="/wiki/Skill" title="Skill">Skill</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Snowball.png" class="image"><img alt="Snowball.png" src="/images/thumb/0/04/Snowball.png/100px-Snowball.png?1473bb" decoding="async" loading="lazy" width="100" height="98" data-file-width="138" data-file-height="135" /></a></div></div>
</td>
<td><a href="/wiki/Snowball" title="Snowball">Snowball</a>
</td>
<td>
<ul><li><b>Start of battle:</b> Inflict 2 <a href="/wiki/Cold" title="Cold"><img alt="Cold" src="/images/thumb/6/6b/Cold.png/15px-Cold.png?f89f07" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.</li>
<li>Your opponent gains 15% less maximum health from items.</li></ul>
</td>
<td><a href="/wiki/Ice" title="Ice"><img alt="Ice" src="/images/thumb/5/52/Icon_Ice.png/15px-Icon_Ice.png?61b1d9" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>4 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Snowcake.png" class="image"><img alt="Snowcake.png" src="/images/thumb/f/f8/Snowcake.png/96px-Snowcake.png?e44d58" decoding="async" loading="lazy" width="96" height="100" data-file-width="304" data-file-height="317" /></a></div></div>
</td>
<td><a href="/wiki/Snowcake" title="Snowcake">Snowcake</a>
</td>
<td>
<ul><li><b>Every 3s:</b> Inflict 1 <a href="/wiki/Cold" title="Cold"><img alt="Cold" src="/images/thumb/6/6b/Cold.png/15px-Cold.png?f89f07" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.</li>
<li>If your opponent has at least 10 <a href="/wiki/Cold" title="Cold"><img alt="Cold" src="/images/thumb/6/6b/Cold.png/15px-Cold.png?f89f07" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>, increase <a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png?3b764e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-damage by 10% and deal 10 <a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png?3b764e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-damage.</li></ul>
</td>
<td><a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Ice" title="Ice"><img alt="Ice" src="/images/thumb/5/52/Icon_Ice.png/15px-Icon_Ice.png?61b1d9" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Food" title="Food">Food</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>6 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:SnowStick.png" class="image"><img alt="SnowStick.png" src="/images/thumb/8/8f/SnowStick.png/25px-SnowStick.png?ea2d24" decoding="async" loading="lazy" width="25" height="100" data-file-width="161" data-file-height="629" /></a></div></div>
</td>
<td><a href="/wiki/Snow_Stick" title="Snow Stick">Snow Stick</a>
</td>
<td>
<p><b>On hit:</b> Inflict 3 <a href="/wiki/Cold" title="Cold"><img alt="Cold" src="/images/thumb/6/6b/Cold.png/15px-Cold.png?f89f07" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a> and 2 <a href="/wiki/Cold" title="Cold"><img alt="Cold" src="/images/thumb/6/6b/Cold.png/15px-Cold.png?f89f07" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a> to yourself.
</p>
</td>
<td><a href="/wiki/Ice" title="Ice"><img alt="Ice" src="/images/thumb/5/52/Icon_Ice.png/15px-Icon_Ice.png?61b1d9" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>8 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Spear.png" class="image"><img alt="Spear.png" src="/images/thumb/4/4f/Spear.png/25px-Spear.png?887bc8" decoding="async" loading="lazy" width="25" height="100" data-file-width="160" data-file-height="640" /></a></div></div>
</td>
<td><a href="/wiki/Spear" title="Spear">Spear</a>
</td>
<td>
<p><b>On hit:</b> Destroy 4 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> for each free <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> slot in front of this.
</p>
</td>
<td><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>6 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:SpectralDagger.png" class="image"><img alt="SpectralDagger.png" src="/images/thumb/1/12/SpectralDagger.png/37px-SpectralDagger.png?8b1fa5" decoding="async" loading="lazy" width="37" height="100" data-file-width="116" data-file-height="309" /></a></div></div>
</td>
<td><a href="/wiki/Spectral_Dagger" title="Spectral Dagger">Spectral Dagger</a>
</td>
<td>
<ul><li><b>On attack:</b> Use 1 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> to ignore <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> and deal +7 damage.</li>
<li><b>On <a href="/wiki/Stun" title="Stun">stun</a>:</b> Triggers extra attack.</li></ul>
</td>
<td><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>10 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:SpicyBanana.png" class="image"><img alt="SpicyBanana.png" src="/images/thumb/1/11/SpicyBanana.png/100px-SpicyBanana.png?a7da4a" decoding="async" loading="lazy" width="100" height="93" data-file-width="151" data-file-height="140" /></a></div></div>
</td>
<td><a href="/wiki/Spicy_Banana" title="Spicy Banana">Spicy Banana</a>
</td>
<td>
<ul><li><b><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Banana" title="Banana">Banana</a> activates:</b> 30% chance to gain 1 <a href="/wiki/Heat" title="Heat"><img alt="Heat" src="/images/thumb/c/c4/Heat.png/15px-Heat.png?4a5362" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.</li>
<li><b>1 stamina used:</b> <a href="/wiki/Heal" title="Heal">Heal</a> for 3.</li></ul>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Skill" title="Skill">Skill</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:SpikedShield.png" class="image"><img alt="SpikedShield.png" src="/images/thumb/2/25/SpikedShield.png/100px-SpikedShield.png?e8ea95" decoding="async" loading="lazy" width="100" height="100" data-file-width="320" data-file-height="320" /></a></div></div>
</td>
<td><a href="/wiki/Spiked_Shield" title="Spiked Shield">Spiked Shield</a>
</td>
<td>
<p><b>On attacked (<a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>):</b> 30% chance to prevent 7 damage, remove 0.3 stamina from opponent, and gain 1 <a href="/wiki/Spikes" title="Spikes"><img alt="Spikes" src="/images/thumb/9/9a/Spikes.png/15px-Spikes.png?ea75dd" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> (up to 4).
</p>
</td>
<td><a href="/wiki/Shield" title="Shield">Shield</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>8 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:StableRecombobulator.png" class="image"><img alt="StableRecombobulator.png" src="/images/thumb/6/61/StableRecombobulator.png/100px-StableRecombobulator.png?e50b16" decoding="async" loading="lazy" width="100" height="94" data-file-width="132" data-file-height="124" /></a></div></div>
</td>
<td><a href="/wiki/Stable_Recombobulator" title="Stable Recombobulator">Stable Recombobulator</a>
</td>
<td>
<ul><li><b>Shop entered:</b> Consume <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> items. Create different items based on the combined value.</li>
<li><b>Every 2.5s:</b> Gain 1 random <a href="/wiki/Buff" title="Buff">buff</a> and cleanse 1 <a href="/wiki/Debuff" title="Debuff">debuff</a>.</li></ul>
</td>
<td><a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png?33e5e5" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>6 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:StaminaSack.png" class="image"><img alt="StaminaSack.png" src="/images/thumb/1/1b/StaminaSack.png/45px-StaminaSack.png?373760" decoding="async" loading="lazy" width="45" height="100" data-file-width="281" data-file-height="626" /></a></div></div>
</td>
<td><a href="/wiki/Stamina_Sack" title="Stamina Sack">Stamina Sack</a>
</td>
<td>
<ul><li>Add 3 backpack slots.</li>
<li>Gain 1 maximum stamina.</li></ul>
</td>
<td><a href="/wiki/Bag" title="Bag">Bag</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:SteelGoobert.png" class="image"><img alt="SteelGoobert.png" src="/images/thumb/0/08/SteelGoobert.png/97px-SteelGoobert.png?b13d4b" decoding="async" loading="lazy" width="97" height="100" data-file-width="301" data-file-height="309" /></a></div></div>
</td>
<td><a href="/wiki/Steel_Goobert" title="Steel Goobert">Steel Goobert</a>
</td>
<td>
<p><b>5 <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> item activations:</b> <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Diamond" src="/images/thumb/e/ea/Diamond.png/15px-Diamond.png?f49dcb" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Weapon" title="Weapon">Weapons</a> gain +2 damage. Gain 16 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.
</p>
</td>
<td><a href="/wiki/Pet" title="Pet">Pet</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>17 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Stone.png" class="image"><img alt="Stone.png" src="/images/thumb/d/d4/Stone.png/100px-Stone.png?13e0c3" decoding="async" loading="lazy" width="100" height="100" data-file-width="160" data-file-height="160" /></a></div></div>
</td>
<td><a href="/wiki/Stone" title="Stone">Stone</a>
</td>
<td>
<ul><li>Can only be thrown once per battle.</li>
<li><b>On hit:</b> Destroy 4 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li></ul>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png?c8ee7a" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="1"><a href="/wiki/Common" title="Common">Common</a>
</td>
<td>1 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:StoneArmor.png" class="image"><img alt="StoneArmor.png" src="/images/thumb/8/8d/StoneArmor.png/75px-StoneArmor.png?d0b5e8" decoding="async" loading="lazy" width="75" height="100" data-file-width="338" data-file-height="449" /></a></div></div>
</td>
<td><a href="/wiki/Stone_Armor" title="Stone Armor">Stone Armor</a>
</td>
<td>
<ul><li>Items use +20% stamina.</li>
<li><b>Start of battle:</b> Gain 90 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li><b>Every 4s:</b> Remove 1 <a href="/wiki/Spikes" title="Spikes"><img alt="Spikes" src="/images/thumb/9/9a/Spikes.png/15px-Spikes.png?ea75dd" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> and 1 <a href="/wiki/Empower" title="Empower"><img alt="Empower" src="/images/thumb/5/57/Empower.png/15px-Empower.png?31c0e4" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a> from opponent.</li>
<li><b>Health drops below 50%:</b> Gain <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> equal to 40% of your missing health (once).</li></ul>
</td>
<td><a href="/wiki/Armor" title="Armor">Armor</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>13 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:StoneBadge.png" class="image"><img alt="StoneBadge.png" src="/images/thumb/a/a3/StoneBadge.png/99px-StoneBadge.png?a5d1cd" decoding="async" loading="lazy" width="99" height="100" data-file-width="132" data-file-height="133" /></a></div></div>
</td>
<td><a href="/wiki/Stone_Badge" title="Stone Badge">Stone Badge</a>
</td>
<td>
<ul><li>Your starting class items are no longer offered in the shop (even when this item is in storage).</li>
<li><b>Shop entered:</b> Generate items worth 1 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />.</li>
<li><b>Every 3s:</b> Gain 4 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li></ul>
</td>
<td><a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Stoned.png" class="image"><img alt="Stoned.png" src="/images/thumb/a/ab/Stoned.png/100px-Stoned.png?22d016" decoding="async" loading="lazy" width="100" height="100" data-file-width="145" data-file-height="145" /></a></div></div>
</td>
<td><a href="/wiki/Stoned" title="Stoned">Stoned</a>
</td>
<td>
<ul><li><b><a href="/wiki/Stone" title="Stone">Stone</a> or <a href="/wiki/Stone_Golem" title="Stone Golem">Stone Golem</a> dealt damage:</b> Gain 45% of the damage as <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li><b>While you have <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>:</b> 40% chance to resist <a href="/wiki/Critical_hits" title="Critical hits">critical hits</a>.</li></ul>
</td>
<td><a href="/wiki/Skill" title="Skill">Skill</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:StoneGolem.png" class="image"><img alt="StoneGolem.png" src="/images/thumb/8/8e/StoneGolem.png/95px-StoneGolem.png?83ddb3" decoding="async" loading="lazy" width="95" height="100" data-file-width="287" data-file-height="301" /></a></div></div>
</td>
<td><a href="/wiki/Stone_Golem" title="Stone Golem">Stone Golem</a>
</td>
<td>
<ul><li><b>On hit:</b> Gain 1 <a href="/wiki/Empower" title="Empower"><img alt="Empower" src="/images/thumb/5/57/Empower.png/15px-Empower.png?31c0e4" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>. 30% chance to <a href="/wiki/Stun" title="Stun">stun</a> for 0.5s.</li>
<li><b>Use 7 <a href="/wiki/Regeneration" title="Regeneration"><img alt="Regeneration" src="/images/thumb/5/53/Regeneration.png/15px-Regeneration.png?7bd3bc" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>:</b> Reduce <a href="/wiki/Cooldown" title="Cooldown">cooldown</a> to 2.6s and gain 150 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> (once).</li>
<li>Deals +10 damage for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Bag_of_Stones" title="Bag of Stones">Bag of Stones</a>.</li></ul>
</td>
<td><a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png?3b764e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png?c8ee7a" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>, <a href="/wiki/Pet" title="Pet">Pet</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>16 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:StoneHelm.png" class="image"><img alt="StoneHelm.png" src="/images/thumb/7/72/StoneHelm.png/58px-StoneHelm.png?451786" decoding="async" loading="lazy" width="58" height="100" data-file-width="172" data-file-height="297" /></a></div></div>
</td>
<td><a href="/wiki/Stone_Helm" title="Stone Helm">Stone Helm</a>
</td>
<td>
<ul><li><b>Start of battle:</b> Reduce damage taken by 25% for 5s and gain 35 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li>25% chance to resist <a href="/wiki/Critical_hits" title="Critical hits">critical hits</a>.</li>
<li>30% chance to resist <a href="/wiki/Stun" title="Stun">stuns</a>.</li></ul>
</td>
<td><a href="/wiki/Helmet" title="Helmet">Helmet</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>13 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:StoneShoes.png" class="image"><img alt="StoneShoes.png" src="/images/thumb/7/7d/StoneShoes.png/57px-StoneShoes.png?3b3e48" decoding="async" loading="lazy" width="57" height="100" data-file-width="167" data-file-height="290" /></a></div></div>
</td>
<td><a href="/wiki/Stone_Shoes" title="Stone Shoes">Stone Shoes</a>
</td>
<td>
<p><b>Health drops below 70%:</b> Gain 1 <a href="/wiki/Luck" title="Luck"><img alt="Luck" src="/images/thumb/f/f0/Luck.png/15px-Luck.png?9ec9f2" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>, 1 <a href="/wiki/Empower" title="Empower"><img alt="Empower" src="/images/thumb/5/57/Empower.png/15px-Empower.png?31c0e4" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>, and 30 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>. Reduce <a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png?c8ee7a" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>/<a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png?3b764e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-damage taken by 30% for 5s (once).
</p>
</td>
<td><a href="/wiki/Shoes" title="Shoes">Shoes</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>12 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:StoneSkinPotion.png" class="image"><img alt="StoneSkinPotion.png" src="/images/thumb/c/ce/StoneSkinPotion.png/49px-StoneSkinPotion.png?d8b455" decoding="async" loading="lazy" width="49" height="100" data-file-width="94" data-file-height="191" /></a></div></div>
</td>
<td><a href="/wiki/Stone_Skin_Potion" title="Stone Skin Potion">Stone Skin Potion</a>
</td>
<td>
<p><b>45 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> reached:</b> Consume this and convert 15 health to 30 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.
</p>
</td>
<td><a href="/wiki/Potion" title="Potion">Potion</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>6 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:StrongHealthPotion.png" class="image"><img alt="StrongHealthPotion.png" src="/images/thumb/c/cd/StrongHealthPotion.png/49px-StrongHealthPotion.png?86d443" decoding="async" loading="lazy" width="49" height="100" data-file-width="109" data-file-height="221" /></a></div></div>
</td>
<td><a href="/wiki/Strong_Health_Potion" title="Strong Health Potion">Strong Health Potion</a>
</td>
<td>
<p><b>Health drops below 50%:</b> Consume this and <a href="/wiki/Heal" title="Heal">heal</a> for 24, gain 3 <a href="/wiki/Regeneration" title="Regeneration"><img alt="Regeneration" src="/images/thumb/5/53/Regeneration.png/15px-Regeneration.png?7bd3bc" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> and cleanse 4 <a href="/wiki/Poison" title="Poison"><img alt="Poison" src="/images/thumb/0/05/Poison.png/15px-Poison.png?984131" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.
</p>
</td>
<td><a href="/wiki/Potion" title="Potion">Potion</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>8 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:StrongHeroicPotion.png" class="image"><img alt="StrongHeroicPotion.png" src="/images/thumb/e/eb/StrongHeroicPotion.png/76px-StrongHeroicPotion.png?6b4913" decoding="async" loading="lazy" width="76" height="100" data-file-width="131" data-file-height="173" /></a></div></div>
</td>
<td><a href="/wiki/Strong_Heroic_Potion" title="Strong Heroic Potion">Strong Heroic Potion</a>
</td>
<td>
<p><b>Out of stamina:</b> Consume this and regenerate 4 stamina and gain 1 <a href="/wiki/Empower" title="Empower"><img alt="Empower" src="/images/thumb/5/57/Empower.png/15px-Empower.png?31c0e4" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.
</p>
</td>
<td><a href="/wiki/Potion" title="Potion">Potion</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>9 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:StrongStoneSkinPotion.png" class="image"><img alt="StrongStoneSkinPotion.png" src="/images/thumb/1/1e/StrongStoneSkinPotion.png/55px-StrongStoneSkinPotion.png?7dd3d3" decoding="async" loading="lazy" width="55" height="100" data-file-width="103" data-file-height="186" /></a></div></div>
</td>
<td><a href="/wiki/Strong_Stone_Skin_Potion" title="Strong Stone Skin Potion">Strong Stone Skin Potion</a>
</td>
<td>
<p><b>45 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> reached:</b> Consume this and convert 15 health to 35 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> and gain 2 <a href="/wiki/Spikes" title="Spikes"><img alt="Spikes" src="/images/thumb/9/9a/Spikes.png/15px-Spikes.png?ea75dd" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> for 4s.
</p>
</td>
<td><a href="/wiki/Potion" title="Potion">Potion</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>9 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Superspacious.png" class="image"><img alt="Superspacious.png" src="/images/thumb/e/e6/Superspacious.png/90px-Superspacious.png?be537c" decoding="async" loading="lazy" width="90" height="100" data-file-width="136" data-file-height="151" /></a></div></div>
</td>
<td><a href="/wiki/Superspacious" title="Superspacious">Superspacious</a>
</td>
<td>
<ul><li><a href="/wiki/Bag" title="Bag">Bags</a> appear more often in the shop and have +30% chance to be on sale.</li>
<li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> item trigger 9% faster for each free <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Diamond" src="/images/thumb/e/ea/Diamond.png/15px-Diamond.png?f49dcb" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> slot.</li></ul>
</td>
<td><a href="/wiki/Skill" title="Skill">Skill</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Thornbloom.png" class="image"><img alt="Thornbloom.png" src="/images/thumb/6/6f/Thornbloom.png/97px-Thornbloom.png?24edc0" decoding="async" loading="lazy" width="97" height="100" data-file-width="463" data-file-height="476" /></a></div></div>
</td>
<td><a href="/wiki/Thornbloom" title="Thornbloom">Thornbloom</a>
</td>
<td>
<ul><li><b>On hit:</b> Gain 1 <a href="/wiki/Spikes" title="Spikes"><img alt="Spikes" src="/images/thumb/9/9a/Spikes.png/15px-Spikes.png?ea75dd" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>. 60% chance to gain 1 <a href="/wiki/Empower" title="Empower"><img alt="Empower" src="/images/thumb/5/57/Empower.png/15px-Empower.png?31c0e4" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.</li>
<li><b><a href="/wiki/Empower" title="Empower"><img alt="Empower" src="/images/thumb/5/57/Empower.png/15px-Empower.png?31c0e4" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a> gained:</b> Gain 10 maximum health.</li>
<li>Deals +1 damage per <a href="/wiki/Spikes" title="Spikes"><img alt="Spikes" src="/images/thumb/9/9a/Spikes.png/15px-Spikes.png?ea75dd" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li></ul>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>14 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Thornburst.png" class="image"><img alt="Thornburst.png" src="/images/7/7e/Thornburst.png?1d3d6c" decoding="async" loading="lazy" width="100" height="87" data-file-width="90" data-file-height="78" /></a></div></div>
</td>
<td><a href="/wiki/Thornburst" title="Thornburst">Thornburst</a>
</td>
<td>
<ul><li><b>Every 10s:</b> <a href="/wiki/Stun" title="Stun">Stun</a> for 0.5s and gain 3 <a href="/wiki/Spikes" title="Spikes"><img alt="Spikes" src="/images/thumb/9/9a/Spikes.png/15px-Spikes.png?ea75dd" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> (up to 3 times).</li>
<li>Triggers 5% faster for each <a href="/wiki/Spikes" title="Spikes"><img alt="Spikes" src="/images/thumb/9/9a/Spikes.png/15px-Spikes.png?ea75dd" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li></ul>
</td>
<td><a href="/wiki/Skill" title="Skill">Skill</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:ThornWhip.png" class="image"><img alt="ThornWhip.png" src="/images/thumb/7/7b/ThornWhip.png/100px-ThornWhip.png?e12059" decoding="async" loading="lazy" width="100" height="100" data-file-width="480" data-file-height="480" /></a></div></div>
</td>
<td><a href="/wiki/Thorn_Whip" title="Thorn Whip">Thorn Whip</a>
</td>
<td>
<ul><li><b>On hit:</b> Gain 1 <a href="/wiki/Spikes" title="Spikes"><img alt="Spikes" src="/images/thumb/9/9a/Spikes.png/15px-Spikes.png?ea75dd" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li>Deals +1 damage per <a href="/wiki/Spikes" title="Spikes"><img alt="Spikes" src="/images/thumb/9/9a/Spikes.png/15px-Spikes.png?ea75dd" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li></ul>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>8 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Tim.png" class="image"><img alt="Tim.png" src="/images/thumb/e/ea/Tim.png/76px-Tim.png?127103" decoding="async" loading="lazy" width="76" height="100" data-file-width="88" data-file-height="116" /></a></div></div>
</td>
<td><a href="/wiki/Tim" title="Tim">Tim</a>
</td>
<td>
<div style="font-size:100%; text-align:center;"><font color="gray">Weapon sockets:</font></div>
<p><b>On hit:</b> 50% chance to steal a random <a href="/wiki/Buff" title="Buff">buff</a>.
</p>
<br /><div style="font-size:100%; text-align:center;"><font color="gray">Armor &amp; other sockets:</font></div>
<p>25% chance to <a href="/wiki/Resist" title="Resist">resist</a> <a href="/wiki/Debuff" title="Debuff">debuffs</a> or <a href="/wiki/Critical_hits" title="Critical hits">critical hits</a>.
</p>
<br /><div style="font-size:100%; text-align:center;"><font color="gray">Backpack:</font></div>
<p><b>Opponent drops below 30%:</b> <a href="/wiki/Heal" title="Heal">Heal</a> for 50 and gain 5 <a href="/wiki/Empower" title="Empower"><img alt="Empower" src="/images/thumb/5/57/Empower.png/15px-Empower.png?31c0e4" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.
</p>
</td>
<td><a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png?33e5e5" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Gemstone" title="Gemstone">Gemstone</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>10 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:TimeDilator.png" class="image"><img alt="TimeDilator.png" src="/images/thumb/1/16/TimeDilator.png/77px-TimeDilator.png?ce5efb" decoding="async" loading="lazy" width="77" height="100" data-file-width="116" data-file-height="151" /></a></div></div>
</td>
<td><a href="/wiki/Time_Dilator" title="Time Dilator">Time Dilator</a>
</td>
<td>
<ul><li>Your and your opponent's <a href="/wiki/Weapon" title="Weapon">Weapons</a> attack 30% slower.</li>
<li><b>Every 1s:</b> Your item with the highest cooldown triggers 6% faster.</li></ul>
</td>
<td><a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>8 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Torch.png" class="image"><img alt="Torch.png" src="/images/thumb/b/b2/Torch.png/45px-Torch.png?cbccb7" decoding="async" loading="lazy" width="45" height="100" data-file-width="175" data-file-height="384" /></a></div></div>
</td>
<td><a href="/wiki/Torch" title="Torch">Torch</a>
</td>
<td>
<p><b>On hit:</b> 25% chance to gain 1 damage.
</p>
</td>
<td><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:UnidentifiedAmulet.png" class="image"><img alt="UnidentifiedAmulet.png" src="/images/thumb/1/14/UnidentifiedAmulet.png/100px-UnidentifiedAmulet.png?658c73" decoding="async" loading="lazy" width="100" height="100" data-file-width="171" data-file-height="171" /></a></div></div>
</td>
<td><a href="/wiki/Unidentified_Amulet" title="Unidentified Amulet">Unidentified Amulet</a>
</td>
<td>
<p><b>On buy:</b> Gain a random effect.
</p>
</td>
<td><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>6 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:UniquelyUnique.png" class="image"><img alt="UniquelyUnique.png" src="/images/thumb/e/ef/UniquelyUnique.png/94px-UniquelyUnique.png?a0756c" decoding="async" loading="lazy" width="94" height="100" data-file-width="133" data-file-height="141" /></a></div></div>
</td>
<td><a href="/wiki/Uniquely_Unique" title="Uniquely Unique">Uniquely Unique</a>
</td>
<td>
<ul><li>The <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> item triggers 20% faster +10% for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Diamond" src="/images/thumb/e/ea/Diamond.png/15px-Diamond.png?f49dcb" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Unique" title="Unique">Unique</a> item, <a href="/wiki/Customer_Card" title="Customer Card">Customer Card</a> or <a href="/wiki/Platinum_Customer_Card" title="Platinum Customer Card">Platinum Customer Card</a>.</li>
<li>Chance to find <a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-items +50%. You can obtain +2 <a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-items.</li></ul>
</td>
<td><a href="/wiki/Skill" title="Skill">Skill</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:UnsettlingPresence.png" class="image"><img alt="UnsettlingPresence.png" src="/images/thumb/a/a0/UnsettlingPresence.png/54px-UnsettlingPresence.png?901958" decoding="async" loading="lazy" width="54" height="100" data-file-width="159" data-file-height="291" /></a></div></div>
</td>
<td><a href="/wiki/Unsettling_Presence" title="Unsettling Presence">Unsettling Presence</a>
</td>
<td>
<ul><li>Deal +30% of your <a href="/wiki/Heal" title="Heal">healing</a> as <a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png?3b764e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-damage.</li>
<li><b>Every 3s:</b> Use a random <a href="/wiki/Buff" title="Buff">buff</a> to <a href="/wiki/Heal" title="Heal">heal</a> for 12.</li></ul>
</td>
<td><a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png?33e5e5" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Pet" title="Pet">Pet</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>10 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:UnstableRecombobulator.png" class="image"><img alt="UnstableRecombobulator.png" src="/images/thumb/1/1e/UnstableRecombobulator.png/100px-UnstableRecombobulator.png?290fcb" decoding="async" loading="lazy" width="100" height="84" data-file-width="158" data-file-height="132" /></a></div></div>
</td>
<td><a href="/wiki/Unstable_Recombobulator" title="Unstable Recombobulator">Unstable Recombobulator</a>
</td>
<td>
<ul><li><b>Shop entered:</b> Consume this and <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> items. Create different items based on the combined value.</li>
<li><b>Every 4s:</b> Gain 1 random <a href="/wiki/Buff" title="Buff">buff</a> and cleanse 1 <a href="/wiki/Debuff" title="Debuff">debuff</a>.</li></ul>
</td>
<td><a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png?33e5e5" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>6 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:VampiricArmor.png" class="image"><img alt="VampiricArmor.png" src="/images/thumb/f/fb/VampiricArmor.png/81px-VampiricArmor.png?91a173" decoding="async" loading="lazy" width="81" height="100" data-file-width="388" data-file-height="480" /></a></div></div>
</td>
<td><a href="/wiki/Vampiric_Armor" title="Vampiric Armor">Vampiric Armor</a>
</td>
<td>
<ul><li><b>Start of battle:</b> Convert 30 health into 65 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> and gain 2 <a href="/wiki/Vampirism" title="Vampirism"><img alt="Vampirism" src="/images/thumb/3/33/Vampirism.png/15px-Vampirism.png?823c3c" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li><b>Every 2.8s:</b> Convert 10 health into 20 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li></ul>
</td>
<td><a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png?8cee0c" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Armor" title="Armor">Armor</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>16 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:VampiricGloves.png" class="image"><img alt="VampiricGloves.png" src="/images/thumb/b/b8/VampiricGloves.png/100px-VampiricGloves.png?f2f79c" decoding="async" loading="lazy" width="100" height="50" data-file-width="320" data-file-height="160" /></a></div></div>
</td>
<td><a href="/wiki/Vampiric_Gloves" title="Vampiric Gloves">Vampiric Gloves</a>
</td>
<td>
<p><b>After 4s:</b> Gain 5 <a href="/wiki/Vampirism" title="Vampirism"><img alt="Vampirism" src="/images/thumb/3/33/Vampirism.png/15px-Vampirism.png?823c3c" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>, <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> items trigger 35% faster.
</p>
</td>
<td><a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png?8cee0c" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Gloves" title="Gloves">Gloves</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>12 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:VampiricPotion.png" class="image"><img alt="VampiricPotion.png" src="/images/thumb/5/54/VampiricPotion.png/35px-VampiricPotion.png?8a26ce" decoding="async" loading="lazy" width="35" height="100" data-file-width="118" data-file-height="332" /></a></div></div>
</td>
<td><a href="/wiki/Vampiric_Potion" title="Vampiric Potion">Vampiric Potion</a>
</td>
<td>
<p><b>Both characters drop below 80% health:</b> Consume this and gain 3 <a href="/wiki/Vampirism" title="Vampirism"><img alt="Vampirism" src="/images/thumb/3/33/Vampirism.png/15px-Vampirism.png?823c3c" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> and deal 15 <a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png?3b764e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-damage with 100% lifesteal.
</p>
</td>
<td><a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png?8cee0c" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Potion" title="Potion">Potion</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>8 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:VillainSword.png" class="image"><img alt="VillainSword.png" src="/images/thumb/6/6e/VillainSword.png/51px-VillainSword.png?bb320f" decoding="async" loading="lazy" width="51" height="100" data-file-width="162" data-file-height="314" /></a></div></div>
</td>
<td><a href="/wiki/Villain_Sword" title="Villain Sword">Villain Sword</a>
</td>
<td>
<p><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-<a href="/wiki/Weapon" title="Weapon">Weapons</a> deal -2 damage. Deals +4 damage per <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-<a href="/wiki/Weapon" title="Weapon">Weapon</a>.
</p>
</td>
<td><a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png?33e5e5" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>7 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:WalrusTusk.png" class="image"><img alt="WalrusTusk.png" src="/images/thumb/2/22/WalrusTusk.png/50px-WalrusTusk.png?525e95" decoding="async" loading="lazy" width="50" height="100" data-file-width="160" data-file-height="320" /></a></div></div>
</td>
<td><a href="/wiki/Walrus_Tusk" title="Walrus Tusk">Walrus Tusk</a>
</td>
<td>
<p><b>Start of battle:</b> Gain 1 <a href="/wiki/Spikes" title="Spikes"><img alt="Spikes" src="/images/thumb/9/9a/Spikes.png/15px-Spikes.png?ea75dd" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.
</p>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="1"><a href="/wiki/Common" title="Common">Common</a>
</td>
<td>4 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Whetstone.png" class="image"><img alt="Whetstone.png" src="/images/thumb/9/9f/Whetstone.png/100px-Whetstone.png?e5238e" decoding="async" loading="lazy" width="100" height="100" data-file-width="160" data-file-height="160" /></a></div></div>
</td>
<td><a href="/wiki/Whetstone" title="Whetstone">Whetstone</a>
</td>
<td>
<p><b>Start of battle:</b> <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Weapon" title="Weapon">Weapons</a> gain 1 damage.
</p>
</td>
<td><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="1"><a href="/wiki/Common" title="Common">Common</a>
</td>
<td>4 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:WingedBoots.png" class="image"><img alt="WingedBoots.png" src="/images/thumb/a/af/WingedBoots.png/98px-WingedBoots.png?661cde" decoding="async" loading="lazy" width="98" height="100" data-file-width="124" data-file-height="126" /></a></div></div>
</td>
<td><a href="/wiki/Winged_Boots" title="Winged Boots">Winged Boots</a>
</td>
<td>
<p><b>Health drops below 70%:</b> Gain 1 <a href="/wiki/Empower" title="Empower"><img alt="Empower" src="/images/thumb/5/57/Empower.png/15px-Empower.png?31c0e4" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>, cleanse 15 debuffs and dodge the next 3 <a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>/<a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png?c8ee7a" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-attacks (once).
</p>
</td>
<td><a href="/wiki/Shoes" title="Shoes">Shoes</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>13 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:WolfBadge.png" class="image"><img alt="WolfBadge.png" src="/images/thumb/5/57/WolfBadge.png/100px-WolfBadge.png?439d03" decoding="async" loading="lazy" width="100" height="95" data-file-width="155" data-file-height="148" /></a></div></div>
</td>
<td><a href="/wiki/Wolf_Badge" title="Wolf Badge">Wolf Badge</a>
</td>
<td>
<ul><li><a href="/wiki/Berserker" title="Berserker">Berserker</a> items are offered in the shop.</li>
<li><b>Health drops below 50%:</b> Enter <a href="/wiki/Battle_Rage" title="Battle Rage">Battle Rage</a> for 5s (once).</li>
<li><b>During <a href="/wiki/Battle_Rage" title="Battle Rage">Battle Rage</a>:</b> <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> items trigger 25% faster. You take 20% reduced damage.</li></ul>
</td>
<td><a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Wolpertinger.png" class="image"><img alt="Wolpertinger.png" src="/images/thumb/c/c3/Wolpertinger.png/100px-Wolpertinger.png?c48373" decoding="async" loading="lazy" width="100" height="98" data-file-width="350" data-file-height="344" /></a></div></div>
</td>
<td><a href="/wiki/Wolpertinger" title="Wolpertinger">Wolpertinger</a>
</td>
<td>
<ul><li>Increase base stamina regeneration by 0.7% for each <a href="/wiki/Buff" title="Buff">buff</a> you have.</li>
<li><b>Every 5s:</b> Gain 3 of the <a href="/wiki/Buff" title="Buff">buff</a> you have least of.</li>
<li>Triggers 15% faster for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Pet" title="Pet">Pet</a>.</li></ul>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Pet" title="Pet">Pet</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>12 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:WonkySnowman.png" class="image"><img alt="WonkySnowman.png" src="/images/thumb/f/fc/WonkySnowman.png/57px-WonkySnowman.png?ea0ba3" decoding="async" loading="lazy" width="57" height="100" data-file-width="158" data-file-height="275" /></a></div></div>
</td>
<td><a href="/wiki/Wonky_Snowman" title="Wonky Snowman">Wonky Snowman</a>
</td>
<td>
<p><b>On Buy:</b> Split into 2 <a href="/wiki/Snowball" title="Snowball">Snowballs</a>.
</p>
</td>
<td><a href="/wiki/Ice" title="Ice"><img alt="Ice" src="/images/thumb/5/52/Icon_Ice.png/15px-Icon_Ice.png?61b1d9" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>8 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:WoodenBuckler.png" class="image"><img alt="WoodenBuckler.png" src="/images/thumb/2/2c/WoodenBuckler.png/100px-WoodenBuckler.png?6a31a8" decoding="async" loading="lazy" width="100" height="100" data-file-width="320" data-file-height="320" /></a></div></div>
</td>
<td><a href="/wiki/Wooden_Buckler" title="Wooden Buckler">Wooden Buckler</a>
</td>
<td>
<p><b>On attacked (<a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>):</b> 30% chance to prevent 7 damage and remove 0.3 stamina from opponent.
</p>
</td>
<td><a href="/wiki/Shield" title="Shield">Shield</a>
</td>
<td data-sort-value="1"><a href="/wiki/Common" title="Common">Common</a>
</td>
<td>4 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:WoodenSword.png" class="image"><img alt="WoodenSword.png" src="/images/thumb/9/9b/WoodenSword.png/50px-WoodenSword.png?97eead" decoding="async" loading="lazy" width="50" height="100" data-file-width="160" data-file-height="320" /></a></div></div>
</td>
<td><a href="/wiki/Wooden_Sword" title="Wooden Sword">Wooden Sword</a>
</td>
<td>
</td>
<td><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png?44556e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="1"><a href="/wiki/Common" title="Common">Common</a>
</td>
<td>3 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr></tbody></table>
<table class="navbox mw-collapsible" data-expandtext="Show" data-collapsetext="Hide"><tbody><tr class="navbox-title"><th colspan="2" scope="col"><span class="navbox-vde"><a href="/wiki/Template:Items" title="Template:Items"><span title="View this navbox template">v</span></a> · <span class="plainlinks"><a class="text" href="https://backpackbattles.wiki.gg/wiki/Template_talk:Items"><span title="Discuss this navbox template">d</span></a> · <a class="text" href="https://backpackbattles.wiki.gg/wiki/Template:Items?action=edit"><span title="Edit this navbox template">e</span></a></span></span><a href="/wiki/Items" title="Items">Items</a></th></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Accessory" title="Accessory">Accessory</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Acorn_Collar" title="Acorn Collar">Acorn Collar</a>&#160;• <a href="/wiki/Amulet_of_Alchemy" title="Amulet of Alchemy">Amulet of Alchemy</a>&#160;• <a href="/wiki/Amulet_of_Darkness" title="Amulet of Darkness">Amulet of Darkness</a>&#160;• <a href="/wiki/Amulet_of_Energy" title="Amulet of Energy">Amulet of Energy</a>&#160;• <a href="/wiki/Amulet_of_Feasting" title="Amulet of Feasting">Amulet of Feasting</a>&#160;• <a href="/wiki/Amulet_of_Life" title="Amulet of Life">Amulet of Life</a>&#160;• <a href="/wiki/Amulet_of_Steel" title="Amulet of Steel">Amulet of Steel</a>&#160;• <a href="/wiki/Amulet_of_the_Wild" title="Amulet of the Wild">Amulet of the Wild</a>&#160;• <a href="/wiki/Anvil" title="Anvil">Anvil</a>&#160;• <a href="/wiki/Bag_of_Stones" title="Bag of Stones">Bag of Stones</a>&#160;• <a href="/wiki/Blood_Amulet" title="Blood Amulet">Blood Amulet</a>&#160;• <a href="/wiki/Book_of_Ice" title="Book of Ice">Book of Ice</a>&#160;• <a href="/wiki/Box_of_Riches" title="Box of Riches">Box of Riches</a>&#160;• <a href="/wiki/Bunch_of_Coins" title="Bunch of Coins">Bunch of Coins</a>&#160;• <a href="/wiki/Burning_Banner" title="Burning Banner">Burning Banner</a>&#160;• <a href="/wiki/Cauldron" title="Cauldron">Cauldron</a>&#160;• <a href="/wiki/Customer_Card" title="Customer Card">Customer Card</a>&#160;• <a href="/wiki/Dark_Lantern" title="Dark Lantern">Dark Lantern</a>&#160;• <a href="/wiki/Deck_of_Cards" title="Deck of Cards">Deck of Cards</a>&#160;• <a href="/wiki/Deerwood_Guardian" title="Deerwood Guardian">Deerwood Guardian</a>&#160;• <a href="/wiki/Djinn_Lamp" title="Djinn Lamp">Djinn Lamp</a>&#160;• <a href="/wiki/Draconic_Orb" title="Draconic Orb">Draconic Orb</a>&#160;• <a href="/wiki/Dragon_Nest" title="Dragon Nest">Dragon Nest</a>&#160;• <a href="/wiki/Fanfare" title="Fanfare">Fanfare</a>&#160;• <a href="/wiki/Flame" title="Flame">Flame</a>&#160;• <a href="/wiki/Flame_Badge" title="Flame Badge">Flame Badge</a>&#160;• <a href="/wiki/Flute" title="Flute">Flute</a>&#160;• <a href="/wiki/Friendly_Fire" title="Friendly Fire">Friendly Fire</a>&#160;• <a href="/wiki/Frozen_Flame" title="Frozen Flame">Frozen Flame</a>&#160;• <a href="/wiki/Healing_Herbs" title="Healing Herbs">Healing Herbs</a>&#160;• <a href="/wiki/Heart_Container" title="Heart Container">Heart Container</a>&#160;• <a href="/wiki/Heart_of_Darkness" title="Heart of Darkness">Heart of Darkness</a>&#160;• <a href="/wiki/King_Crown" title="King Crown">King Crown</a>&#160;• <a href="/wiki/Leaf_Badge" title="Leaf Badge">Leaf Badge</a>&#160;• <a href="/wiki/Lucky_Clover" title="Lucky Clover">Lucky Clover</a>&#160;• <a href="/wiki/Lucky_Piggy" title="Lucky Piggy">Lucky Piggy</a>&#160;• <a href="/wiki/Mana_Orb" title="Mana Orb">Mana Orb</a>&#160;• <a href="/wiki/Maneki-neko" title="Maneki-neko">Maneki-neko</a>&#160;• <a href="/wiki/Mega_Clover" title="Mega Clover">Mega Clover</a>&#160;• <a href="/wiki/Miss_Fortune" title="Miss Fortune">Miss Fortune</a>&#160;• <a href="/wiki/Mr._Struggles" title="Mr. Struggles">Mr. Struggles</a>&#160;• <a href="/wiki/Mrs._Struggles" title="Mrs. Struggles">Mrs. Struggles</a>&#160;• <a href="/wiki/Nocturnal_Lock_Lifter" title="Nocturnal Lock Lifter">Nocturnal Lock Lifter</a>&#160;• <a href="/wiki/Oil_Lamp" title="Oil Lamp">Oil Lamp</a>&#160;• <a href="/wiki/Piercing_Arrow" title="Piercing Arrow">Piercing Arrow</a>&#160;• <a href="/wiki/Piggybank" title="Piggybank">Piggybank</a>&#160;• <a href="/wiki/Platinum_Customer_Card" title="Platinum Customer Card">Platinum Customer Card</a>&#160;• <a href="/wiki/Pocket_Sand" title="Pocket Sand">Pocket Sand</a>&#160;• <a href="/wiki/Poison_Ivy" title="Poison Ivy">Poison Ivy</a>&#160;• <a href="/wiki/Present" title="Present">Present</a>&#160;• <a href="/wiki/Prismatic_Orb" title="Prismatic Orb">Prismatic Orb</a>&#160;• <a href="/wiki/Rainbow_Badge" title="Rainbow Badge">Rainbow Badge</a>&#160;• <a href="/wiki/Shaman_Mask" title="Shaman Mask">Shaman Mask</a>&#160;• <a href="/wiki/Shepherd%27s_Crook" class="mw-redirect" title="Shepherd&#39;s Crook">Shepherd's Crook</a>&#160;• <a href="/wiki/Shiny_Shell" title="Shiny Shell">Shiny Shell</a>&#160;• <a href="/wiki/Skull_Badge" title="Skull Badge">Skull Badge</a>&#160;• <a href="/wiki/Snowball" title="Snowball">Snowball</a>&#160;• <a href="/wiki/Spiked_Collar" title="Spiked Collar">Spiked Collar</a>&#160;• <a href="/wiki/Stable_Recombobulator" title="Stable Recombobulator">Stable Recombobulator</a>&#160;• <a href="/wiki/Stone_Badge" title="Stone Badge">Stone Badge</a>&#160;• <a href="/wiki/Time_Dilator" title="Time Dilator">Time Dilator</a>&#160;• <a href="/wiki/Unidentified_Amulet" title="Unidentified Amulet">Unidentified Amulet</a>&#160;• <a href="/wiki/Unstable_Recombobulator" title="Unstable Recombobulator">Unstable Recombobulator</a>&#160;• <a href="/wiki/Walrus_Tusk" title="Walrus Tusk">Walrus Tusk</a>&#160;• <a href="/wiki/Whetstone" title="Whetstone">Whetstone</a>&#160;• <a href="/wiki/Wolf_Badge" title="Wolf Badge">Wolf Badge</a>&#160;• <a href="/wiki/Wolf_Emblem" title="Wolf Emblem">Wolf Emblem</a>&#160;• <a href="/wiki/Wonky_Snowman" title="Wonky Snowman">Wonky Snowman</a>&#160;• <a href="/wiki/Yggdrasil_Leaf" title="Yggdrasil Leaf">Yggdrasil Leaf</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Armor" title="Armor">Armor</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Corrupted_Armor" title="Corrupted Armor">Corrupted Armor</a>&#160;• <a href="/wiki/Dragonscale_Armor" title="Dragonscale Armor">Dragonscale Armor</a>&#160;• <a href="/wiki/Holy_Armor" title="Holy Armor">Holy Armor</a>&#160;• <a href="/wiki/Ice_Armor" title="Ice Armor">Ice Armor</a>&#160;• <a href="/wiki/Leather_Armor" title="Leather Armor">Leather Armor</a>&#160;• <a href="/wiki/Moon_Armor" title="Moon Armor">Moon Armor</a>&#160;• <a href="/wiki/Stone_Armor" title="Stone Armor">Stone Armor</a>&#160;• <a href="/wiki/Sun_Armor" title="Sun Armor">Sun Armor</a>&#160;• <a href="/wiki/Vampiric_Armor" title="Vampiric Armor">Vampiric Armor</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Bag" title="Bag">Bag</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Box_of_Prosperity" title="Box of Prosperity">Box of Prosperity</a>&#160;• <a href="/wiki/Duffle_Bag" title="Duffle Bag">Duffle Bag</a>&#160;• <a href="/wiki/Fanny_Pack" title="Fanny Pack">Fanny Pack</a>&#160;• <a href="/wiki/Fire_Pit" title="Fire Pit">Fire Pit</a>&#160;• <a href="/wiki/Holdall" title="Holdall">Holdall</a>&#160;• <a href="/wiki/Leather_Bag" title="Leather Bag">Leather Bag</a>&#160;• <a href="/wiki/Offering_Bowl" title="Offering Bowl">Offering Bowl</a>&#160;• <a href="/wiki/Potion_Belt" title="Potion Belt">Potion Belt</a>&#160;• <a href="/wiki/Protective_Purse" title="Protective Purse">Protective Purse</a>&#160;• <a href="/wiki/Ranger_Bag" title="Ranger Bag">Ranger Bag</a>&#160;• <a href="/wiki/Relic_Case" title="Relic Case">Relic Case</a>&#160;• <a href="/wiki/Sack_of_Surprises" title="Sack of Surprises">Sack of Surprises</a>&#160;• <a href="/wiki/Stamina_Sack" title="Stamina Sack">Stamina Sack</a>&#160;• <a href="/wiki/Storage_Coffin" title="Storage Coffin">Storage Coffin</a>&#160;• <a href="/wiki/Utility_Pouch" title="Utility Pouch">Utility Pouch</a>&#160;• <a href="/wiki/Vineweave_Basket" title="Vineweave Basket">Vineweave Basket</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Food" title="Food">Food</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Banana" title="Banana">Banana</a>&#160;• <a href="/wiki/Big_Bowl_of_Treats" title="Big Bowl of Treats">Big Bowl of Treats</a>&#160;• <a href="/wiki/Blueberries" title="Blueberries">Blueberries</a>&#160;• <a href="/wiki/Carrot" title="Carrot">Carrot</a>&#160;• <a href="/wiki/Cheese" title="Cheese">Cheese</a>&#160;• <a href="/wiki/Chili_Pepper" title="Chili Pepper">Chili Pepper</a>&#160;• <a href="/wiki/Doom_Cap" title="Doom Cap">Doom Cap</a>&#160;• <a href="/wiki/Fly_Agaric" title="Fly Agaric">Fly Agaric</a>&#160;• <a href="/wiki/Garlic" title="Garlic">Garlic</a>&#160;• <a href="/wiki/Gingerbread_Jerry" title="Gingerbread Jerry">Gingerbread Jerry</a>&#160;• <a href="/wiki/Pineapple" title="Pineapple">Pineapple</a>&#160;• <a href="/wiki/Pumpkin" title="Pumpkin">Pumpkin</a>&#160;• <a href="/wiki/Snowcake" title="Snowcake">Snowcake</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Gemstone" title="Gemstone">Gemstone</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Amethyst" title="Amethyst">Amethyst</a>&#160;• <a href="/wiki/Badger_Rune" title="Badger Rune">Badger Rune</a>&#160;• <a href="/wiki/Burning_Coal" title="Burning Coal">Burning Coal</a>&#160;• <a href="/wiki/Corrupted_Crystal" title="Corrupted Crystal">Corrupted Crystal</a>&#160;• <a href="/wiki/Elephant_Rune" title="Elephant Rune">Elephant Rune</a>&#160;• <a href="/wiki/Emerald" title="Emerald">Emerald</a>&#160;• <a href="/wiki/Hawk_Rune" title="Hawk Rune">Hawk Rune</a>&#160;• <a href="/wiki/Lump_of_Coal" title="Lump of Coal">Lump of Coal</a>&#160;• <a href="/wiki/Ruby" title="Ruby">Ruby</a>&#160;• <a href="/wiki/Sapphire" title="Sapphire">Sapphire</a>&#160;• <a href="/wiki/Tim" title="Tim">Tim</a>&#160;• <a href="/wiki/Topaz" title="Topaz">Topaz</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Gloves" title="Gloves">Gloves</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Dragon_Claws" title="Dragon Claws">Dragon Claws</a>&#160;• <a href="/wiki/Gloves_of_Haste" title="Gloves of Haste">Gloves of Haste</a>&#160;• <a href="/wiki/Gloves_of_Power" title="Gloves of Power">Gloves of Power</a>&#160;• <a href="/wiki/Vampiric_Gloves" title="Vampiric Gloves">Vampiric Gloves</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Helmet" title="Helmet">Helmet</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Cap_of_Discomfort" title="Cap of Discomfort">Cap of Discomfort</a>&#160;• <a href="/wiki/Cap_of_Resilience" title="Cap of Resilience">Cap of Resilience</a>&#160;• <a href="/wiki/Glowing_Crown" title="Glowing Crown">Glowing Crown</a>&#160;• <a href="/wiki/Stone_Helm" title="Stone Helm">Stone Helm</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Pet" title="Pet">Pet</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Amethyst_Egg" title="Amethyst Egg">Amethyst Egg</a>&#160;• <a href="/wiki/Armored_Courage_Puppy" title="Armored Courage Puppy">Armored Courage Puppy</a>&#160;• <a href="/wiki/Armored_Power_Puppy" title="Armored Power Puppy">Armored Power Puppy</a>&#160;• <a href="/wiki/Armored_Wisdom_Puppy" title="Armored Wisdom Puppy">Armored Wisdom Puppy</a>&#160;• <a href="/wiki/Blood_Goobert" title="Blood Goobert">Blood Goobert</a>&#160;• <a href="/wiki/Carrot_Goobert" title="Carrot Goobert">Carrot Goobert</a>&#160;• <a href="/wiki/Cheese_Goobert" title="Cheese Goobert">Cheese Goobert</a>&#160;• <a href="/wiki/Chili_Goobert" title="Chili Goobert">Chili Goobert</a>&#160;• <a href="/wiki/Chtulhu" title="Chtulhu">Chtulhu</a>&#160;• <a href="/wiki/Courage_Puppy" title="Courage Puppy">Courage Puppy</a>&#160;• <a href="/wiki/Cubert" title="Cubert">Cubert</a>&#160;• <a href="/wiki/Emerald_Egg" title="Emerald Egg">Emerald Egg</a>&#160;• <a href="/wiki/Emerald_Whelp" title="Emerald Whelp">Emerald Whelp</a>&#160;• <a href="/wiki/Goobert" title="Goobert">Goobert</a>&#160;• <a href="/wiki/Goobling" title="Goobling">Goobling</a>&#160;• <a href="/wiki/Hedgehog" title="Hedgehog">Hedgehog</a>&#160;• <a href="/wiki/Ice_Dragon" title="Ice Dragon">Ice Dragon</a>&#160;• <a href="/wiki/Jynx_torquilla" title="Jynx torquilla">Jynx torquilla</a>&#160;• <a href="/wiki/King_Goobert" title="King Goobert">King Goobert</a>&#160;• <a href="/wiki/Light_Goobert" title="Light Goobert">Light Goobert</a>&#160;• <a href="/wiki/Obsidian_Dragon" title="Obsidian Dragon">Obsidian Dragon</a>&#160;• <a href="/wiki/Phoenix" title="Phoenix">Phoenix</a>&#160;• <a href="/wiki/Poison_Goobert" title="Poison Goobert">Poison Goobert</a>&#160;• <a href="/wiki/Pop" title="Pop">Pop</a>&#160;• <a href="/wiki/Power_Puppy" title="Power Puppy">Power Puppy</a>&#160;• <a href="/wiki/Rainbow_Goobert_Deathslushy_Mansquisher" title="Rainbow Goobert Deathslushy Mansquisher">Rainbow Goobert Deathslushy Mansquisher</a>&#160;• <a href="/wiki/Rainbow_Goobert_Epicglob_Uberviscous" title="Rainbow Goobert Epicglob Uberviscous">Rainbow Goobert Epicglob Uberviscous</a>&#160;• <a href="/wiki/Rainbow_Goobert_Megasludge_Alphapuddle" title="Rainbow Goobert Megasludge Alphapuddle">Rainbow Goobert Megasludge Alphapuddle</a>&#160;• <a href="/wiki/Rainbow_Goobert_Omegaooze_Primeslime" title="Rainbow Goobert Omegaooze Primeslime">Rainbow Goobert Omegaooze Primeslime</a>&#160;• <a href="/wiki/Rat" title="Rat">Rat</a>&#160;• <a href="/wiki/Rat_Chef" title="Rat Chef">Rat Chef</a>&#160;• <a href="/wiki/Ruby_Chonk" title="Ruby Chonk">Ruby Chonk</a>&#160;• <a href="/wiki/Ruby_Egg" title="Ruby Egg">Ruby Egg</a>&#160;• <a href="/wiki/Ruby_Whelp" title="Ruby Whelp">Ruby Whelp</a>&#160;• <a href="/wiki/Sapphire_Egg" title="Sapphire Egg">Sapphire Egg</a>&#160;• <a href="/wiki/Sapphire_Whelp" title="Sapphire Whelp">Sapphire Whelp</a>&#160;• <a href="/wiki/Snake" title="Snake">Snake</a>&#160;• <a href="/wiki/Squirrel" title="Squirrel">Squirrel</a>&#160;• <a href="/wiki/Squirrel_Archer" title="Squirrel Archer">Squirrel Archer</a>&#160;• <a href="/wiki/Steel_Goobert" title="Steel Goobert">Steel Goobert</a>&#160;• <a href="/wiki/Stone_Golem" title="Stone Golem">Stone Golem</a>&#160;• <a href="/wiki/Toad" title="Toad">Toad</a>&#160;• <a href="/wiki/Unsettling_Presence" title="Unsettling Presence">Unsettling Presence</a>&#160;• <a href="/wiki/Wisdom_Puppy" title="Wisdom Puppy">Wisdom Puppy</a>&#160;• <a href="/wiki/Wolpertinger" title="Wolpertinger">Wolpertinger</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Playing_Card" title="Playing Card">Playing Card</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Ace_of_Spades" title="Ace of Spades">Ace of Spades</a>&#160;• <a href="/wiki/Darkest_Lotus" title="Darkest Lotus">Darkest Lotus</a>&#160;• <a href="/wiki/Holo_Fire_Lizard" title="Holo Fire Lizard">Holo Fire Lizard</a>&#160;• <a href="/wiki/Jimbo" title="Jimbo">Jimbo</a>&#160;• <a href="/wiki/Reverse!" title="Reverse!">Reverse!</a>&#160;• <a href="/wiki/The_Fool" title="The Fool">The Fool</a>&#160;• <a href="/wiki/The_Lovers" title="The Lovers">The Lovers</a>&#160;• <a href="/wiki/White-Eyes_Blue_Dragon" title="White-Eyes Blue Dragon">White-Eyes Blue Dragon</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Potion" title="Potion">Potion</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Demonic_Flask" title="Demonic Flask">Demonic Flask</a>&#160;• <a href="/wiki/Divine_Potion" title="Divine Potion">Divine Potion</a>&#160;• <a href="/wiki/Health_Potion" title="Health Potion">Health Potion</a>&#160;• <a href="/wiki/Heroic_Potion" title="Heroic Potion">Heroic Potion</a>&#160;• <a href="/wiki/Mana_Potion" title="Mana Potion">Mana Potion</a>&#160;• <a href="/wiki/Pestilence_Flask" title="Pestilence Flask">Pestilence Flask</a>&#160;• <a href="/wiki/Stone_Skin_Potion" title="Stone Skin Potion">Stone Skin Potion</a>&#160;• <a href="/wiki/Strong_Demonic_Flask" title="Strong Demonic Flask">Strong Demonic Flask</a>&#160;• <a href="/wiki/Strong_Divine_Potion" title="Strong Divine Potion">Strong Divine Potion</a>&#160;• <a href="/wiki/Strong_Health_Potion" title="Strong Health Potion">Strong Health Potion</a>&#160;• <a href="/wiki/Strong_Heroic_Potion" title="Strong Heroic Potion">Strong Heroic Potion</a>&#160;• <a href="/wiki/Strong_Mana_Potion" title="Strong Mana Potion">Strong Mana Potion</a>&#160;• <a href="/wiki/Strong_Pestilence_Flask" title="Strong Pestilence Flask">Strong Pestilence Flask</a>&#160;• <a href="/wiki/Strong_Stone_Skin_Potion" title="Strong Stone Skin Potion">Strong Stone Skin Potion</a>&#160;• <a href="/wiki/Strong_Vampiric_Potion" title="Strong Vampiric Potion">Strong Vampiric Potion</a>&#160;• <a href="/wiki/Vampiric_Potion" title="Vampiric Potion">Vampiric Potion</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Shield" title="Shield">Shield</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Frozen_Buckler" title="Frozen Buckler">Frozen Buckler</a>&#160;• <a href="/wiki/Moon_Shield" title="Moon Shield">Moon Shield</a>&#160;• <a href="/wiki/Shield_of_Valor" title="Shield of Valor">Shield of Valor</a>&#160;• <a href="/wiki/Spiked_Shield" title="Spiked Shield">Spiked Shield</a>&#160;• <a href="/wiki/Sun_Shield" title="Sun Shield">Sun Shield</a>&#160;• <a href="/wiki/Wooden_Buckler" title="Wooden Buckler">Wooden Buckler</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Shoes" title="Shoes">Shoes</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Dragonskin_Boots" title="Dragonskin Boots">Dragonskin Boots</a>&#160;• <a href="/wiki/Leather_Boots" title="Leather Boots">Leather Boots</a>&#160;• <a href="/wiki/Stone_Shoes" title="Stone Shoes">Stone Shoes</a>&#160;• <a href="/wiki/Winged_Boots" title="Winged Boots">Winged Boots</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Weapon" title="Weapon">Weapon</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Amethyst_Whelp" title="Amethyst Whelp">Amethyst Whelp</a>&#160;• <a href="/wiki/Armored_Courage_Puppy" title="Armored Courage Puppy">Armored Courage Puppy</a>&#160;• <a href="/wiki/Artifact_Stone:_Cold" title="Artifact Stone: Cold">Artifact Stone: Cold</a>&#160;• <a href="/wiki/Artifact_Stone:_Death" title="Artifact Stone: Death">Artifact Stone: Death</a>&#160;• <a href="/wiki/Artifact_Stone:_Heat" title="Artifact Stone: Heat">Artifact Stone: Heat</a>&#160;• <a href="/wiki/Axe" title="Axe">Axe</a>&#160;• <a href="/wiki/Belladonna%27s_Shade" title="Belladonna&#39;s Shade">Belladonna's Shade</a>&#160;• <a href="/wiki/Belladonna%27s_Whisper" title="Belladonna&#39;s Whisper">Belladonna's Whisper</a>&#160;• <a href="/wiki/Blood_Harvester" title="Blood Harvester">Blood Harvester</a>&#160;• <a href="/wiki/Bloodthorne" title="Bloodthorne">Bloodthorne</a>&#160;• <a href="/wiki/Bloody_Dagger" title="Bloody Dagger">Bloody Dagger</a>&#160;• <a href="/wiki/Bow_and_Arrow" title="Bow and Arrow">Bow and Arrow</a>&#160;• <a href="/wiki/Brass_Knuckles" title="Brass Knuckles">Brass Knuckles</a>&#160;• <a href="/wiki/Broom" title="Broom">Broom</a>&#160;• <a href="/wiki/Burning_Blade" title="Burning Blade">Burning Blade</a>&#160;• <a href="/wiki/Burning_Sword" title="Burning Sword">Burning Sword</a>&#160;• <a href="/wiki/Burning_Torch" title="Burning Torch">Burning Torch</a>&#160;• <a href="/wiki/Busted_Blade" title="Busted Blade">Busted Blade</a>&#160;• <a href="/wiki/Chain_Whip" title="Chain Whip">Chain Whip</a>&#160;• <a href="/wiki/Claws_of_Attack" title="Claws of Attack">Claws of Attack</a>&#160;• <a href="/wiki/Courage_Puppy" title="Courage Puppy">Courage Puppy</a>&#160;• <a href="/wiki/Critwood_Staff" title="Critwood Staff">Critwood Staff</a>&#160;• <a href="/wiki/Crossblades" title="Crossblades">Crossblades</a>&#160;• <a href="/wiki/Cursed_Dagger" title="Cursed Dagger">Cursed Dagger</a>&#160;• <a href="/wiki/Dagger" title="Dagger">Dagger</a>&#160;• <a href="/wiki/Dancing_Dragon" title="Dancing Dragon">Dancing Dragon</a>&#160;• <a href="/wiki/Darksaber" title="Darksaber">Darksaber</a>&#160;• <a href="/wiki/Death_Scythe" title="Death Scythe">Death Scythe</a>&#160;• <a href="/wiki/Double_Axe" title="Double Axe">Double Axe</a>&#160;• <a href="/wiki/Eggscalibur" title="Eggscalibur">Eggscalibur</a>&#160;• <a href="/wiki/Emerald_Whelp" title="Emerald Whelp">Emerald Whelp</a>&#160;• <a href="/wiki/Falcon_Blade" title="Falcon Blade">Falcon Blade</a>&#160;• <a href="/wiki/Fancy_Fencing_Rapier" title="Fancy Fencing Rapier">Fancy Fencing Rapier</a>&#160;• <a href="/wiki/Flame_Whip" title="Flame Whip">Flame Whip</a>&#160;• <a href="/wiki/Forging_Hammer" title="Forging Hammer">Forging Hammer</a>&#160;• <a href="/wiki/Fortuna%27s_Grace" title="Fortuna&#39;s Grace">Fortuna's Grace</a>&#160;• <a href="/wiki/Fortuna%27s_Hope" title="Fortuna&#39;s Hope">Fortuna's Hope</a>&#160;• <a href="/wiki/Frostbite" title="Frostbite">Frostbite</a>&#160;• <a href="/wiki/Hammer" title="Hammer">Hammer</a>&#160;• <a href="/wiki/Hero_Longsword" title="Hero Longsword">Hero Longsword</a>&#160;• <a href="/wiki/Hero_Sword" title="Hero Sword">Hero Sword</a>&#160;• <a href="/wiki/Holy_Spear" title="Holy Spear">Holy Spear</a>&#160;• <a href="/wiki/Hungry_Blade" title="Hungry Blade">Hungry Blade</a>&#160;• <a href="/wiki/Ice_Dragon" title="Ice Dragon">Ice Dragon</a>&#160;• <a href="/wiki/Impractically_Large_Greatsword" title="Impractically Large Greatsword">Impractically Large Greatsword</a>&#160;• <a href="/wiki/Katana" title="Katana">Katana</a>&#160;• <a href="/wiki/Lightsaber" title="Lightsaber">Lightsaber</a>&#160;• <a href="/wiki/Magic_Staff" title="Magic Staff">Magic Staff</a>&#160;• <a href="/wiki/Magic_Torch" title="Magic Torch">Magic Torch</a>&#160;• <a href="/wiki/Manathirst" title="Manathirst">Manathirst</a>&#160;• <a href="/wiki/Molten_Dagger" title="Molten Dagger">Molten Dagger</a>&#160;• <a href="/wiki/Molten_Spear" title="Molten Spear">Molten Spear</a>&#160;• <a href="/wiki/Obsidian_Dragon" title="Obsidian Dragon">Obsidian Dragon</a>&#160;• <a href="/wiki/Pan" title="Pan">Pan</a>&#160;• <a href="/wiki/Pandamonium" title="Pandamonium">Pandamonium</a>&#160;• <a href="/wiki/Phoenix" title="Phoenix">Phoenix</a>&#160;• <a href="/wiki/Poison_Dagger" title="Poison Dagger">Poison Dagger</a>&#160;• <a href="/wiki/Pop" title="Pop">Pop</a>&#160;• <a href="/wiki/Prismatic_Sword" title="Prismatic Sword">Prismatic Sword</a>&#160;• <a href="/wiki/Pumpkin" title="Pumpkin">Pumpkin</a>&#160;• <a href="/wiki/Ripsaw_Blade" title="Ripsaw Blade">Ripsaw Blade</a>&#160;• <a href="/wiki/Ruby_Chonk" title="Ruby Chonk">Ruby Chonk</a>&#160;• <a href="/wiki/Ruby_Whelp" title="Ruby Whelp">Ruby Whelp</a>&#160;• <a href="/wiki/Sapphire_Whelp" title="Sapphire Whelp">Sapphire Whelp</a>&#160;• <a href="/wiki/Serpent_Staff" title="Serpent Staff">Serpent Staff</a>&#160;• <a href="/wiki/Shell_Totem" title="Shell Totem">Shell Totem</a>&#160;• <a href="/wiki/Shortbow" title="Shortbow">Shortbow</a>&#160;• <a href="/wiki/Shovel" title="Shovel">Shovel</a>&#160;• <a href="/wiki/Snow_Stick" title="Snow Stick">Snow Stick</a>&#160;• <a href="/wiki/Spear" title="Spear">Spear</a>&#160;• <a href="/wiki/Spectral_Dagger" title="Spectral Dagger">Spectral Dagger</a>&#160;• <a href="/wiki/Spiked_Staff" title="Spiked Staff">Spiked Staff</a>&#160;• <a href="/wiki/Squirrel_Archer" title="Squirrel Archer">Squirrel Archer</a>&#160;• <a href="/wiki/Staff_of_Fire" title="Staff of Fire">Staff of Fire</a>&#160;• <a href="/wiki/Staff_of_Unhealing" title="Staff of Unhealing">Staff of Unhealing</a>&#160;• <a href="/wiki/Stone" title="Stone">Stone</a>&#160;• <a href="/wiki/Stone_Golem" title="Stone Golem">Stone Golem</a>&#160;• <a href="/wiki/Thorn_Whip" title="Thorn Whip">Thorn Whip</a>&#160;• <a href="/wiki/Thornbloom" title="Thornbloom">Thornbloom</a>&#160;• <a href="/wiki/Torch" title="Torch">Torch</a>&#160;• <a href="/wiki/Tusk_Piercer" title="Tusk Piercer">Tusk Piercer</a>&#160;• <a href="/wiki/Tusk_Poker" title="Tusk Poker">Tusk Poker</a>&#160;• <a href="/wiki/Villain_Sword" title="Villain Sword">Villain Sword</a>&#160;• <a href="/wiki/Wooden_Sword" title="Wooden Sword">Wooden Sword</a></div></td></tr></tbody></table>
<!-- 
NewPP limit report
Cached time: 20250419133425
Cache expiry: 2592000
Reduced expiry: false
Complications: []
CPU time usage: 1.523 seconds
Real time usage: 3.975 seconds
Preprocessor visited node count: 10780/1000000
Post‐expand include size: 213781/4194304 bytes
Template argument size: 52768/4194304 bytes
Highest expansion depth: 6/100
Expensive parser function count: 0/500
Unstrip recursion depth: 0/20
Unstrip post‐expand size: 8280/10000000 bytes
Lua time usage: 0.007/15.000 seconds
Lua memory usage: 636931/52428800 bytes
Number of processed Cargo queries: 14
Time spent processing Cargo queries: 2.379 ms (avg. 0.170 ms)
Number of Cargo row insertion attempts: 0
-->
<!--
Transclusion expansion time report (%,ms,calls,template)
100.00%  232.816      1 -total
 54.36%  126.570    179 Template:Item_table/class
 40.89%   95.194      1 Template:Items
 39.60%   92.186      1 Template:Navbox
  3.51%    8.168     14 Template:Icon/vampiric
  2.57%    5.984      8 Template:Icon/fire
  2.38%    5.543    179 Template:Icon/gold
  1.10%    2.554     37 Template:Icon/melee
  1.07%    2.488     18 Template:Icon/nature
  1.04%    2.413     25 Template:Icon/magic
-->

<!-- Saved in parser cache with key backpackbattles_en:pcache:idhash:1352-0!canonical and timestamp 20250419133425 and revision id 10321. Rendering was triggered because: page-view
 -->
</div>
<div class="printfooter" data-nosnippet="">Retrieved from "<a dir="ltr" href="https://backpackbattles.wiki.gg/wiki/Neutral_items?oldid=10321">https://backpackbattles.wiki.gg/wiki/Neutral_items?oldid=10321</a>"</div></div>
					<div id="catlinks" class="catlinks" data-mw="interface"><div id="mw-normal-catlinks" class="mw-normal-catlinks"><a href="/wiki/Special:Categories" title="Special:Categories">Category</a>: <ul><li><a href="/wiki/Category:Items" title="Category:Items">Items</a></li></ul></div></div>
				</div>
			</main>
			<aside id="wikigg-sl-rail" data-mw="WggShowcaseLayout">
        <div
            class="wikigg-showcase__unit wikigg-showcase__reserved--mrec"
            data-wgg-unit-type="pubco"
        ><div id="nn_mpu1"></div></div>
        <div
            class="wikigg-showcase__unit wikigg-showcase__reserved--mrec"
            data-wgg-unit-type="internal"
        ></div>
        <div
            class="wikigg-showcase__unit wikigg-showcase__reserved--mrec"
            data-wgg-unit-type="pubco"
        ><div id="nn_mpu2"></div></div>
</aside>

		</div>
		<aside id="wikigg-sl-footer" data-mw="WggShowcaseLayout">
        <div
            class="wikigg-showcase__unit wikigg-showcase__reserved--lb"
            data-wgg-unit-type="internal"
        ></div>
</aside>

	</div>
</div>
<div id='mw-data-after-content'>
	<div class="mw-cookiewarning-container"><div class="mw-cookiewarning-text"><span>Cookies help us deliver our services. By using our services, you agree to our use of cookies.</span></div><form method="POST"><div class='oo-ui-layout oo-ui-horizontalLayout'><span class='oo-ui-widget oo-ui-widget-enabled oo-ui-buttonElement oo-ui-buttonElement-framed oo-ui-labelElement oo-ui-flaggedElement-progressive oo-ui-buttonWidget'><a role='button' tabindex='0' href='https://www.indie.io/privacy-policy' rel='nofollow' class='oo-ui-buttonElement-button'><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon oo-ui-image-progressive'></span><span class='oo-ui-labelElement-label'>More information</span><span class='oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator oo-ui-image-progressive'></span></a></span><span class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-buttonElement oo-ui-buttonElement-framed oo-ui-labelElement oo-ui-flaggedElement-primary oo-ui-flaggedElement-progressive oo-ui-buttonInputWidget'><button type='submit' tabindex='0' name='disablecookiewarning' value='OK' class='oo-ui-inputWidget-input oo-ui-buttonElement-button'><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon oo-ui-image-invert'></span><span class='oo-ui-labelElement-label'>OK</span><span class='oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator oo-ui-image-invert'></span></button></span></div></form></div>
</div>


<footer id="footer" class="mw-footer" >
	<ul id="footer-info">
	<li id="footer-info-lastmod"> This page was last edited on 8 November 2024, at 13:11.</li>
	<li id="footer-info-copyright"><div class="wikigg-copyright-box">Pages that were created prior to November 2023 are adapted from the Backpack Battles Fandom wiki.<br />Page content is under <a class="external" rel="nofollow" href="https://creativecommons.org/licenses/by-sa/4.0">Creative Commons Attribution-ShareAlike 4.0 License</a> unless otherwise noted.</div></li>
</ul>

	<ul id="footer-places">
	<li id="footer-places-wikigg-tos"><a href="https://www.indie.io/terms-of-service">Terms of Service</a></li>
	<li id="footer-places-wikigg-privacy"><a href="https://www.indie.io/privacy-policy">Privacy policy</a></li>
	<li id="footer-places-wikigg-support"><a href="https://support.wiki.gg">Support Wiki</a></li>
	<li id="footer-places-wikigg-servicedesk"><a href="https://wiki.gg/go/servicedesk">Send a ticket to wiki.gg</a></li>
	<li id="footer-places-wikigg-statuspage"><a href="https://wikiggstatus.com">Status page</a></li>
	<li id="footer-places-wikigg-pcmp"><a href="#" class="nn-cmp-show" style="display: none">Manage cookie settings</a></li>
</ul>

	<ul id="footer-icons" class="noprint">
	<li id="footer-copyrightico"><a href="https://creativecommons.org/licenses/by-sa/4.0" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><img src="https://commons.wiki.gg/images/1/14/CC-BY-SA_footer_badge.svg?d931d3" alt="Creative Commons Attribution-ShareAlike 4.0 License" height="31" width="88" loading="lazy" data-theme="light"><img src="https://commons.wiki.gg/images/7/71/CC-BY-SA_footer_badge_dark.svg?55845c" alt="Creative Commons Attribution-ShareAlike 4.0 License" height="31" width="88" loading="lazy" data-theme="dark"></a></li>
	<li id="footer-poweredbyico"><a href="https://www.mediawiki.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><img src="/mw-1.43/resources/assets/poweredby_mediawiki.svg" alt="Powered by MediaWiki" width="88" height="31" loading="lazy" data-theme="light"><img src="https://commons.wiki.gg/images/1/1c/MediaWiki_footer_badge_dark.svg?12ec0a" alt="Powered by MediaWiki" width="88" height="31" loading="lazy" data-theme="dark"></a></li>
	<li id="footer-partofico"><a href="https://wiki.gg" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><img src="https://commons.wiki.gg/images/d/d1/Network_footer_badge.svg?9d5a96" alt="Part of wiki.gg" height="31" width="88" loading="lazy" data-theme="light"><img src="https://commons.wiki.gg/images/2/23/Network_footer_badge_dark.svg?9cf3e8" alt="Part of wiki.gg" height="31" width="88" loading="lazy" data-theme="dark"></a></li>
</ul>

</footer>


<script>(RLQ=window.RLQ||[]).push(function(){mw.config.set({"wgBackendResponseTime":363,"wgPageParseReport":{"limitreport":{"cputime":"1.523","walltime":"3.975","ppvisitednodes":{"value":10780,"limit":1000000},"postexpandincludesize":{"value":213781,"limit":4194304},"templateargumentsize":{"value":52768,"limit":4194304},"expansiondepth":{"value":6,"limit":100},"expensivefunctioncount":{"value":0,"limit":500},"unstrip-depth":{"value":0,"limit":20},"unstrip-size":{"value":8280,"limit":10000000},"timingprofile":["100.00%  232.816      1 -total"," 54.36%  126.570    179 Template:Item_table/class"," 40.89%   95.194      1 Template:Items"," 39.60%   92.186      1 Template:Navbox","  3.51%    8.168     14 Template:Icon/vampiric","  2.57%    5.984      8 Template:Icon/fire","  2.38%    5.543    179 Template:Icon/gold","  1.10%    2.554     37 Template:Icon/melee","  1.07%    2.488     18 Template:Icon/nature","  1.04%    2.413     25 Template:Icon/magic"]},"scribunto":{"limitreport-timeusage":{"value":"0.007","limit":"15.000"},"limitreport-memusage":{"value":636931,"limit":52428800}},"librarian":{"limitreport-queries":14,"limitreport-querytime":["2.379 ms (avg. 0.170 ms)"],"limitreport-insertions":0},"cachereport":{"timestamp":"20250419133425","ttl":2592000,"transientcontent":false}}});});</script>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'950260e3bac7b947',t:'MTc0OTk5Mzc2OS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script><script defer src="https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon='{"rayId":"950260e3bac7b947","serverTiming":{"name":{"cfExtPri":true,"cfEdge":true,"cfOrigin":true,"cfL4":true,"cfSpeedBrain":true,"cfCacheStatus":true}},"version":"2025.6.2","token":"26b2c6e7c852417d8f9d11b0c7f02309"}' crossorigin="anonymous"></script>
</body>
</html>