<!DOCTYPE html>
<html class="client-nojs view-unknown theme-none skin-theme-clientpref-day" lang="en" dir="ltr">
<head>
<meta charset="UTF-8">
<title>Ranger items - The Backpack Battles Wiki</title>
<script>(function(){var className="client-js view-unknown theme-none skin-theme-clientpref-day";var cookie=document.cookie.match(/(?:^|; )commons_enmwclientpreferences=([^;]+)/);if(cookie){cookie[1].split('%2C').forEach(function(pref){className=className.replace(new RegExp('(^| )'+pref.replace(/-clientpref-\w+$|[^\w-]+/g,'')+'-clientpref-\\w+( |$)'),'$1'+pref+'$2');});}document.documentElement.className=className;}());RLCONF={"wgBreakFrames":false,"wgSeparatorTransformTable":["",""],"wgDigitTransformTable":["",""],"wgDefaultDateFormat":"dmy","wgMonthNames":["","January","February","March","April","May","June","July","August","September","October","November","December"],"wgRequestId":"68078852872672a5580418b8","wgCanonicalNamespace":"","wgCanonicalSpecialPageName":false,"wgNamespaceNumber":0,"wgPageName":"Ranger_items","wgTitle":"Ranger items","wgCurRevisionId":10436,"wgRevisionId":10436,"wgArticleId":1348,"wgIsArticle":true,"wgIsRedirect":false,"wgAction":"view","wgUserName":null,"wgUserGroups":["*"],"wgCategories":["Items"],"wgPageViewLanguage":"en","wgPageContentLanguage":"en","wgPageContentModel":"wikitext","wgRelevantPageName":"Ranger_items","wgRelevantArticleId":1348,"wgIsProbablyEditable":false,"wgRelevantPageIsProbablyEditable":false,"wgRestrictionEdit":[],"wgRestrictionMove":[],"wgPageFormsTargetName":null,"wgPageFormsAutocompleteValues":[],"wgPageFormsAutocompleteOnAllChars":false,"wgPageFormsFieldProperties":[],"wgPageFormsCargoFields":[],"wgPageFormsDependentFields":[],"wgPageFormsCalendarValues":[],"wgPageFormsCalendarParams":[],"wgPageFormsCalendarHTML":null,"wgPageFormsGridValues":[],"wgPageFormsGridParams":[],"wgPageFormsContLangYes":null,"wgPageFormsContLangNo":null,"wgPageFormsContLangMonths":[],"wgPageFormsHeightForMinimizingInstances":800,"wgPageFormsDelayReload":false,"wgPageFormsShowOnSelect":[],"wgPageFormsScriptPath":"/mw-1.43/extensions/PageForms","edgValues":[],"wgPageFormsEDSettings":null,"wgAmericanDates":false,"wgCargoDefaultQueryLimit":2000,"wgCargoMonthNamesShort":["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],"wgMediaViewerOnClick":true,"wgMediaViewerEnabledByDefault":true,"wgCiteReferencePreviewsActive":true,"wgCheckUserClientHintsHeadersJsApi":["architecture","bitness","brands","fullVersionList","mobile","model","platform","platformVersion"]};
RLSTATE={"ext.globalCssJs.user.styles":"ready","site.styles":"ready","user.styles":"ready","ext.globalCssJs.user":"ready","user":"ready","user.options":"loading","skins.vector.styles.legacy":"ready","wgg.skins.vector.styles.search":"ready","jquery.tablesorter.styles":"ready","jquery.makeCollapsible.styles":"ready","ext.CookieWarning.styles":"ready","oojs-ui-core.styles":"ready","oojs-ui.styles.indicators":"ready","mediawiki.widgets.styles":"ready","oojs-ui-core.icons":"ready","ext.removeredlinks.styles":"ready","ext.usergroupbadges.styles":"ready","ext.globalui.styles":"ready"};RLPAGEMODULES=["ext.cargo.main","site","mediawiki.page.ready","jquery.tablesorter","jquery.makeCollapsible","skins.vector.legacy.js","ext.CookieWarning","ext.removeredlinks","ext.themes.switcher","ext.gadget.interwikiDropdownButton","mmv.bootstrap","ext.checkUser.clientHints","ext.cargo.purge","ext.globalui"];</script>
<script>(RLQ=window.RLQ||[]).push(function(){mw.loader.impl(function(){return["user.options@12s5i",function($,jQuery,require,module){mw.user.tokens.set({"patrolToken":"+\\","watchToken":"+\\","csrfToken":"+\\"});
}];});});</script>
<link rel="stylesheet" href="/load.php?lang=en&amp;modules=ext.CookieWarning.styles%7Cext.globalui.styles%7Cext.removeredlinks.styles%7Cext.usergroupbadges.styles%7Cjquery.makeCollapsible.styles%7Cjquery.tablesorter.styles%7Cmediawiki.widgets.styles%7Coojs-ui-core.icons%2Cstyles%7Coojs-ui.styles.indicators%7Cskins.vector.styles.legacy%7Cwgg.skins.vector.styles.search&amp;only=styles&amp;skin=vector">
<script async="" src="/load.php?lang=en&amp;modules=startup&amp;only=scripts&amp;raw=1&amp;skin=vector"></script>
<meta name="ResourceLoaderDynamicStyles" content="">
<link rel="stylesheet" href="/load.php?lang=en&amp;modules=site.styles&amp;only=styles&amp;skin=vector">
<script async src="/load.php?lang=en&skin=vector&modules=ext.themes.apply&only=scripts&skin=vector&raw=1"></script>
<meta name="generator" content="MediaWiki 1.43.1">
<meta name="robots" content="max-image-preview:standard">
<meta name="format-detection" content="telephone=no">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, minimum-scale=0.25, maximum-scale=5.0">
<style type="text/css">@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/cyrillic-ext/wght/normal.woff2);unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/cyrillic/wght/normal.woff2);unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/latin/wght/normal.woff2);unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/latin-ext/wght/normal.woff2);unicode-range:U+0100-02AF,U+0304,U+0308,U+0329,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/vietnamese/wght/normal.woff2);unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;font-display:swap;}</style>
<link rel="apple-touch-icon" href="/images/4/4a/Site-favicon.ico">
<link rel="icon" href="/images/4/4a/Site-favicon.ico">
<link rel="search" type="application/opensearchdescription+xml" href="/rest.php/v1/search" title="Backpack Battles Wiki (en)">
<link rel="EditURI" type="application/rsd+xml" href="https://backpackbattles.wiki.gg/api.php?action=rsd">
<link rel="canonical" href="https://backpackbattles.wiki.gg/wiki/Ranger_items">
<link rel="license" href="https://creativecommons.org/licenses/by-sa/4.0">
<link rel="alternate" type="application/atom+xml" title="Backpack Battles Wiki Atom feed" href="/wiki/Special:RecentChanges?feed=atom">
	<meta property="og:type" content="article">

	<meta property="og:site_name" content="Backpack Battles Wiki">

	<meta property="og:title" content="Ranger items">

	<meta property="og:url" content="https://backpackbattles.wiki.gg/wiki/Ranger_items">

<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-P68LVFKN');</script>
<!-- End Google Tag Manager -->
</head>
<body class="wgg-dom-version-1_43 skin-vector-legacy mediawiki ltr sitedir-ltr mw-hide-empty-elt ns-0 ns-subject page-Ranger_items rootpage-Ranger_items skin-vector action-view skin--responsive"><header id="wgg-netbar">
	<div class="wgg-netbar__left">
		<div class="wgg-netbar__logo">
			<a href="/" aria-label="wiki.gg">
				<img height=25 alt="wiki.gg logo" src="https://commons.wiki.gg/images/2/27/Network_header_logo.svg?feeba7">
			</a>
		</div>
	</div>

	<nav class="wgg-netbar__user-links" aria-label="Personal tools">
		<div id="p-personal" class="mw-portlet mw-portlet-personal"  >
		    
		    <ul>
		            <li id="pt-createaccount" class="mw-list-item wgg-netbar__desktop-only"><a data-mw="interface" class=""  href="/wiki/Special:CreateAccount?returnto=Ranger+items" title="You are encouraged to create an account and log in; however, it is not mandatory"><span>Create account</span></a></li>
		            <li id="pt-login" class="mw-list-item wgg-netbar__desktop-only"><a data-mw="interface" class=""  href="/wiki/Special:UserLogin?returnto=Ranger+items" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o"><span>Log in</span></a></li>
		    </ul>
		    
		</div>

		<div id="wgg-user-menu-overflow" class="wgg-netbar__dropdown wgg-netbar__mobile-only">
			<input type="checkbox"
				id="wgg-user-menu-overflow-checkbox"
				role="button"
				aria-haspopup="true"
				aria-labelledby="wgg-user-menu-overflow-label"
			>
			<label
				id="wgg-user-menu-overflow-label"
		        for="wgg-user-menu-overflow-checkbox"
				class="wgg-netbar__icon-button"
			><span class="wgg-netbar-icon wgg-netbar-icon--overflow"></span><span>Toggle personal tools menu</span></label>

			<div class="wgg-netbar__dropdown-content">
				<div id="p-user-menu" class="mw-portlet mw-portlet-user-menu"  >
				    
				    <ul>
				            <li id="pt-createaccount-overflow" class="mw-list-item wgg-netbar__mobile-only"><a data-mw="interface" class=""  href="/wiki/Special:CreateAccount?returnto=Ranger+items" title="You are encouraged to create an account and log in; however, it is not mandatory"><span>Create account</span></a></li>
				            <li id="pt-login-overflow" class="mw-list-item wgg-netbar__mobile-only"><a data-mw="interface" class=""  href="/wiki/Special:UserLogin?returnto=Ranger+items" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o"><span>Log in</span></a></li>
				    </ul>
				    
				</div>
			</div>
		</div>
</nav>

</header>

<div id="mw-page-base" class="noprint"></div>
<div id="mw-head-base" class="noprint"></div>
<div class="content-wrapper">
	<div id="mw-navigation">
		<h2>Navigation menu</h2>
		<div id="mw-head">
			<div id="left-navigation">
				
<div id="p-namespaces" class="mw-portlet mw-portlet-namespaces vector-menu-tabs vector-menu-tabs-legacy vectorTabs vector-menu" aria-labelledby="p-namespaces-label"  >
	<h3
		id="p-namespaces-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">Namespaces</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="ca-nstab-main" class="selected mw-list-item"><a href="/wiki/Ranger_items" title="View the content page [c]" accesskey="c"><span>Page</span></a></li><li id="ca-talk" class="new mw-list-item"><a href="/wiki/Talk:Ranger_items?action=edit&amp;redlink=1" rel="discussion" class="new" title="Discussion about the content page (page does not exist) [t]" accesskey="t"><span>Discussion</span></a></li>
		</ul>
		
	</div>
</div>

				
<div id="p-variants" class="mw-portlet mw-portlet-variants emptyPortlet vector-menu-dropdown vector-menu" aria-labelledby="p-variants-label"  >
	<input type="checkbox"
		id="p-variants-checkbox"
		role="button"
		aria-haspopup="true"
		data-event-name="ui.dropdown-p-variants"
		class="vector-menu-checkbox"
		aria-labelledby="p-variants-label"
	>
	<label
		id="p-variants-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">English</span>
	</label>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			
		</ul>
		
	</div>
</div>

			</div>
			<div id="right-navigation">
				
<div id="p-views" class="mw-portlet mw-portlet-views vector-menu-tabs vector-menu-tabs-legacy vectorTabs vector-menu" aria-labelledby="p-views-label"  >
	<h3
		id="p-views-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">Views</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="ca-view" class="selected mw-list-item"><a href="/wiki/Ranger_items"><span>Read</span></a></li><li id="ca-edit" class="mw-list-item"><a href="/wiki/Special:CreateAccount?warning=accountrequiredtoedit&amp;returnto=Ranger_items&amp;returntoquery=action%3Dedit" title="Edit this page [e]" accesskey="e"><span>Sign up to edit</span></a></li><li id="ca-viewsource" class="mw-list-item"><a href="/wiki/Ranger_items?action=edit" title="This page is protected.&#10;You can view its source [e]" accesskey="e"><span>View source</span></a></li><li id="ca-history" class="mw-list-item"><a href="/wiki/Ranger_items?action=history" title="Past revisions of this page [h]" accesskey="h"><span>History</span></a></li>
		</ul>
		
	</div>
</div>

				
<div id="p-cactions" class="mw-portlet mw-portlet-cactions vectorMenu vector-menu-dropdown vector-menu" aria-labelledby="p-cactions-label"  title="More options" >
	<input type="checkbox"
		id="p-cactions-checkbox"
		role="button"
		aria-haspopup="true"
		data-event-name="ui.dropdown-p-cactions"
		class="vector-menu-checkbox"
		aria-labelledby="p-cactions-label"
	>
	<label
		id="p-cactions-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">More</span>
	</label>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="ca-cargo-purge" class="mw-list-item"><a href="/wiki/Ranger_items?action=purge"><span>Purge cache</span></a></li>
		</ul>
		
	</div>
</div>

				
<div id="p-search" role="search" class="vector-search-box-vue  vector-search-box-show-thumbnail vector-search-box-auto-expand-width vector-search-box">
	<h3 >Search</h3>
	<form action="/wiki/Special:Search" id="searchform" class="vector-search-box-form">
		<div id="simpleSearch"
			class="vector-search-box-inner"
			 data-search-loc="header-navigation">
			<input class="vector-search-box-input"
				 type="search" name="search" placeholder="Search Backpack Battles Wiki" aria-label="Search Backpack Battles Wiki" autocapitalize="sentences" spellcheck="false" title="Search Backpack Battles Wiki [f]" accesskey="f" id="searchInput"
			>
			<input id="mw-searchButton"
				 class="searchButton mw-fallbackSearchButton" type="submit" name="fulltext" title="Search the pages for this text" value="Search">
			<input id="searchButton"
				 class="searchButton" type="submit" name="go" title="Go to a page with this exact name if it exists" value="Go">
		</div>
	</form>
</div>

			</div>
		</div>
		
<div id="mw-panel">
	<div id="p-logo" role="banner">
		<a class="mw-wiki-logo" href="/"
			title="Visit the main page"></a>
	</div>
	
<div id="p-Content" class="mw-portlet mw-portlet-Content vector-menu-portal portal vector-menu" aria-labelledby="p-Content-label"  >
	<h3
		id="p-Content-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">Content</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="n-mainpage-description" class="mw-list-item"><a href="/" title="Visit the main page [z]" accesskey="z"><span>Main page</span></a></li><li id="n-Backpack-Battles" class="mw-list-item"><a href="/wiki/Backpack_Battles"><span>Backpack Battles</span></a></li><li id="n-Items" class="mw-list-item"><a href="/wiki/Items"><span>Items</span></a></li><li id="n-Game-Mechanics" class="mw-list-item"><a href="/wiki/Game_Mechanics"><span>Game Mechanics</span></a></li><li id="n-Characters" class="mw-list-item"><a href="/wiki/Characters"><span>Characters</span></a></li><li id="n-Version-History" class="mw-list-item"><a href="/wiki/Version_History"><span>Version History</span></a></li>
		</ul>
		
	</div>
</div>

	
<div id="p-navigation" class="mw-portlet mw-portlet-navigation vector-menu-portal portal vector-menu" aria-labelledby="p-navigation-label"  >
	<h3
		id="p-navigation-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">Navigation</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="n-portal" class="mw-list-item"><a href="/wiki/Backpack_Battles_Wiki:Community_portal" title="About the project, what you can do, where to find things"><span>Community portal</span></a></li><li id="n-recentchanges" class="mw-list-item"><a href="/wiki/Special:RecentChanges" title="A list of recent changes in the wiki [r]" accesskey="r"><span>Recent changes</span></a></li><li id="n-randompage" class="mw-list-item"><a href="/wiki/Special:Random" title="Load a random page [x]" accesskey="x"><span>Random page</span></a></li><li id="n-help-mediawiki" class="mw-list-item"><a href="https://www.mediawiki.org/wiki/Special:MyLanguage/Help:Contents"><span>Help about MediaWiki</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-tb" class="mw-portlet mw-portlet-tb vector-menu-portal portal vector-menu" aria-labelledby="p-tb-label"  >
	<h3
		id="p-tb-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">Tools</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="t-whatlinkshere" class="mw-list-item"><a href="/wiki/Special:WhatLinksHere/Ranger_items" title="A list of all wiki pages that link here [j]" accesskey="j"><span>What links here</span></a></li><li id="t-recentchangeslinked" class="mw-list-item"><a href="/wiki/Special:RecentChangesLinked/Ranger_items" rel="nofollow" title="Recent changes in pages linked from this page"><span>Related changes</span></a></li><li id="t-newpage" class="mw-list-item"><a href="/wiki/Special:NewPage" accesskey="]"><span>New page</span></a></li><li id="t-specialpages" class="mw-list-item"><a href="/wiki/Special:SpecialPages" title="A list of all special pages [q]" accesskey="q"><span>Special pages</span></a></li><li id="t-print" class="mw-list-item"><a href="javascript:print();" rel="alternate" title="Printable version of this page [p]" accesskey="p"><span>Printable version</span></a></li><li id="t-info" class="mw-list-item"><a href="/wiki/Ranger_items?action=info" title="More information about this page"><span>Page information</span></a></li><li id="t-cargopagevalueslink" class="mw-list-item"><a href="/wiki/Ranger_items?action=pagevalues" rel="cargo-pagevalues"><span>Cargo data</span></a></li>
		</ul>
		
	</div>
</div>

	
</div>

	</div>
	<div id="content" class="mw-body" role="main">
		<aside id="wikigg-sl-header" data-mw="WggShowcaseLayout">
        <div
            class="wikigg-showcase__unit wikigg-showcase__reserved--lb"
            data-wgg-unit-type="internal"
        ></div>
</aside>

		<div class="content-body">
			<main>
				<a id="top"></a>
				<div id="siteNotice"></div>
				<div class="mw-indicators">
				</div>
				<h1 id="firstHeading" class="firstHeading mw-first-heading"><span class="mw-page-title-main">Ranger items</span></h1>
				<div id="bodyContent" class="vector-body">
					<div id="siteSub" class="noprint">From Backpack Battles Wiki</div>
					<div id="contentSub"></div>
					<div id="contentSub2"></div>
					
					<div id="jump-to-nav"></div>
					<a class="mw-jump-link" href="#mw-head">Jump to navigation</a>
					<a class="mw-jump-link" href="#searchInput">Jump to search</a>
					<div id="mw-content-text" class="mw-body-content"><div class="mw-content-ltr mw-parser-output" lang="en" dir="ltr"><table class="styled items nostyle-list sortable">
<tbody><tr>
<th class="unsortable">
</th>
<th><b>Name</b>
</th>
<th class="unsortable"><b>Effect</b>
</th>
<th><b>Item Type</b>
</th>
<th><b>Rarity</b>
</th>
<th><b>Cost</b>
</th></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:AcornAce.png" class="image"><img alt="AcornAce.png" src="/images/thumb/9/96/AcornAce.png/79px-AcornAce.png?f89a68" decoding="async" loading="lazy" width="79" height="100" data-file-width="116" data-file-height="147" /></a></div></div>
</td>
<td><a href="/wiki/Acorn_Ace" title="Acorn Ace">Acorn Ace</a>
</td>
<td>
<ul><li><a href="/wiki/Acorn_Collar" title="Acorn Collar">Acorn Collars</a> have more <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> slots.</li>
<li>Items affected by <a href="/wiki/Acorn_Collar" title="Acorn Collar">Acorn Collar</a> use -8% stamina.</li>
<li><a href="/wiki/Critwood_Staff" title="Critwood Staff">Critwood Staffs</a> use -75% stamina.</li></ul>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Skill" title="Skill">Skill</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:AcornCollar.png" class="image"><img alt="AcornCollar.png" src="/images/thumb/f/f5/AcornCollar.png/100px-AcornCollar.png?70fbf6" decoding="async" loading="lazy" width="100" height="92" data-file-width="142" data-file-height="131" /></a></div></div>
</td>
<td><a href="/wiki/Acorn_Collar" title="Acorn Collar">Acorn Collar</a>
</td>
<td>
<p><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> items gain 5% <a href="/wiki/Critical_hits" title="Critical hits">critical</a> hit chance for each <a href="/wiki/Luck" title="Luck"><img alt="Luck" src="/images/thumb/f/f0/Luck.png/15px-Luck.png?9ec9f2" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.
</p>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>6 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:BelladonnasShade.png" class="image"><img alt="BelladonnasShade.png" src="/images/thumb/b/bf/BelladonnasShade.png/49px-BelladonnasShade.png?dcd99a" decoding="async" loading="lazy" width="49" height="100" data-file-width="173" data-file-height="353" /></a></div></div>
</td>
<td><a href="/wiki/Belladonna%27s_Shade" title="Belladonna&#39;s Shade">Belladonna&#39;s Shade</a>
</td>
<td>
<p><b>On hit:</b> 70% chance to inflict 2 <a href="/wiki/Poison" title="Poison"><img alt="Poison" src="/images/thumb/0/05/Poison.png/15px-Poison.png?984131" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> and a random <a href="/wiki/Debuff" title="Debuff">debuff</a>.
</p>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png?c8ee7a" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>11 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:BelladonnasWhisper.png" class="image"><img alt="BelladonnasWhisper.png" src="/images/thumb/a/ac/BelladonnasWhisper.png/97px-BelladonnasWhisper.png?85cfad" decoding="async" loading="lazy" width="97" height="100" data-file-width="139" data-file-height="143" /></a></div></div>
</td>
<td><a href="/wiki/Belladonna%27s_Whisper" title="Belladonna&#39;s Whisper">Belladonna&#39;s Whisper</a>
</td>
<td>
<ul><li><b>For every 5 damage <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Weapon" title="Weapon">Weapon</a> deals:</b> Inflict +1 <a href="/wiki/Poison" title="Poison"><img alt="Poison" src="/images/thumb/0/05/Poison.png/15px-Poison.png?984131" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> on the next attack.</li>
<li>Deals +0.5 damage per <a href="/wiki/Poison" title="Poison"><img alt="Poison" src="/images/thumb/0/05/Poison.png/15px-Poison.png?984131" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> of your opponent.</li></ul>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png?c8ee7a" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>14 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:BigBowlofTreats.png" class="image"><img alt="BigBowlofTreats.png" src="/images/thumb/c/c3/BigBowlofTreats.png/100px-BigBowlofTreats.png?e9203e" decoding="async" loading="lazy" width="100" height="71" data-file-width="270" data-file-height="193" /></a></div></div>
</td>
<td><a href="/wiki/Big_Bowl_of_Treats" title="Big Bowl of Treats">Big Bowl of Treats</a>
</td>
<td>
<ul><li><b>Every 3.7s:</b> Gain 2 random <a href="/wiki/Buff" title="Buff">buffs</a> and make <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Food" title="Food">Food</a> trigger 25% faster (up to 100%).</li>
<li>All your <a href="/wiki/Pet" title="Pet">Pets</a> have a 20% chance to activate twice.</li>
<li>Friends of the forest are offered in the shop.</li></ul>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Food" title="Food">Food</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>10 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:BowandArrow.png" class="image"><img alt="BowandArrow.png" src="/images/thumb/7/7d/BowandArrow.png/100px-BowandArrow.png?fbd333" decoding="async" loading="lazy" width="100" height="98" data-file-width="458" data-file-height="451" /></a></div></div>
</td>
<td><a href="/wiki/Bow_and_Arrow" title="Bow and Arrow">Bow and Arrow</a>
</td>
<td>
<p><b><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Weapon" title="Weapon">Weapon</a> hits:</b> Gain gain +1 damage (up to 7).
</p>
</td>
<td><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png?c8ee7a" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>7 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Carrot.png" class="image"><img alt="Carrot.png" src="/images/thumb/c/c3/Carrot.png/100px-Carrot.png?39787a" decoding="async" loading="lazy" width="100" height="32" data-file-width="327" data-file-height="105" /></a></div></div>
</td>
<td><a href="/wiki/Carrot" title="Carrot">Carrot</a>
</td>
<td>
<p><b>Every 3.2s:</b> Cleanse 1 <a href="/wiki/Debuff" title="Debuff">debuff</a>. If you have at least 4 <a href="/wiki/Luck" title="Luck"><img alt="Luck" src="/images/thumb/f/f0/Luck.png/15px-Luck.png?9ec9f2" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>: 55% chance to gain 1 <a href="/wiki/Empower" title="Empower"><img alt="Empower" src="/images/thumb/5/57/Empower.png/15px-Empower.png?31c0e4" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.
</p>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Food" title="Food">Food</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>3 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:CarrotGoobert.png" class="image"><img alt="CarrotGoobert.png" src="/images/thumb/8/88/CarrotGoobert.png/91px-CarrotGoobert.png?15c47d" decoding="async" loading="lazy" width="91" height="100" data-file-width="304" data-file-height="334" /></a></div></div>
</td>
<td><a href="/wiki/Carrot_Goobert" title="Carrot Goobert">Carrot Goobert</a>
</td>
<td>
<p><b>6 <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> item activations:</b> Cleanse 4 random <a href="/wiki/Debuff" title="Debuff">debuffs</a> and gain 2 <a href="/wiki/Empower" title="Empower"><img alt="Empower" src="/images/thumb/5/57/Empower.png/15px-Empower.png?31c0e4" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a> for 6s.
</p>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Pet" title="Pet">Pet</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>12 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:CritwoodStaff.png" class="image"><img alt="CritwoodStaff.png" src="/images/thumb/3/33/CritwoodStaff.png/26px-CritwoodStaff.png?6cdd54" decoding="async" loading="lazy" width="26" height="100" data-file-width="168" data-file-height="640" /></a></div></div>
</td>
<td><a href="/wiki/Critwood_Staff" title="Critwood Staff">Critwood Staff</a>
</td>
<td>
<p><b>On attack:</b> Use 3 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> to deal +7 damage and for the next 1.2s, all your attacks are <a href="/wiki/Critical_hits" title="Critical hits">critical</a>.
</p>
</td>
<td><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png?c8ee7a" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>16 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:FortunasGrace.png" class="image"><img alt="FortunasGrace.png" src="/images/thumb/d/d5/FortunasGrace.png/99px-FortunasGrace.png?a8c7d4" decoding="async" loading="lazy" width="99" height="100" data-file-width="451" data-file-height="454" /></a></div></div>
</td>
<td><a href="/wiki/Fortuna%27s_Grace" title="Fortuna&#39;s Grace">Fortuna&#39;s Grace</a>
</td>
<td>
<ul><li><b>Start of battle:</b> Gain 3 <a href="/wiki/Luck" title="Luck"><img alt="Luck" src="/images/thumb/f/f0/Luck.png/15px-Luck.png?9ec9f2" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li><b><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Weapon" title="Weapon">Weapon</a> <a href="/wiki/Critical_hits" title="Critical hits">crits</a>:</b> Attack twice on the next attack.</li></ul>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png?c8ee7a" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>11 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:FortunasHope.png" class="image"><img alt="FortunasHope.png" src="/images/thumb/8/81/FortunasHope.png/46px-FortunasHope.png?3232da" decoding="async" loading="lazy" width="46" height="100" data-file-width="155" data-file-height="335" /></a></div></div>
</td>
<td><a href="/wiki/Fortuna%27s_Hope" title="Fortuna&#39;s Hope">Fortuna&#39;s Hope</a>
</td>
<td>
<p><b>On hit:</b> 70% chance to gain 1 <a href="/wiki/Luck" title="Luck"><img alt="Luck" src="/images/thumb/f/f0/Luck.png/15px-Luck.png?9ec9f2" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.
</p>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png?c8ee7a" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>8 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Hedgehog.png" class="image"><img alt="Hedgehog.png" src="/images/thumb/c/c4/Hedgehog.png/69px-Hedgehog.png?533403" decoding="async" loading="lazy" width="69" height="100" data-file-width="187" data-file-height="270" /></a></div></div>
</td>
<td><a href="/wiki/Hedgehog" title="Hedgehog">Hedgehog</a>
</td>
<td>
<ul><li><b>Every 5s:</b> Deal 10 <a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png?3b764e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-damage + 0.5 for each <a href="/wiki/Spikes" title="Spikes"><img alt="Spikes" src="/images/thumb/9/9a/Spikes.png/15px-Spikes.png?ea75dd" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li><b>Health drops below 70%:</b> Gain 2 <a href="/wiki/Spikes" title="Spikes"><img alt="Spikes" src="/images/thumb/9/9a/Spikes.png/15px-Spikes.png?ea75dd" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> and 15 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> (once).</li>
<li>Triggers 15% faster for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Pet" title="Pet">Pet</a> or <a href="/wiki/Food" title="Food">Food</a>.</li></ul>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Pet" title="Pet">Pet</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>7 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:LuckyClover.png" class="image"><img alt="LuckyClover.png" src="/images/thumb/1/15/LuckyClover.png/100px-LuckyClover.png?eacc9e" decoding="async" loading="lazy" width="100" height="100" data-file-width="160" data-file-height="160" /></a></div></div>
</td>
<td><a href="/wiki/Lucky_Clover" title="Lucky Clover">Lucky Clover</a>
</td>
<td>
<p><b>Start of battle:</b> Gain 1 <a href="/wiki/Luck" title="Luck"><img alt="Luck" src="/images/thumb/f/f0/Luck.png/15px-Luck.png?9ec9f2" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.
</p>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>2 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:LuckyPiggy.png" class="image"><img alt="LuckyPiggy.png" src="/images/thumb/6/64/LuckyPiggy.png/100px-LuckyPiggy.png?71ab13" decoding="async" loading="lazy" width="100" height="57" data-file-width="291" data-file-height="165" /></a></div></div>
</td>
<td><a href="/wiki/Lucky_Piggy" title="Lucky Piggy">Lucky Piggy</a>
</td>
<td>
<ul><li><b>Shop entered:</b> Gain 1 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />.</li>
<li><b>Start of battle:</b> Gain 2 <a href="/wiki/Luck" title="Luck"><img alt="Luck" src="/images/thumb/f/f0/Luck.png/15px-Luck.png?9ec9f2" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li>Chance-based effects of the <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> items are 15% more likely to trigger.</li></ul>
</td>
<td><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>7 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Markswoman.png" class="image"><img alt="Markswoman.png" src="/images/thumb/e/ea/Markswoman.png/100px-Markswoman.png?922064" decoding="async" loading="lazy" width="100" height="77" data-file-width="173" data-file-height="133" /></a></div></div>
</td>
<td><a href="/wiki/Markswoman" title="Markswoman">Markswoman</a>
</td>
<td>
<p><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png?c8ee7a" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-<a href="/wiki/Weapon" title="Weapon">Weapons</a> deal +20% damage, attack +30% faster and have +15% accuracy.
</p>
</td>
<td><a href="/wiki/Skill" title="Skill">Skill</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:MegaClover.png" class="image"><img alt="MegaClover.png" src="/images/thumb/2/2a/MegaClover.png/99px-MegaClover.png?18a7b3" decoding="async" loading="lazy" width="99" height="100" data-file-width="312" data-file-height="314" /></a></div></div>
</td>
<td><a href="/wiki/Mega_Clover" title="Mega Clover">Mega Clover</a>
</td>
<td>
<ul><li>Sale chance +5%</li>
<li>Chance to find <a href="/wiki/Treasure" title="Treasure"><img alt="Treasure" src="/images/thumb/d/d3/Icon_Treasure.png/15px-Icon_Treasure.png?c8854f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-items +20%.</li>
<li><b>Shop entered:</b> Generate two <a href="/wiki/Lucky_Clover" title="Lucky Clover">Lucky Clovers</a></li>
<li><b>15 <a href="/wiki/Luck" title="Luck"><img alt="Luck" src="/images/thumb/f/f0/Luck.png/15px-Luck.png?9ec9f2" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> reached:</b> Gain 25 random other <a href="/wiki/Buff" title="Buff">buffs</a>.</li></ul>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>10 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:PiercingArrow.png" class="image"><img alt="PiercingArrow.png" src="/images/thumb/1/1e/PiercingArrow.png/100px-PiercingArrow.png?87e6ba" decoding="async" loading="lazy" width="100" height="41" data-file-width="355" data-file-height="146" /></a></div></div>
</td>
<td><a href="/wiki/Piercing_Arrow" title="Piercing Arrow">Piercing Arrow</a>
</td>
<td>
<ul><li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Weapon" title="Weapon">Weapons</a> deal +50% <a href="/wiki/Critical_hits" title="Critical hits">critical</a> damage. They remove 15 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> on <a href="/wiki/Critical_hits" title="Critical hits">crit</a>.</li>
<li><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Diamond" src="/images/thumb/e/ea/Diamond.png/15px-Diamond.png?f49dcb" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <b>Item activates:</b> 65% chance to gain 1 <a href="/wiki/Luck" title="Luck"><img alt="Luck" src="/images/thumb/f/f0/Luck.png/15px-Luck.png?9ec9f2" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li></ul>
</td>
<td><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>10 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:PoisonIvy.png" class="image"><img alt="PoisonIvy.png" src="/images/thumb/0/02/PoisonIvy.png/100px-PoisonIvy.png?827379" decoding="async" loading="lazy" width="100" height="86" data-file-width="184" data-file-height="158" /></a></div></div>
</td>
<td><a href="/wiki/Poison_Ivy" title="Poison Ivy">Poison Ivy</a>
</td>
<td>
<ul><li>You have a 5% chance to <a href="/wiki/Resist" title="Resist">resist</a> <a href="/wiki/Debuff" title="Debuff">debuffs</a> for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-item.</li>
<li><b><a href="/wiki/Spikes" title="Spikes"><img alt="Spikes" src="/images/thumb/9/9a/Spikes.png/15px-Spikes.png?ea75dd" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> gained:</b> Inflict 2 <a href="/wiki/Poison" title="Poison"><img alt="Poison" src="/images/thumb/0/05/Poison.png/15px-Poison.png?984131" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li><b>Opponent reaches 18 <a href="/wiki/Poison" title="Poison"><img alt="Poison" src="/images/thumb/0/05/Poison.png/15px-Poison.png?984131" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>:</b> They take +25% damage.</li></ul>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>10 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:RainbowGoobertMegasludgeAlphapuddle.png" class="image"><img alt="RainbowGoobertMegasludgeAlphapuddle.png" src="/images/thumb/d/d7/RainbowGoobertMegasludgeAlphapuddle.png/97px-RainbowGoobertMegasludgeAlphapuddle.png?ccf123" decoding="async" loading="lazy" width="97" height="100" data-file-width="301" data-file-height="309" /></a></div></div>
</td>
<td><a href="/wiki/Rainbow_Goobert_Megasludge_Alphapuddle" title="Rainbow Goobert Megasludge Alphapuddle">Rainbow Goobert Megasludge Alphapuddle</a>
</td>
<td>
<p><b>6 <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> item activations:</b> <a href="/wiki/Heal" title="Heal">Heal</a> for 20, gain 20 <a href="/wiki/Block" title="Block"><img alt="Block" src="/images/thumb/4/48/Block.png/15px-Block.png?2486c6" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>, 2 <a href="/wiki/Vampirism" title="Vampirism"><img alt="Vampirism" src="/images/thumb/3/33/Vampirism.png/15px-Vampirism.png?823c3c" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> and 2 <a href="/wiki/Empower" title="Empower"><img alt="Empower" src="/images/thumb/5/57/Empower.png/15px-Empower.png?31c0e4" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>, inflict 4 <a href="/wiki/Blind" title="Blind"><img alt="Blind" src="/images/thumb/6/68/Blind.png/15px-Blind.png?f2b891" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>, and <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Weapon" title="Weapon">Weapons</a> gain 4 damage.
</p>
</td>
<td><a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png?8cee0c" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png?3d2225" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Pet" title="Pet">Pet</a>
</td>
<td data-sort-value="5"><a href="/wiki/Godly" title="Godly">Godly</a>
</td>
<td>68 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:RangerBag.png" class="image"><img alt="RangerBag.png" src="/images/thumb/0/0a/RangerBag.png/89px-RangerBag.png?50230b" decoding="async" loading="lazy" width="89" height="100" data-file-width="497" data-file-height="558" /></a></div></div>
</td>
<td><a href="/wiki/Ranger_Bag" title="Ranger Bag">Ranger Bag</a>
</td>
<td>
<ul><li>Add 6 backpack slots.</li>
<li>Items inside gain 10% <a href="/wiki/Critical_hits" title="Critical hits">critical</a> hit chance +3% for each <a href="/wiki/Luck" title="Luck"><img alt="Luck" src="/images/thumb/f/f0/Luck.png/15px-Luck.png?9ec9f2" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li></ul>
</td>
<td><a href="/wiki/Bag" title="Bag">Bag</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>16 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Rat.png" class="image"><img alt="Rat.png" src="/images/thumb/8/8b/Rat.png/100px-Rat.png?eb7de9" decoding="async" loading="lazy" width="100" height="42" data-file-width="324" data-file-height="136" /></a></div></div>
</td>
<td><a href="/wiki/Rat" title="Rat">Rat</a>
</td>
<td>
<ul><li><b>Every 3.3s:</b> Deal 5 <a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png?3b764e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-damage. 75% to inflict 1 <a href="/wiki/Poison" title="Poison"><img alt="Poison" src="/images/thumb/0/05/Poison.png/15px-Poison.png?984131" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>. 10% to inflict 1 <a href="/wiki/Blind" title="Blind"><img alt="Blind" src="/images/thumb/6/68/Blind.png/15px-Blind.png?f2b891" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li>Triggers 15% faster for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Pet" title="Pet">Pet</a> or <a href="/wiki/Food" title="Food">Food</a>.</li></ul>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Pet" title="Pet">Pet</a>
</td>
<td data-sort-value="1"><a href="/wiki/Common" title="Common">Common</a>
</td>
<td>4 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:RatChef.png" class="image"><img alt="RatChef.png" src="/images/thumb/7/75/RatChef.png/50px-RatChef.png?72a300" decoding="async" loading="lazy" width="50" height="100" data-file-width="162" data-file-height="322" /></a></div></div>
</td>
<td><a href="/wiki/Rat_Chef" title="Rat Chef">Rat Chef</a>
</td>
<td>
<ul><li><b>Start of battle:</b> Gain 1 <a href="/wiki/Regeneration" title="Regeneration"><img alt="Regeneration" src="/images/thumb/5/53/Regeneration.png/15px-Regeneration.png?7bd3bc" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Food" title="Food">Food</a>.</li>
<li><b>Every 7s:</b> Regenerate 2 stamina and gain 1 <a href="/wiki/Empower" title="Empower"><img alt="Empower" src="/images/thumb/5/57/Empower.png/15px-Empower.png?31c0e4" decoding="async" loading="lazy" width="15" height="14" data-file-width="100" data-file-height="95" /></a>.</li>
<li>Triggers 15% faster for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Pet" title="Pet">Pet</a> or <a href="/wiki/Food" title="Food">Food</a>.</li></ul>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Pet" title="Pet">Pet</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>8 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Shortbow.png" class="image"><img alt="Shortbow.png" src="/images/thumb/f/f9/Shortbow.png/43px-Shortbow.png?b72e3e" decoding="async" loading="lazy" width="43" height="100" data-file-width="146" data-file-height="335" /></a></div></div>
</td>
<td><a href="/wiki/Shortbow" title="Shortbow">Shortbow</a>
</td>
<td>
</td>
<td><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png?c8ee7a" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="1"><a href="/wiki/Common" title="Common">Common</a>
</td>
<td>4 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:Squirrel.png" class="image"><img alt="Squirrel.png" src="/images/thumb/4/41/Squirrel.png/93px-Squirrel.png?6782f8" decoding="async" loading="lazy" width="93" height="100" data-file-width="310" data-file-height="334" /></a></div></div>
</td>
<td><a href="/wiki/Squirrel" title="Squirrel">Squirrel</a>
</td>
<td>
<ul><li><b>Every 4s:</b> Steal a random <a href="/wiki/Buff" title="Buff">buff</a>.</li>
<li>Triggers 15% faster for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Pet" title="Pet">Pet</a> or <a href="/wiki/Food" title="Food">Food</a>.</li></ul>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Pet" title="Pet">Pet</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>5 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:SquirrelArcher.png" class="image"><img alt="SquirrelArcher.png" src="/images/thumb/a/aa/SquirrelArcher.png/100px-SquirrelArcher.png?73463e" decoding="async" loading="lazy" width="100" height="97" data-file-width="329" data-file-height="320" /></a></div></div>
</td>
<td><a href="/wiki/Squirrel_Archer" title="Squirrel Archer">Squirrel Archer</a>
</td>
<td>
<ul><li><b>On hit:</b> Steal a random <a href="/wiki/Buff" title="Buff">buff</a>.</li>
<li>Triggers 15% faster for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Pet" title="Pet">Pet</a> or <a href="/wiki/Food" title="Food">Food</a>.</li></ul>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png?3b764e" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png?c8ee7a" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>, <a href="/wiki/Pet" title="Pet">Pet</a>
</td>
<td data-sort-value="3"><a href="/wiki/Epic" title="Epic">Epic</a>
</td>
<td>9 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:TuskPiercer.png" class="image"><img alt="TuskPiercer.png" src="/images/thumb/8/87/TuskPiercer.png/100px-TuskPiercer.png?a05e0b" decoding="async" loading="lazy" width="100" height="98" data-file-width="477" data-file-height="466" /></a></div></div>
</td>
<td><a href="/wiki/Tusk_Piercer" title="Tusk Piercer">Tusk Piercer</a>
</td>
<td>
<ul><li><b>Start of battle:</b> Gain 4 <a href="/wiki/Spikes" title="Spikes"><img alt="Spikes" src="/images/thumb/9/9a/Spikes.png/15px-Spikes.png?ea75dd" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.</li>
<li><b><a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Weapon" title="Weapon">Weapon</a> hits:</b> Use 1 <a href="/wiki/Spikes" title="Spikes"><img alt="Spikes" src="/images/thumb/9/9a/Spikes.png/15px-Spikes.png?ea75dd" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> to deal +9 damage on the next attack.</li></ul>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png?c8ee7a" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="4"><a href="/wiki/Legendary" title="Legendary">Legendary</a>
</td>
<td>11 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:TuskPoker.png" class="image"><img alt="TuskPoker.png" src="/images/thumb/b/ba/TuskPoker.png/46px-TuskPoker.png?f3b61d" decoding="async" loading="lazy" width="46" height="100" data-file-width="154" data-file-height="335" /></a></div></div>
</td>
<td><a href="/wiki/Tusk_Poker" title="Tusk Poker">Tusk Poker</a>
</td>
<td>
<p><b>On hit:</b> 50% chance to gain 1 <a href="/wiki/Spikes" title="Spikes"><img alt="Spikes" src="/images/thumb/9/9a/Spikes.png/15px-Spikes.png?ea75dd" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a>.
</p>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png?c8ee7a" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Weapon" title="Weapon">Weapon</a>
</td>
<td data-sort-value="2"><a href="/wiki/Rare" title="Rare">Rare</a>
</td>
<td>8 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:VineweaveBasket.png" class="image"><img alt="VineweaveBasket.png" src="/images/thumb/4/4b/VineweaveBasket.png/99px-VineweaveBasket.png?e6ea94" decoding="async" loading="lazy" width="99" height="100" data-file-width="551" data-file-height="558" /></a></div></div>
</td>
<td><a href="/wiki/Vineweave_Basket" title="Vineweave Basket">Vineweave Basket</a>
</td>
<td>
<ul><li>Add 9 backpack slots.</li>
<li>Your <a href="/wiki/Heal" title="Heal">healing</a> is amplified by 10% + 3% per <a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-item inside.</li>
<li>In rounds 1 and 10, sale chance is increased by 20%.</li></ul>
</td>
<td><a href="/wiki/Bag" title="Bag">Bag</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>20 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr>
<tr>
<td><div class="center"><div class="floatnone"><a href="/wiki/File:YggdrasilLeaf.png" class="image"><img alt="YggdrasilLeaf.png" src="/images/thumb/e/e4/YggdrasilLeaf.png/97px-YggdrasilLeaf.png?cc6ffd" decoding="async" loading="lazy" width="97" height="100" data-file-width="170" data-file-height="175" /></a></div></div>
</td>
<td><a href="/wiki/Yggdrasil_Leaf" title="Yggdrasil Leaf">Yggdrasil Leaf</a>
</td>
<td>
<ul><li><b>Start of battle:</b> Gain 2 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> and 1 <a href="/wiki/Regeneration" title="Regeneration"><img alt="Regeneration" src="/images/thumb/5/53/Regeneration.png/15px-Regeneration.png?7bd3bc" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> for each <a href="/wiki/Game_Mechanics" title="Game Mechanics"><img alt="Star" src="/images/thumb/2/2e/Star.png/15px-Star.png?716865" decoding="async" loading="lazy" width="15" height="15" data-file-width="50" data-file-height="50" /></a> <a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a>-item.</li>
<li><b>5 <a href="/wiki/Mana" title="Mana"><img alt="Mana" src="/images/thumb/e/ed/Mana.png/15px-Mana.png?26a676" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" /></a> used:</b> <a href="/wiki/Heal" title="Heal">Heal</a> for 17 and cleanse 2 <a href="/wiki/Debuff" title="Debuff">debuffs</a>.</li></ul>
</td>
<td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png?bfec41" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56" /></a><a href="/wiki/Accessory" title="Accessory">Accessory</a>
</td>
<td data-sort-value="6"><a href="/wiki/Unique" title="Unique">Unique</a>
</td>
<td>10 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100" />
</td></tr></tbody></table>
<table class="navbox mw-collapsible" data-expandtext="Show" data-collapsetext="Hide"><tbody><tr class="navbox-title"><th colspan="2" scope="col"><span class="navbox-vde"><a href="/wiki/Template:Items" title="Template:Items"><span title="View this navbox template">v</span></a> · <span class="plainlinks"><a class="text" href="https://backpackbattles.wiki.gg/wiki/Template_talk:Items"><span title="Discuss this navbox template">d</span></a> · <a class="text" href="https://backpackbattles.wiki.gg/wiki/Template:Items?action=edit"><span title="Edit this navbox template">e</span></a></span></span><a href="/wiki/Items" title="Items">Items</a></th></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Accessory" title="Accessory">Accessory</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Acorn_Collar" title="Acorn Collar">Acorn Collar</a>&#160;• <a href="/wiki/Amulet_of_Alchemy" title="Amulet of Alchemy">Amulet of Alchemy</a>&#160;• <a href="/wiki/Amulet_of_Darkness" title="Amulet of Darkness">Amulet of Darkness</a>&#160;• <a href="/wiki/Amulet_of_Energy" title="Amulet of Energy">Amulet of Energy</a>&#160;• <a href="/wiki/Amulet_of_Feasting" title="Amulet of Feasting">Amulet of Feasting</a>&#160;• <a href="/wiki/Amulet_of_Life" title="Amulet of Life">Amulet of Life</a>&#160;• <a href="/wiki/Amulet_of_Steel" title="Amulet of Steel">Amulet of Steel</a>&#160;• <a href="/wiki/Amulet_of_the_Wild" title="Amulet of the Wild">Amulet of the Wild</a>&#160;• <a href="/wiki/Anvil" title="Anvil">Anvil</a>&#160;• <a href="/wiki/Bag_of_Stones" title="Bag of Stones">Bag of Stones</a>&#160;• <a href="/wiki/Blood_Amulet" title="Blood Amulet">Blood Amulet</a>&#160;• <a href="/wiki/Book_of_Ice" title="Book of Ice">Book of Ice</a>&#160;• <a href="/wiki/Box_of_Riches" title="Box of Riches">Box of Riches</a>&#160;• <a href="/wiki/Bunch_of_Coins" title="Bunch of Coins">Bunch of Coins</a>&#160;• <a href="/wiki/Burning_Banner" title="Burning Banner">Burning Banner</a>&#160;• <a href="/wiki/Cauldron" title="Cauldron">Cauldron</a>&#160;• <a href="/wiki/Customer_Card" title="Customer Card">Customer Card</a>&#160;• <a href="/wiki/Dark_Lantern" title="Dark Lantern">Dark Lantern</a>&#160;• <a href="/wiki/Deck_of_Cards" title="Deck of Cards">Deck of Cards</a>&#160;• <a href="/wiki/Deerwood_Guardian" title="Deerwood Guardian">Deerwood Guardian</a>&#160;• <a href="/wiki/Djinn_Lamp" title="Djinn Lamp">Djinn Lamp</a>&#160;• <a href="/wiki/Draconic_Orb" title="Draconic Orb">Draconic Orb</a>&#160;• <a href="/wiki/Dragon_Nest" title="Dragon Nest">Dragon Nest</a>&#160;• <a href="/wiki/Fanfare" title="Fanfare">Fanfare</a>&#160;• <a href="/wiki/Flame" title="Flame">Flame</a>&#160;• <a href="/wiki/Flame_Badge" title="Flame Badge">Flame Badge</a>&#160;• <a href="/wiki/Flute" title="Flute">Flute</a>&#160;• <a href="/wiki/Friendly_Fire" title="Friendly Fire">Friendly Fire</a>&#160;• <a href="/wiki/Frozen_Flame" title="Frozen Flame">Frozen Flame</a>&#160;• <a href="/wiki/Healing_Herbs" title="Healing Herbs">Healing Herbs</a>&#160;• <a href="/wiki/Heart_Container" title="Heart Container">Heart Container</a>&#160;• <a href="/wiki/Heart_of_Darkness" title="Heart of Darkness">Heart of Darkness</a>&#160;• <a href="/wiki/King_Crown" title="King Crown">King Crown</a>&#160;• <a href="/wiki/Leaf_Badge" title="Leaf Badge">Leaf Badge</a>&#160;• <a href="/wiki/Lucky_Clover" title="Lucky Clover">Lucky Clover</a>&#160;• <a href="/wiki/Lucky_Piggy" title="Lucky Piggy">Lucky Piggy</a>&#160;• <a href="/wiki/Mana_Orb" title="Mana Orb">Mana Orb</a>&#160;• <a href="/wiki/Maneki-neko" title="Maneki-neko">Maneki-neko</a>&#160;• <a href="/wiki/Mega_Clover" title="Mega Clover">Mega Clover</a>&#160;• <a href="/wiki/Miss_Fortune" title="Miss Fortune">Miss Fortune</a>&#160;• <a href="/wiki/Mr._Struggles" title="Mr. Struggles">Mr. Struggles</a>&#160;• <a href="/wiki/Mrs._Struggles" title="Mrs. Struggles">Mrs. Struggles</a>&#160;• <a href="/wiki/Nocturnal_Lock_Lifter" title="Nocturnal Lock Lifter">Nocturnal Lock Lifter</a>&#160;• <a href="/wiki/Oil_Lamp" title="Oil Lamp">Oil Lamp</a>&#160;• <a href="/wiki/Piercing_Arrow" title="Piercing Arrow">Piercing Arrow</a>&#160;• <a href="/wiki/Piggybank" title="Piggybank">Piggybank</a>&#160;• <a href="/wiki/Platinum_Customer_Card" title="Platinum Customer Card">Platinum Customer Card</a>&#160;• <a href="/wiki/Pocket_Sand" title="Pocket Sand">Pocket Sand</a>&#160;• <a href="/wiki/Poison_Ivy" title="Poison Ivy">Poison Ivy</a>&#160;• <a href="/wiki/Present" title="Present">Present</a>&#160;• <a href="/wiki/Prismatic_Orb" title="Prismatic Orb">Prismatic Orb</a>&#160;• <a href="/wiki/Rainbow_Badge" title="Rainbow Badge">Rainbow Badge</a>&#160;• <a href="/wiki/Shaman_Mask" title="Shaman Mask">Shaman Mask</a>&#160;• <a href="/wiki/Shepherd%27s_Crook" class="mw-redirect" title="Shepherd&#39;s Crook">Shepherd's Crook</a>&#160;• <a href="/wiki/Shiny_Shell" title="Shiny Shell">Shiny Shell</a>&#160;• <a href="/wiki/Skull_Badge" title="Skull Badge">Skull Badge</a>&#160;• <a href="/wiki/Snowball" title="Snowball">Snowball</a>&#160;• <a href="/wiki/Spiked_Collar" title="Spiked Collar">Spiked Collar</a>&#160;• <a href="/wiki/Stable_Recombobulator" title="Stable Recombobulator">Stable Recombobulator</a>&#160;• <a href="/wiki/Stone_Badge" title="Stone Badge">Stone Badge</a>&#160;• <a href="/wiki/Time_Dilator" title="Time Dilator">Time Dilator</a>&#160;• <a href="/wiki/Unidentified_Amulet" title="Unidentified Amulet">Unidentified Amulet</a>&#160;• <a href="/wiki/Unstable_Recombobulator" title="Unstable Recombobulator">Unstable Recombobulator</a>&#160;• <a href="/wiki/Walrus_Tusk" title="Walrus Tusk">Walrus Tusk</a>&#160;• <a href="/wiki/Whetstone" title="Whetstone">Whetstone</a>&#160;• <a href="/wiki/Wolf_Badge" title="Wolf Badge">Wolf Badge</a>&#160;• <a href="/wiki/Wolf_Emblem" title="Wolf Emblem">Wolf Emblem</a>&#160;• <a href="/wiki/Wonky_Snowman" title="Wonky Snowman">Wonky Snowman</a>&#160;• <a href="/wiki/Yggdrasil_Leaf" title="Yggdrasil Leaf">Yggdrasil Leaf</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Armor" title="Armor">Armor</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Corrupted_Armor" title="Corrupted Armor">Corrupted Armor</a>&#160;• <a href="/wiki/Dragonscale_Armor" title="Dragonscale Armor">Dragonscale Armor</a>&#160;• <a href="/wiki/Holy_Armor" title="Holy Armor">Holy Armor</a>&#160;• <a href="/wiki/Ice_Armor" title="Ice Armor">Ice Armor</a>&#160;• <a href="/wiki/Leather_Armor" title="Leather Armor">Leather Armor</a>&#160;• <a href="/wiki/Moon_Armor" title="Moon Armor">Moon Armor</a>&#160;• <a href="/wiki/Stone_Armor" title="Stone Armor">Stone Armor</a>&#160;• <a href="/wiki/Sun_Armor" title="Sun Armor">Sun Armor</a>&#160;• <a href="/wiki/Vampiric_Armor" title="Vampiric Armor">Vampiric Armor</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Bag" title="Bag">Bag</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Box_of_Prosperity" title="Box of Prosperity">Box of Prosperity</a>&#160;• <a href="/wiki/Duffle_Bag" title="Duffle Bag">Duffle Bag</a>&#160;• <a href="/wiki/Fanny_Pack" title="Fanny Pack">Fanny Pack</a>&#160;• <a href="/wiki/Fire_Pit" title="Fire Pit">Fire Pit</a>&#160;• <a href="/wiki/Holdall" title="Holdall">Holdall</a>&#160;• <a href="/wiki/Leather_Bag" title="Leather Bag">Leather Bag</a>&#160;• <a href="/wiki/Offering_Bowl" title="Offering Bowl">Offering Bowl</a>&#160;• <a href="/wiki/Potion_Belt" title="Potion Belt">Potion Belt</a>&#160;• <a href="/wiki/Protective_Purse" title="Protective Purse">Protective Purse</a>&#160;• <a href="/wiki/Ranger_Bag" title="Ranger Bag">Ranger Bag</a>&#160;• <a href="/wiki/Relic_Case" title="Relic Case">Relic Case</a>&#160;• <a href="/wiki/Sack_of_Surprises" title="Sack of Surprises">Sack of Surprises</a>&#160;• <a href="/wiki/Stamina_Sack" title="Stamina Sack">Stamina Sack</a>&#160;• <a href="/wiki/Storage_Coffin" title="Storage Coffin">Storage Coffin</a>&#160;• <a href="/wiki/Utility_Pouch" title="Utility Pouch">Utility Pouch</a>&#160;• <a href="/wiki/Vineweave_Basket" title="Vineweave Basket">Vineweave Basket</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Food" title="Food">Food</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Banana" title="Banana">Banana</a>&#160;• <a href="/wiki/Big_Bowl_of_Treats" title="Big Bowl of Treats">Big Bowl of Treats</a>&#160;• <a href="/wiki/Blueberries" title="Blueberries">Blueberries</a>&#160;• <a href="/wiki/Carrot" title="Carrot">Carrot</a>&#160;• <a href="/wiki/Cheese" title="Cheese">Cheese</a>&#160;• <a href="/wiki/Chili_Pepper" title="Chili Pepper">Chili Pepper</a>&#160;• <a href="/wiki/Doom_Cap" title="Doom Cap">Doom Cap</a>&#160;• <a href="/wiki/Fly_Agaric" title="Fly Agaric">Fly Agaric</a>&#160;• <a href="/wiki/Garlic" title="Garlic">Garlic</a>&#160;• <a href="/wiki/Gingerbread_Jerry" title="Gingerbread Jerry">Gingerbread Jerry</a>&#160;• <a href="/wiki/Pineapple" title="Pineapple">Pineapple</a>&#160;• <a href="/wiki/Pumpkin" title="Pumpkin">Pumpkin</a>&#160;• <a href="/wiki/Snowcake" title="Snowcake">Snowcake</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Gemstone" title="Gemstone">Gemstone</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Amethyst" title="Amethyst">Amethyst</a>&#160;• <a href="/wiki/Badger_Rune" title="Badger Rune">Badger Rune</a>&#160;• <a href="/wiki/Burning_Coal" title="Burning Coal">Burning Coal</a>&#160;• <a href="/wiki/Corrupted_Crystal" title="Corrupted Crystal">Corrupted Crystal</a>&#160;• <a href="/wiki/Elephant_Rune" title="Elephant Rune">Elephant Rune</a>&#160;• <a href="/wiki/Emerald" title="Emerald">Emerald</a>&#160;• <a href="/wiki/Hawk_Rune" title="Hawk Rune">Hawk Rune</a>&#160;• <a href="/wiki/Lump_of_Coal" title="Lump of Coal">Lump of Coal</a>&#160;• <a href="/wiki/Ruby" title="Ruby">Ruby</a>&#160;• <a href="/wiki/Sapphire" title="Sapphire">Sapphire</a>&#160;• <a href="/wiki/Tim" title="Tim">Tim</a>&#160;• <a href="/wiki/Topaz" title="Topaz">Topaz</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Gloves" title="Gloves">Gloves</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Dragon_Claws" title="Dragon Claws">Dragon Claws</a>&#160;• <a href="/wiki/Gloves_of_Haste" title="Gloves of Haste">Gloves of Haste</a>&#160;• <a href="/wiki/Gloves_of_Power" title="Gloves of Power">Gloves of Power</a>&#160;• <a href="/wiki/Vampiric_Gloves" title="Vampiric Gloves">Vampiric Gloves</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Helmet" title="Helmet">Helmet</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Cap_of_Discomfort" title="Cap of Discomfort">Cap of Discomfort</a>&#160;• <a href="/wiki/Cap_of_Resilience" title="Cap of Resilience">Cap of Resilience</a>&#160;• <a href="/wiki/Glowing_Crown" title="Glowing Crown">Glowing Crown</a>&#160;• <a href="/wiki/Stone_Helm" title="Stone Helm">Stone Helm</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Pet" title="Pet">Pet</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Amethyst_Egg" title="Amethyst Egg">Amethyst Egg</a>&#160;• <a href="/wiki/Armored_Courage_Puppy" title="Armored Courage Puppy">Armored Courage Puppy</a>&#160;• <a href="/wiki/Armored_Power_Puppy" title="Armored Power Puppy">Armored Power Puppy</a>&#160;• <a href="/wiki/Armored_Wisdom_Puppy" title="Armored Wisdom Puppy">Armored Wisdom Puppy</a>&#160;• <a href="/wiki/Blood_Goobert" title="Blood Goobert">Blood Goobert</a>&#160;• <a href="/wiki/Carrot_Goobert" title="Carrot Goobert">Carrot Goobert</a>&#160;• <a href="/wiki/Cheese_Goobert" title="Cheese Goobert">Cheese Goobert</a>&#160;• <a href="/wiki/Chili_Goobert" title="Chili Goobert">Chili Goobert</a>&#160;• <a href="/wiki/Chtulhu" title="Chtulhu">Chtulhu</a>&#160;• <a href="/wiki/Courage_Puppy" title="Courage Puppy">Courage Puppy</a>&#160;• <a href="/wiki/Cubert" title="Cubert">Cubert</a>&#160;• <a href="/wiki/Emerald_Egg" title="Emerald Egg">Emerald Egg</a>&#160;• <a href="/wiki/Emerald_Whelp" title="Emerald Whelp">Emerald Whelp</a>&#160;• <a href="/wiki/Goobert" title="Goobert">Goobert</a>&#160;• <a href="/wiki/Goobling" title="Goobling">Goobling</a>&#160;• <a href="/wiki/Hedgehog" title="Hedgehog">Hedgehog</a>&#160;• <a href="/wiki/Ice_Dragon" title="Ice Dragon">Ice Dragon</a>&#160;• <a href="/wiki/Jynx_torquilla" title="Jynx torquilla">Jynx torquilla</a>&#160;• <a href="/wiki/King_Goobert" title="King Goobert">King Goobert</a>&#160;• <a href="/wiki/Light_Goobert" title="Light Goobert">Light Goobert</a>&#160;• <a href="/wiki/Obsidian_Dragon" title="Obsidian Dragon">Obsidian Dragon</a>&#160;• <a href="/wiki/Phoenix" title="Phoenix">Phoenix</a>&#160;• <a href="/wiki/Poison_Goobert" title="Poison Goobert">Poison Goobert</a>&#160;• <a href="/wiki/Pop" title="Pop">Pop</a>&#160;• <a href="/wiki/Power_Puppy" title="Power Puppy">Power Puppy</a>&#160;• <a href="/wiki/Rainbow_Goobert_Deathslushy_Mansquisher" title="Rainbow Goobert Deathslushy Mansquisher">Rainbow Goobert Deathslushy Mansquisher</a>&#160;• <a href="/wiki/Rainbow_Goobert_Epicglob_Uberviscous" title="Rainbow Goobert Epicglob Uberviscous">Rainbow Goobert Epicglob Uberviscous</a>&#160;• <a href="/wiki/Rainbow_Goobert_Megasludge_Alphapuddle" title="Rainbow Goobert Megasludge Alphapuddle">Rainbow Goobert Megasludge Alphapuddle</a>&#160;• <a href="/wiki/Rainbow_Goobert_Omegaooze_Primeslime" title="Rainbow Goobert Omegaooze Primeslime">Rainbow Goobert Omegaooze Primeslime</a>&#160;• <a href="/wiki/Rat" title="Rat">Rat</a>&#160;• <a href="/wiki/Rat_Chef" title="Rat Chef">Rat Chef</a>&#160;• <a href="/wiki/Ruby_Chonk" title="Ruby Chonk">Ruby Chonk</a>&#160;• <a href="/wiki/Ruby_Egg" title="Ruby Egg">Ruby Egg</a>&#160;• <a href="/wiki/Ruby_Whelp" title="Ruby Whelp">Ruby Whelp</a>&#160;• <a href="/wiki/Sapphire_Egg" title="Sapphire Egg">Sapphire Egg</a>&#160;• <a href="/wiki/Sapphire_Whelp" title="Sapphire Whelp">Sapphire Whelp</a>&#160;• <a href="/wiki/Snake" title="Snake">Snake</a>&#160;• <a href="/wiki/Squirrel" title="Squirrel">Squirrel</a>&#160;• <a href="/wiki/Squirrel_Archer" title="Squirrel Archer">Squirrel Archer</a>&#160;• <a href="/wiki/Steel_Goobert" title="Steel Goobert">Steel Goobert</a>&#160;• <a href="/wiki/Stone_Golem" title="Stone Golem">Stone Golem</a>&#160;• <a href="/wiki/Toad" title="Toad">Toad</a>&#160;• <a href="/wiki/Unsettling_Presence" title="Unsettling Presence">Unsettling Presence</a>&#160;• <a href="/wiki/Wisdom_Puppy" title="Wisdom Puppy">Wisdom Puppy</a>&#160;• <a href="/wiki/Wolpertinger" title="Wolpertinger">Wolpertinger</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Playing_Card" title="Playing Card">Playing Card</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Ace_of_Spades" title="Ace of Spades">Ace of Spades</a>&#160;• <a href="/wiki/Darkest_Lotus" title="Darkest Lotus">Darkest Lotus</a>&#160;• <a href="/wiki/Holo_Fire_Lizard" title="Holo Fire Lizard">Holo Fire Lizard</a>&#160;• <a href="/wiki/Jimbo" title="Jimbo">Jimbo</a>&#160;• <a href="/wiki/Reverse!" title="Reverse!">Reverse!</a>&#160;• <a href="/wiki/The_Fool" title="The Fool">The Fool</a>&#160;• <a href="/wiki/The_Lovers" title="The Lovers">The Lovers</a>&#160;• <a href="/wiki/White-Eyes_Blue_Dragon" title="White-Eyes Blue Dragon">White-Eyes Blue Dragon</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Potion" title="Potion">Potion</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Demonic_Flask" title="Demonic Flask">Demonic Flask</a>&#160;• <a href="/wiki/Divine_Potion" title="Divine Potion">Divine Potion</a>&#160;• <a href="/wiki/Health_Potion" title="Health Potion">Health Potion</a>&#160;• <a href="/wiki/Heroic_Potion" title="Heroic Potion">Heroic Potion</a>&#160;• <a href="/wiki/Mana_Potion" title="Mana Potion">Mana Potion</a>&#160;• <a href="/wiki/Pestilence_Flask" title="Pestilence Flask">Pestilence Flask</a>&#160;• <a href="/wiki/Stone_Skin_Potion" title="Stone Skin Potion">Stone Skin Potion</a>&#160;• <a href="/wiki/Strong_Demonic_Flask" title="Strong Demonic Flask">Strong Demonic Flask</a>&#160;• <a href="/wiki/Strong_Divine_Potion" title="Strong Divine Potion">Strong Divine Potion</a>&#160;• <a href="/wiki/Strong_Health_Potion" title="Strong Health Potion">Strong Health Potion</a>&#160;• <a href="/wiki/Strong_Heroic_Potion" title="Strong Heroic Potion">Strong Heroic Potion</a>&#160;• <a href="/wiki/Strong_Mana_Potion" title="Strong Mana Potion">Strong Mana Potion</a>&#160;• <a href="/wiki/Strong_Pestilence_Flask" title="Strong Pestilence Flask">Strong Pestilence Flask</a>&#160;• <a href="/wiki/Strong_Stone_Skin_Potion" title="Strong Stone Skin Potion">Strong Stone Skin Potion</a>&#160;• <a href="/wiki/Strong_Vampiric_Potion" title="Strong Vampiric Potion">Strong Vampiric Potion</a>&#160;• <a href="/wiki/Vampiric_Potion" title="Vampiric Potion">Vampiric Potion</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Shield" title="Shield">Shield</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Frozen_Buckler" title="Frozen Buckler">Frozen Buckler</a>&#160;• <a href="/wiki/Moon_Shield" title="Moon Shield">Moon Shield</a>&#160;• <a href="/wiki/Shield_of_Valor" title="Shield of Valor">Shield of Valor</a>&#160;• <a href="/wiki/Spiked_Shield" title="Spiked Shield">Spiked Shield</a>&#160;• <a href="/wiki/Sun_Shield" title="Sun Shield">Sun Shield</a>&#160;• <a href="/wiki/Wooden_Buckler" title="Wooden Buckler">Wooden Buckler</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Shoes" title="Shoes">Shoes</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Dragonskin_Boots" title="Dragonskin Boots">Dragonskin Boots</a>&#160;• <a href="/wiki/Leather_Boots" title="Leather Boots">Leather Boots</a>&#160;• <a href="/wiki/Stone_Shoes" title="Stone Shoes">Stone Shoes</a>&#160;• <a href="/wiki/Winged_Boots" title="Winged Boots">Winged Boots</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Weapon" title="Weapon">Weapon</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Amethyst_Whelp" title="Amethyst Whelp">Amethyst Whelp</a>&#160;• <a href="/wiki/Armored_Courage_Puppy" title="Armored Courage Puppy">Armored Courage Puppy</a>&#160;• <a href="/wiki/Artifact_Stone:_Cold" title="Artifact Stone: Cold">Artifact Stone: Cold</a>&#160;• <a href="/wiki/Artifact_Stone:_Death" title="Artifact Stone: Death">Artifact Stone: Death</a>&#160;• <a href="/wiki/Artifact_Stone:_Heat" title="Artifact Stone: Heat">Artifact Stone: Heat</a>&#160;• <a href="/wiki/Axe" title="Axe">Axe</a>&#160;• <a href="/wiki/Belladonna%27s_Shade" title="Belladonna&#39;s Shade">Belladonna's Shade</a>&#160;• <a href="/wiki/Belladonna%27s_Whisper" title="Belladonna&#39;s Whisper">Belladonna's Whisper</a>&#160;• <a href="/wiki/Blood_Harvester" title="Blood Harvester">Blood Harvester</a>&#160;• <a href="/wiki/Bloodthorne" title="Bloodthorne">Bloodthorne</a>&#160;• <a href="/wiki/Bloody_Dagger" title="Bloody Dagger">Bloody Dagger</a>&#160;• <a href="/wiki/Bow_and_Arrow" title="Bow and Arrow">Bow and Arrow</a>&#160;• <a href="/wiki/Brass_Knuckles" title="Brass Knuckles">Brass Knuckles</a>&#160;• <a href="/wiki/Broom" title="Broom">Broom</a>&#160;• <a href="/wiki/Burning_Blade" title="Burning Blade">Burning Blade</a>&#160;• <a href="/wiki/Burning_Sword" title="Burning Sword">Burning Sword</a>&#160;• <a href="/wiki/Burning_Torch" title="Burning Torch">Burning Torch</a>&#160;• <a href="/wiki/Busted_Blade" title="Busted Blade">Busted Blade</a>&#160;• <a href="/wiki/Chain_Whip" title="Chain Whip">Chain Whip</a>&#160;• <a href="/wiki/Claws_of_Attack" title="Claws of Attack">Claws of Attack</a>&#160;• <a href="/wiki/Courage_Puppy" title="Courage Puppy">Courage Puppy</a>&#160;• <a href="/wiki/Critwood_Staff" title="Critwood Staff">Critwood Staff</a>&#160;• <a href="/wiki/Crossblades" title="Crossblades">Crossblades</a>&#160;• <a href="/wiki/Cursed_Dagger" title="Cursed Dagger">Cursed Dagger</a>&#160;• <a href="/wiki/Dagger" title="Dagger">Dagger</a>&#160;• <a href="/wiki/Dancing_Dragon" title="Dancing Dragon">Dancing Dragon</a>&#160;• <a href="/wiki/Darksaber" title="Darksaber">Darksaber</a>&#160;• <a href="/wiki/Death_Scythe" title="Death Scythe">Death Scythe</a>&#160;• <a href="/wiki/Double_Axe" title="Double Axe">Double Axe</a>&#160;• <a href="/wiki/Eggscalibur" title="Eggscalibur">Eggscalibur</a>&#160;• <a href="/wiki/Emerald_Whelp" title="Emerald Whelp">Emerald Whelp</a>&#160;• <a href="/wiki/Falcon_Blade" title="Falcon Blade">Falcon Blade</a>&#160;• <a href="/wiki/Fancy_Fencing_Rapier" title="Fancy Fencing Rapier">Fancy Fencing Rapier</a>&#160;• <a href="/wiki/Flame_Whip" title="Flame Whip">Flame Whip</a>&#160;• <a href="/wiki/Forging_Hammer" title="Forging Hammer">Forging Hammer</a>&#160;• <a href="/wiki/Fortuna%27s_Grace" title="Fortuna&#39;s Grace">Fortuna's Grace</a>&#160;• <a href="/wiki/Fortuna%27s_Hope" title="Fortuna&#39;s Hope">Fortuna's Hope</a>&#160;• <a href="/wiki/Frostbite" title="Frostbite">Frostbite</a>&#160;• <a href="/wiki/Hammer" title="Hammer">Hammer</a>&#160;• <a href="/wiki/Hero_Longsword" title="Hero Longsword">Hero Longsword</a>&#160;• <a href="/wiki/Hero_Sword" title="Hero Sword">Hero Sword</a>&#160;• <a href="/wiki/Holy_Spear" title="Holy Spear">Holy Spear</a>&#160;• <a href="/wiki/Hungry_Blade" title="Hungry Blade">Hungry Blade</a>&#160;• <a href="/wiki/Ice_Dragon" title="Ice Dragon">Ice Dragon</a>&#160;• <a href="/wiki/Impractically_Large_Greatsword" title="Impractically Large Greatsword">Impractically Large Greatsword</a>&#160;• <a href="/wiki/Katana" title="Katana">Katana</a>&#160;• <a href="/wiki/Lightsaber" title="Lightsaber">Lightsaber</a>&#160;• <a href="/wiki/Magic_Staff" title="Magic Staff">Magic Staff</a>&#160;• <a href="/wiki/Magic_Torch" title="Magic Torch">Magic Torch</a>&#160;• <a href="/wiki/Manathirst" title="Manathirst">Manathirst</a>&#160;• <a href="/wiki/Molten_Dagger" title="Molten Dagger">Molten Dagger</a>&#160;• <a href="/wiki/Molten_Spear" title="Molten Spear">Molten Spear</a>&#160;• <a href="/wiki/Obsidian_Dragon" title="Obsidian Dragon">Obsidian Dragon</a>&#160;• <a href="/wiki/Pan" title="Pan">Pan</a>&#160;• <a href="/wiki/Pandamonium" title="Pandamonium">Pandamonium</a>&#160;• <a href="/wiki/Phoenix" title="Phoenix">Phoenix</a>&#160;• <a href="/wiki/Poison_Dagger" title="Poison Dagger">Poison Dagger</a>&#160;• <a href="/wiki/Pop" title="Pop">Pop</a>&#160;• <a href="/wiki/Prismatic_Sword" title="Prismatic Sword">Prismatic Sword</a>&#160;• <a href="/wiki/Pumpkin" title="Pumpkin">Pumpkin</a>&#160;• <a href="/wiki/Ripsaw_Blade" title="Ripsaw Blade">Ripsaw Blade</a>&#160;• <a href="/wiki/Ruby_Chonk" title="Ruby Chonk">Ruby Chonk</a>&#160;• <a href="/wiki/Ruby_Whelp" title="Ruby Whelp">Ruby Whelp</a>&#160;• <a href="/wiki/Sapphire_Whelp" title="Sapphire Whelp">Sapphire Whelp</a>&#160;• <a href="/wiki/Serpent_Staff" title="Serpent Staff">Serpent Staff</a>&#160;• <a href="/wiki/Shell_Totem" title="Shell Totem">Shell Totem</a>&#160;• <a href="/wiki/Shortbow" title="Shortbow">Shortbow</a>&#160;• <a href="/wiki/Shovel" title="Shovel">Shovel</a>&#160;• <a href="/wiki/Snow_Stick" title="Snow Stick">Snow Stick</a>&#160;• <a href="/wiki/Spear" title="Spear">Spear</a>&#160;• <a href="/wiki/Spectral_Dagger" title="Spectral Dagger">Spectral Dagger</a>&#160;• <a href="/wiki/Spiked_Staff" title="Spiked Staff">Spiked Staff</a>&#160;• <a href="/wiki/Squirrel_Archer" title="Squirrel Archer">Squirrel Archer</a>&#160;• <a href="/wiki/Staff_of_Fire" title="Staff of Fire">Staff of Fire</a>&#160;• <a href="/wiki/Staff_of_Unhealing" title="Staff of Unhealing">Staff of Unhealing</a>&#160;• <a href="/wiki/Stone" title="Stone">Stone</a>&#160;• <a href="/wiki/Stone_Golem" title="Stone Golem">Stone Golem</a>&#160;• <a href="/wiki/Thorn_Whip" title="Thorn Whip">Thorn Whip</a>&#160;• <a href="/wiki/Thornbloom" title="Thornbloom">Thornbloom</a>&#160;• <a href="/wiki/Torch" title="Torch">Torch</a>&#160;• <a href="/wiki/Tusk_Piercer" title="Tusk Piercer">Tusk Piercer</a>&#160;• <a href="/wiki/Tusk_Poker" title="Tusk Poker">Tusk Poker</a>&#160;• <a href="/wiki/Villain_Sword" title="Villain Sword">Villain Sword</a>&#160;• <a href="/wiki/Wooden_Sword" title="Wooden Sword">Wooden Sword</a></div></td></tr></tbody></table>
<!-- 
NewPP limit report
Cached time: 20250602205546
Cache expiry: 2592000
Reduced expiry: false
Complications: []
CPU time usage: 0.398 seconds
Real time usage: 1.073 seconds
Preprocessor visited node count: 2225/1000000
Revision size: 376/4194304 bytes
Post‐expand include size: 45107/4194304 bytes
Template argument size: 8367/4194304 bytes
Highest expansion depth: 6/100
Expensive parser function count: 0/500
Unstrip recursion depth: 0/20
Unstrip post‐expand size: 8223/10000000 bytes
Lua time usage: 0.008/15.000 seconds
Lua memory usage: 636928/52428800 bytes
Number of processed Cargo queries: 14
Time spent processing Cargo queries: 0.668 s (avg. 0.048 s)
Number of Cargo row insertion attempts: 0
-->
<!--
Transclusion expansion time report (%,ms,calls,template)
100.00%  184.066      1 -total
 67.25%  123.789      1 Template:Items
 65.26%  120.121      1 Template:Navbox
 31.64%   58.238     29 Template:Item_table/class
  4.06%    7.472     29 Template:Icon/gold
  3.55%    6.537     22 Template:Icon/nature
  2.54%    4.673      2 Template:Icon/magic
  2.49%    4.586      1 Template:Icon/holy
  2.47%    4.552      1 Template:Icon/vampiric
  2.42%    4.463      1 Template:Icon/effect
-->

<!-- Saved in parser cache with key backpackbattles_en:pcache:idhash:1348-0!canonical and timestamp 20250602205546 and revision id 10436. Rendering was triggered because: page-view
 -->
</div>
<div class="printfooter" data-nosnippet="">Retrieved from "<a dir="ltr" href="https://backpackbattles.wiki.gg/wiki/Ranger_items?oldid=10436">https://backpackbattles.wiki.gg/wiki/Ranger_items?oldid=10436</a>"</div></div>
					<div id="catlinks" class="catlinks" data-mw="interface"><div id="mw-normal-catlinks" class="mw-normal-catlinks"><a href="/wiki/Special:Categories" title="Special:Categories">Category</a>: <ul><li><a href="/wiki/Category:Items" title="Category:Items">Items</a></li></ul></div></div>
				</div>
			</main>
			<aside id="wikigg-sl-rail" data-mw="WggShowcaseLayout">
        <div
            class="wikigg-showcase__unit wikigg-showcase__reserved--mrec"
            data-wgg-unit-type="pubco"
        ><div id="nn_mpu1"></div></div>
        <div
            class="wikigg-showcase__unit wikigg-showcase__reserved--mrec"
            data-wgg-unit-type="internal"
        ></div>
        <div
            class="wikigg-showcase__unit wikigg-showcase__reserved--mrec"
            data-wgg-unit-type="pubco"
        ><div id="nn_mpu2"></div></div>
</aside>

		</div>
		<aside id="wikigg-sl-footer" data-mw="WggShowcaseLayout">
        <div
            class="wikigg-showcase__unit wikigg-showcase__reserved--lb"
            data-wgg-unit-type="internal"
        ></div>
</aside>

	</div>
</div>
<div id='mw-data-after-content'>
	<div class="mw-cookiewarning-container"><div class="mw-cookiewarning-text"><span>Cookies help us deliver our services. By using our services, you agree to our use of cookies.</span></div><form method="POST"><div class='oo-ui-layout oo-ui-horizontalLayout'><span class='oo-ui-widget oo-ui-widget-enabled oo-ui-buttonElement oo-ui-buttonElement-framed oo-ui-labelElement oo-ui-flaggedElement-progressive oo-ui-buttonWidget'><a role='button' tabindex='0' href='https://www.indie.io/privacy-policy' rel='nofollow' class='oo-ui-buttonElement-button'><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon oo-ui-image-progressive'></span><span class='oo-ui-labelElement-label'>More information</span><span class='oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator oo-ui-image-progressive'></span></a></span><span class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-buttonElement oo-ui-buttonElement-framed oo-ui-labelElement oo-ui-flaggedElement-primary oo-ui-flaggedElement-progressive oo-ui-buttonInputWidget'><button type='submit' tabindex='0' name='disablecookiewarning' value='OK' class='oo-ui-inputWidget-input oo-ui-buttonElement-button'><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon oo-ui-image-invert'></span><span class='oo-ui-labelElement-label'>OK</span><span class='oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator oo-ui-image-invert'></span></button></span></div></form></div>
</div>


<footer id="footer" class="mw-footer" >
	<ul id="footer-info">
	<li id="footer-info-lastmod"> This page was last edited on 8 November 2024, at 15:11.</li>
	<li id="footer-info-copyright"><div class="wikigg-copyright-box">Pages that were created prior to November 2023 are adapted from the Backpack Battles Fandom wiki.<br />Page content is under <a class="external" rel="nofollow" href="https://creativecommons.org/licenses/by-sa/4.0">Creative Commons Attribution-ShareAlike 4.0 License</a> unless otherwise noted.</div></li>
</ul>

	<ul id="footer-places">
	<li id="footer-places-wikigg-tos"><a href="https://www.indie.io/terms-of-service">Terms of Service</a></li>
	<li id="footer-places-wikigg-privacy"><a href="https://www.indie.io/privacy-policy">Privacy policy</a></li>
	<li id="footer-places-wikigg-support"><a href="https://support.wiki.gg">Support Wiki</a></li>
	<li id="footer-places-wikigg-servicedesk"><a href="https://wiki.gg/go/servicedesk">Send a ticket to wiki.gg</a></li>
	<li id="footer-places-wikigg-statuspage"><a href="https://wikiggstatus.com">Status page</a></li>
	<li id="footer-places-wikigg-pcmp"><a href="#" class="nn-cmp-show" style="display: none">Manage cookie settings</a></li>
</ul>

	<ul id="footer-icons" class="noprint">
	<li id="footer-copyrightico"><a href="https://creativecommons.org/licenses/by-sa/4.0" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><img src="https://commons.wiki.gg/images/1/14/CC-BY-SA_footer_badge.svg?d931d3" alt="Creative Commons Attribution-ShareAlike 4.0 License" height="31" width="88" loading="lazy" data-theme="light"><img src="https://commons.wiki.gg/images/7/71/CC-BY-SA_footer_badge_dark.svg?55845c" alt="Creative Commons Attribution-ShareAlike 4.0 License" height="31" width="88" loading="lazy" data-theme="dark"></a></li>
	<li id="footer-poweredbyico"><a href="https://www.mediawiki.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><img src="/mw-1.43/resources/assets/poweredby_mediawiki.svg" alt="Powered by MediaWiki" width="88" height="31" loading="lazy" data-theme="light"><img src="https://commons.wiki.gg/images/1/1c/MediaWiki_footer_badge_dark.svg?12ec0a" alt="Powered by MediaWiki" width="88" height="31" loading="lazy" data-theme="dark"></a></li>
	<li id="footer-partofico"><a href="https://wiki.gg" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><img src="https://commons.wiki.gg/images/d/d1/Network_footer_badge.svg?9d5a96" alt="Part of wiki.gg" height="31" width="88" loading="lazy" data-theme="light"><img src="https://commons.wiki.gg/images/2/23/Network_footer_badge_dark.svg?9cf3e8" alt="Part of wiki.gg" height="31" width="88" loading="lazy" data-theme="dark"></a></li>
</ul>

</footer>


<script>(RLQ=window.RLQ||[]).push(function(){mw.config.set({"wgBackendResponseTime":144,"wgPageParseReport":{"limitreport":{"cputime":"0.398","walltime":"1.073","ppvisitednodes":{"value":2225,"limit":1000000},"revisionsize":{"value":376,"limit":4194304},"postexpandincludesize":{"value":45107,"limit":4194304},"templateargumentsize":{"value":8367,"limit":4194304},"expansiondepth":{"value":6,"limit":100},"expensivefunctioncount":{"value":0,"limit":500},"unstrip-depth":{"value":0,"limit":20},"unstrip-size":{"value":8223,"limit":10000000},"timingprofile":["100.00%  184.066      1 -total"," 67.25%  123.789      1 Template:Items"," 65.26%  120.121      1 Template:Navbox"," 31.64%   58.238     29 Template:Item_table/class","  4.06%    7.472     29 Template:Icon/gold","  3.55%    6.537     22 Template:Icon/nature","  2.54%    4.673      2 Template:Icon/magic","  2.49%    4.586      1 Template:Icon/holy","  2.47%    4.552      1 Template:Icon/vampiric","  2.42%    4.463      1 Template:Icon/effect"]},"scribunto":{"limitreport-timeusage":{"value":"0.008","limit":"15.000"},"limitreport-memusage":{"value":636928,"limit":52428800}},"librarian":{"limitreport-queries":14,"limitreport-querytime":["0.668 s (avg. 0.048 s)"],"limitreport-insertions":0},"cachereport":{"timestamp":"20250602205546","ttl":2592000,"transientcontent":false}}});});</script>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'950260eb3e542303',t:'MTc0OTk5Mzc3MC4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script><script defer src="https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon='{"rayId":"950260eb3e542303","serverTiming":{"name":{"cfExtPri":true,"cfEdge":true,"cfOrigin":true,"cfL4":true,"cfSpeedBrain":true,"cfCacheStatus":true}},"version":"2025.6.2","token":"26b2c6e7c852417d8f9d11b0c7f02309"}' crossorigin="anonymous"></script>
</body>
</html>