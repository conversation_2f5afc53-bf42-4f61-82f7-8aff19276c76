import json
import re
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from collections import defaultdict, Counter
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # Non-interactive backend

@dataclass
class BuildAnalysis:
    """Comprehensive build analysis results."""
    total_dps: float
    effective_dps: float  # Accounting for accuracy and cooldowns
    stamina_efficiency: float
    healing_per_second: float
    damage_mitigation_percent: float
    buff_generation: Dict[str, float]  # buff_type -> per_second
    debuff_application: Dict[str, float]  # debuff_type -> per_second
    synergy_score: float
    positional_efficiency: float
    cost_efficiency: float

class ComprehensiveAnalyzer:
    """Comprehensive analysis system for Backpack Battles builds."""
    
    def __init__(self, data_file: str = "complete_enhanced_backpack_battles_data_with_emojis.json"):
        """Initialize with emoji-fixed game data."""
        with open(data_file, 'r', encoding='utf-8') as f:
            self.game_data = json.load(f)
        
        self.items = {item['name']: item for item in self.game_data['enhanced_items']}
        self.recipes = self.game_data.get('recipes', [])
        
        # Buff/debuff patterns with emojis
        self.buff_patterns = {
            'strength': r'gain (\d+) 💪',
            'mana': r'gain (\d+) 🔮',
            'armor': r'gain (\d+) 🛡️',
            'health': r'gain (\d+) ❤️',
            'stamina': r'gain (\d+) ⚡',
            'luck': r'gain (\d+) 🍀',
            'healing': r'heal for (\d+) 💚',
            'damage_boost': r'weapons gain (\d+) ⚔️ damage'
        }
        
        self.debuff_patterns = {
            'poison': r'inflict (\d+) ☠️',
            'fire_damage': r'deal (\d+) 🔥 damage',
            'ice_damage': r'deal (\d+) ❄️ damage',
            'generic_debuff': r'inflict (\d+) random debuff'
        }

    def analyze_dps(self, items: List[str]) -> Tuple[float, float]:
        """Analyze damage per second including weapon stats and buffs."""
        base_dps = 0.0
        effective_dps = 0.0
        damage_bonuses = 0
        accuracy_modifier = 1.0
        
        for item_name in items:
            if item_name not in self.items:
                continue
                
            item = self.items[item_name]
            weapon_stats = item.get('weapon_stats')
            effect = item.get('effect', '').lower()
            
            # Base weapon DPS
            if weapon_stats and weapon_stats.get('damage'):
                damage_str = weapon_stats['damage']
                dps_match = re.search(r'\(([0-9.]+)/s\)', damage_str)
                if dps_match:
                    base_dps += float(dps_match.group(1))
                
                # Accuracy modifier
                accuracy_str = weapon_stats.get('accuracy', '100%')
                accuracy_match = re.search(r'(\d+)%', accuracy_str)
                if accuracy_match:
                    accuracy_modifier *= float(accuracy_match.group(1)) / 100
            
            # Damage bonuses from effects
            damage_bonus_matches = re.findall(r'weapons? gain (\d+) ⚔️ damage', effect)
            for match in damage_bonus_matches:
                damage_bonuses += int(match)
            
            # Conditional damage bonuses
            if 'gain.*damage' in effect:
                conditional_matches = re.findall(r'gain (\d+) damage', effect)
                for match in conditional_matches:
                    damage_bonuses += int(match) * 0.5  # Assume 50% uptime
        
        effective_dps = (base_dps + damage_bonuses) * accuracy_modifier
        return base_dps, effective_dps

    def analyze_stamina_management(self, items: List[str]) -> float:
        """Analyze stamina efficiency and management."""
        stamina_cost_reduction = 0.0
        stamina_generation = 0.0
        stamina_efficiency_bonus = 0.0
        
        for item_name in items:
            if item_name not in self.items:
                continue
                
            item = self.items[item_name]
            effect = item.get('effect', '').lower()
            bag_properties = item.get('bag_properties')
            
            # Stamina generation
            stamina_matches = re.findall(r'regenerate (\d+) stamina', effect)
            for match in stamina_matches:
                stamina_generation += int(match)
            
            # Stamina efficiency from bags
            if bag_properties and 'Stamina bonus' in bag_properties.get('special_effects', []):
                stamina_efficiency_bonus += 0.1
            
            # Items that reduce stamina costs
            if 'use.*stamina' in effect and 'less' in effect:
                stamina_cost_reduction += 0.1
        
        # Calculate overall stamina efficiency score
        efficiency_score = (stamina_generation * 2 + stamina_efficiency_bonus * 10 + stamina_cost_reduction * 5)
        return efficiency_score

    def analyze_buff_debuff_management(self, items: List[str]) -> Tuple[Dict[str, float], Dict[str, float]]:
        """Analyze buff generation and debuff application rates."""
        buff_rates = defaultdict(float)
        debuff_rates = defaultdict(float)
        
        for item_name in items:
            if item_name not in self.items:
                continue
                
            item = self.items[item_name]
            effect = item.get('effect', '')
            timing_effects = item.get('timing_effects', [])
            
            # Extract timing intervals
            base_interval = 10.0  # Default interval
            for timing in timing_effects:
                if 'Seconds:' in timing:
                    try:
                        interval = float(timing.split(':')[1].strip())
                        base_interval = interval
                        break
                    except:
                        pass
            
            # Analyze buff generation
            for buff_type, pattern in self.buff_patterns.items():
                matches = re.findall(pattern, effect, re.IGNORECASE)
                for match in matches:
                    amount = int(match)
                    rate = amount / base_interval
                    buff_rates[buff_type] += rate
            
            # Analyze debuff application
            for debuff_type, pattern in self.debuff_patterns.items():
                matches = re.findall(pattern, effect, re.IGNORECASE)
                for match in matches:
                    amount = int(match)
                    rate = amount / base_interval
                    debuff_rates[debuff_type] += rate
        
        return dict(buff_rates), dict(debuff_rates)

    def analyze_healing(self, items: List[str]) -> float:
        """Analyze healing per second."""
        total_healing_per_second = 0.0
        
        for item_name in items:
            if item_name not in self.items:
                continue
                
            item = self.items[item_name]
            effect = item.get('effect', '')
            timing_effects = item.get('timing_effects', [])
            
            # Extract healing amounts and timing
            heal_matches = re.findall(r'heal for (\d+) 💚', effect, re.IGNORECASE)
            if not heal_matches:
                heal_matches = re.findall(r'heal for (\d+)', effect, re.IGNORECASE)
            
            for heal_amount in heal_matches:
                # Find timing for this healing
                interval = 10.0  # Default
                for timing in timing_effects:
                    if 'Seconds:' in timing:
                        try:
                            interval = float(timing.split(':')[1].strip())
                            break
                        except:
                            pass
                
                healing_rate = int(heal_amount) / interval
                total_healing_per_second += healing_rate
        
        return total_healing_per_second

    def analyze_synergies(self, items: List[str]) -> float:
        """Analyze positional and type synergies."""
        synergy_score = 0.0
        item_types = Counter()
        
        for item_name in items:
            if item_name not in self.items:
                continue
                
            item = self.items[item_name]
            item_type = item.get('type', '')
            item_types[item_type] += 1
            
            positional_synergies = item.get('positional_synergies', [])
            
            # Score positional synergies
            for synergy in positional_synergies:
                if 'star synergy' in synergy.lower():
                    synergy_score += 2.0
                elif 'diamond synergy' in synergy.lower():
                    synergy_score += 1.5
                elif 'adjacency' in synergy.lower():
                    synergy_score += 1.0
                elif 'for each' in synergy.lower():
                    synergy_score += 1.0
        
        # Bonus for type diversity
        type_diversity_bonus = len(item_types) * 0.5
        
        # Bonus for multiple items of same type (some synergies)
        same_type_bonus = sum(max(0, count - 1) * 0.3 for count in item_types.values())
        
        total_synergy = synergy_score + type_diversity_bonus + same_type_bonus
        return total_synergy

    def analyze_cost_efficiency(self, items: List[str]) -> float:
        """Analyze gold cost efficiency of the build."""
        total_cost = 0
        total_value_score = 0
        
        for item_name in items:
            if item_name not in self.items:
                continue
                
            item = self.items[item_name]
            cost = int(item.get('cost', '0'))
            rarity = item.get('rarity', 'Common')
            
            total_cost += cost
            
            # Value scoring based on rarity and effects
            rarity_values = {
                'Common': 1, 'Rare': 2, 'Epic': 4, 
                'Legendary': 8, 'Unique': 6, 'Godly': 16, 'Varies': 3
            }
            
            base_value = rarity_values.get(rarity, 1)
            
            # Bonus value for complex effects
            effect = item.get('effect', '')
            if len(effect) > 100:
                base_value *= 1.2
            if 'start of battle' in effect.lower():
                base_value *= 1.1
            
            total_value_score += base_value
        
        if total_cost == 0:
            return 0.0
        
        efficiency = total_value_score / total_cost
        return efficiency

    def comprehensive_build_analysis(self, items: List[str]) -> BuildAnalysis:
        """Perform comprehensive analysis of a build."""
        base_dps, effective_dps = self.analyze_dps(items)
        stamina_efficiency = self.analyze_stamina_management(items)
        healing_per_second = self.analyze_healing(items)
        buff_rates, debuff_rates = self.analyze_buff_debuff_management(items)
        synergy_score = self.analyze_synergies(items)
        cost_efficiency = self.analyze_cost_efficiency(items)
        
        # Estimate damage mitigation (simplified)
        damage_mitigation = 0.0
        for item_name in items:
            if item_name in self.items:
                effect = self.items[item_name].get('effect', '').lower()
                if 'resist' in effect or 'reduce.*damage' in effect:
                    damage_mitigation += 5.0  # 5% per resistance item
        
        # Calculate positional efficiency based on grid layouts
        positional_efficiency = 0.0
        total_cells = 0
        for item_name in items:
            if item_name in self.items:
                grid_layout = self.items[item_name].get('grid_layout', [])
                if grid_layout:
                    for row in grid_layout:
                        total_cells += len(row)
                        positional_efficiency += len([cell for cell in row if cell in ['star', 'diamond']])
        
        if total_cells > 0:
            positional_efficiency = (positional_efficiency / total_cells) * 100
        
        return BuildAnalysis(
            total_dps=base_dps,
            effective_dps=effective_dps,
            stamina_efficiency=stamina_efficiency,
            healing_per_second=healing_per_second,
            damage_mitigation_percent=damage_mitigation,
            buff_generation=buff_rates,
            debuff_application=debuff_rates,
            synergy_score=synergy_score,
            positional_efficiency=positional_efficiency,
            cost_efficiency=cost_efficiency
        )

    def create_build_report(self, items: List[str], build_name: str = "Custom Build") -> str:
        """Generate a comprehensive text report for a build."""
        analysis = self.comprehensive_build_analysis(items)
        
        report = f"""
🎯 COMPREHENSIVE BUILD ANALYSIS: {build_name}
{'=' * 60}

📊 COMBAT PERFORMANCE
⚔️ Base DPS: {analysis.total_dps:.2f}
🎯 Effective DPS: {analysis.effective_dps:.2f} (accounting for accuracy)
💚 Healing/sec: {analysis.healing_per_second:.2f}
🛡️ Damage Mitigation: {analysis.damage_mitigation_percent:.1f}%

⚡ RESOURCE MANAGEMENT
🔋 Stamina Efficiency Score: {analysis.stamina_efficiency:.1f}
💰 Cost Efficiency: {analysis.cost_efficiency:.2f} (value/gold)

🔗 SYNERGIES & POSITIONING
🌟 Synergy Score: {analysis.synergy_score:.1f}
📐 Positional Efficiency: {analysis.positional_efficiency:.1f}% (star/diamond usage)

💪 BUFF GENERATION (per second):"""
        
        for buff_type, rate in analysis.buff_generation.items():
            if rate > 0:
                report += f"\n   {buff_type}: {rate:.2f}/s"
        
        if not analysis.buff_generation:
            report += "\n   No significant buff generation detected"
        
        report += "\n\n☠️ DEBUFF APPLICATION (per second):"
        
        for debuff_type, rate in analysis.debuff_application.items():
            if rate > 0:
                report += f"\n   {debuff_type}: {rate:.2f}/s"
        
        if not analysis.debuff_application:
            report += "\n   No significant debuff application detected"
        
        # Item breakdown
        report += f"\n\n📦 ITEM BREAKDOWN ({len(items)} items):"
        total_cost = 0
        
        for item_name in items:
            if item_name in self.items:
                item = self.items[item_name]
                cost = int(item.get('cost', '0'))
                total_cost += cost
                item_type = item.get('type', 'Unknown')
                rarity = item.get('rarity', 'Common')
                
                report += f"\n   • {item_name} ({item_type}, {rarity}) - {cost}g"
        
        report += f"\n\n💰 Total Build Cost: {total_cost} gold"
        
        # Performance rating
        overall_score = (
            analysis.effective_dps * 2 +
            analysis.healing_per_second * 3 +
            analysis.synergy_score +
            analysis.positional_efficiency / 10 +
            analysis.cost_efficiency * 5
        )
        
        rating = "⭐" * min(5, max(1, int(overall_score / 10)))
        report += f"\n\n🏆 Overall Build Rating: {rating} ({overall_score:.1f}/50)"
        
        return report

def main():
    """Test the comprehensive analysis system."""
    analyzer = ComprehensiveAnalyzer()
    
    # Test builds
    test_builds = {
        "Weapon Focus Build": ["Crossblades", "Ruby", "Emerald", "Whetstone", "Hero Sword"],
        "Healing Support Build": ["Banana", "Carrot", "Healing Herbs", "Heart Container", "Fanny Pack"],
        "Bag Efficiency Build": ["Fanny Pack", "Stamina Sack", "Potion Belt", "Protective Purse", "Bagtacular"],
        "Combat Hybrid Build": ["Blood Harvester", "Vampiric Gloves", "Ruby", "Sapphire", "Stone Armor"]
    }
    
    print("🔍 COMPREHENSIVE BUILD ANALYSIS SYSTEM")
    print("=" * 50)
    
    for build_name, items in test_builds.items():
        print(f"\n{analyzer.create_build_report(items, build_name)}")
        print("\n" + "="*60)

if __name__ == "__main__":
    main()
