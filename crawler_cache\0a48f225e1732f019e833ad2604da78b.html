<!DOCTYPE html>
<html class="client-nojs view-unknown theme-none skin-theme-clientpref-day" lang="en" dir="ltr">
<head>
<meta charset="UTF-8">
<title>Items - The Backpack Battles Wiki</title>
<script>(function(){var className="client-js view-unknown theme-none skin-theme-clientpref-day";var cookie=document.cookie.match(/(?:^|; )commons_enmwclientpreferences=([^;]+)/);if(cookie){cookie[1].split('%2C').forEach(function(pref){className=className.replace(new RegExp('(^| )'+pref.replace(/-clientpref-\w+$|[^\w-]+/g,'')+'-clientpref-\\w+( |$)'),'$1'+pref+'$2');});}document.documentElement.className=className;}());RLCONF={"wgBreakFrames":false,"wgSeparatorTransformTable":["",""],"wgDigitTransformTable":["",""],"wgDefaultDateFormat":"dmy","wgMonthNames":["","January","February","March","April","May","June","July","August","September","October","November","December"],"wgRequestId":"7ebfff880d5efaf33713b2bb","wgCanonicalNamespace":"","wgCanonicalSpecialPageName":false,"wgNamespaceNumber":0,"wgPageName":"Items","wgTitle":"Items","wgCurRevisionId":10081,"wgRevisionId":10081,"wgArticleId":315,"wgIsArticle":true,"wgIsRedirect":false,"wgAction":"view","wgUserName":null,"wgUserGroups":["*"],
"wgCategories":["Items"],"wgPageViewLanguage":"en","wgPageContentLanguage":"en","wgPageContentModel":"wikitext","wgRelevantPageName":"Items","wgRelevantArticleId":315,"wgIsProbablyEditable":false,"wgRelevantPageIsProbablyEditable":false,"wgRestrictionEdit":[],"wgRestrictionMove":[],"wgPageFormsTargetName":null,"wgPageFormsAutocompleteValues":[],"wgPageFormsAutocompleteOnAllChars":false,"wgPageFormsFieldProperties":[],"wgPageFormsCargoFields":[],"wgPageFormsDependentFields":[],"wgPageFormsCalendarValues":[],"wgPageFormsCalendarParams":[],"wgPageFormsCalendarHTML":null,"wgPageFormsGridValues":[],"wgPageFormsGridParams":[],"wgPageFormsContLangYes":null,"wgPageFormsContLangNo":null,"wgPageFormsContLangMonths":[],"wgPageFormsHeightForMinimizingInstances":800,"wgPageFormsDelayReload":false,"wgPageFormsShowOnSelect":[],"wgPageFormsScriptPath":"/mw-1.43/extensions/PageForms","edgValues":[],"wgPageFormsEDSettings":null,"wgAmericanDates":false,"wgCargoDefaultQueryLimit":2000,
"wgCargoMapClusteringMinimum":80,"wgCargoMonthNamesShort":["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],"egAppHost":"https://app.wiki.gg","wgMediaViewerOnClick":true,"wgMediaViewerEnabledByDefault":true,"wgCiteReferencePreviewsActive":true,"wgCheckUserClientHintsHeadersJsApi":["architecture","bitness","brands","fullVersionList","mobile","model","platform","platformVersion"]};RLSTATE={"ext.globalCssJs.user.styles":"ready","site.styles":"ready","user.styles":"ready","ext.globalCssJs.user":"ready","user":"ready","user.options":"loading","skins.vector.styles.legacy":"ready","jquery.makeCollapsible.styles":"ready","ext.CookieWarning.styles":"ready","oojs-ui-core.styles":"ready","oojs-ui.styles.indicators":"ready","mediawiki.widgets.styles":"ready","oojs-ui-core.icons":"ready","ext.removeredlinks.styles":"ready","ext.usergroupbadges.styles":"ready","ext.embedVideo.styles":"ready","ext.globalui.styles":"ready"};RLPAGEMODULES=["ext.cargo.main","site",
"mediawiki.page.ready","jquery.makeCollapsible","mediawiki.toc","skins.vector.legacy.js","ext.CookieWarning","ext.removeredlinks","ext.themes.switcher","ext.gadget.interwikiDropdownButton","mmv.bootstrap","ext.checkUser.clientHints","ext.embedVideo.overlay","ext.cargo.purge","ext.globalui"];</script>
<script>(RLQ=window.RLQ||[]).push(function(){mw.loader.impl(function(){return["user.options@12s5i",function($,jQuery,require,module){mw.user.tokens.set({"patrolToken":"+\\","watchToken":"+\\","csrfToken":"+\\"});
}];});});</script>
<link rel="stylesheet" href="/load.php?lang=en&amp;modules=ext.CookieWarning.styles%7Cext.embedVideo.styles%7Cext.globalui.styles%7Cext.removeredlinks.styles%7Cext.usergroupbadges.styles%7Cjquery.makeCollapsible.styles%7Cmediawiki.widgets.styles%7Coojs-ui-core.icons%2Cstyles%7Coojs-ui.styles.indicators%7Cskins.vector.styles.legacy&amp;only=styles&amp;skin=vector">
<script async="" src="/load.php?lang=en&amp;modules=startup&amp;only=scripts&amp;raw=1&amp;skin=vector"></script>
<meta name="ResourceLoaderDynamicStyles" content="">
<link rel="stylesheet" href="/load.php?lang=en&amp;modules=site.styles&amp;only=styles&amp;skin=vector">
<script async src="/load.php?lang=en&skin=vector&modules=ext.themes.apply&only=scripts&skin=vector&raw=1"></script>
<meta name="generator" content="MediaWiki 1.43.0">
<meta name="robots" content="max-image-preview:standard">
<meta name="format-detection" content="telephone=no">
<meta name="description" content="Items can be placed in the inventory where they will be brought into battle, or kept in the storage where they will not have any effect.&#10;Each shop item has a 10% chance to go on sale at half of the cost rounded up. The player may sell items at half price by dragging them into the chest. Buying an item...">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, minimum-scale=0.25, maximum-scale=5.0">
<style type="text/css">@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/cyrillic-ext/wght/normal.woff2);unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/cyrillic/wght/normal.woff2);unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/latin-ext/wght/normal.woff2);unicode-range:U+0100-02AF,U+0304,U+0308,U+0329,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/vietnamese/wght/normal.woff2);unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/latin/wght/normal.woff2);unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;font-display:swap;}</style>
<link rel="apple-touch-icon" href="/images/4/4a/Site-favicon.ico">
<link rel="icon" href="/images/4/4a/Site-favicon.ico">
<link rel="search" type="application/opensearchdescription+xml" href="/rest.php/v1/search" title="Backpack Battles Wiki (en)">
<link rel="EditURI" type="application/rsd+xml" href="https://backpackbattles.wiki.gg/api.php?action=rsd">
<link rel="canonical" href="https://backpackbattles.wiki.gg/wiki/Items">
<link rel="license" href="https://creativecommons.org/licenses/by-sa/4.0">
<link rel="alternate" type="application/atom+xml" title="Backpack Battles Wiki Atom feed" href="/wiki/Special:RecentChanges?feed=atom">
	<meta property="fb:app_id" content="1025655121420775" prefix="fb: http://www.facebook.com/2008/fbml">

	<meta property="og:type" content="article">

	<meta property="og:site_name" content="Backpack Battles Wiki">

	<meta property="og:title" content="Items">

	<meta property="og:description" content="Items can be placed in the inventory where they will be brought into battle, or kept in the storage where they will not have any effect.&#10;Each shop item has a 10% chance to go on sale at half of the cost rounded up. The player may sell items at half price by dragging them into the chest. Buying an item...">

	<meta property="og:url" content="https://backpackbattles.wiki.gg/wiki/Items">

<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-P68LVFKN');</script>
<!-- End Google Tag Manager -->
</head>
<body class="wgg-dom-version-1_43 skin-vector-legacy mediawiki ltr sitedir-ltr mw-hide-empty-elt ns-0 ns-subject page-Items rootpage-Items skin-vector action-view skin--responsive"><header id="wgg-netbar">
	<div class="wgg-netbar__left">
		<div class="wgg-netbar__logo">
			<a href="/" aria-label="wiki.gg">
				<img height=25 alt="wiki.gg logo" src="https://commons.wiki.gg/images/2/27/Network_header_logo.svg">
			</a>
		</div>
	</div>

	<nav class="wgg-netbar__user-links" aria-label="Personal tools">
		<div id="p-personal" class="mw-portlet mw-portlet-personal"  >
		    
		    <ul>
		            <li id="pt-createaccount" class="mw-list-item wgg-netbar__desktop-only"><a data-mw="interface" class=""  href="/wiki/Special:CreateAccount?returnto=Items" title="You are encouraged to create an account and log in; however, it is not mandatory"><span>Create account</span></a></li>
		            <li id="pt-login" class="mw-list-item wgg-netbar__desktop-only"><a data-mw="interface" class=""  href="/wiki/Special:UserLogin?returnto=Items" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o"><span>Log in</span></a></li>
		    </ul>
		    
		</div>

		<div id="wgg-user-menu-overflow" class="wgg-netbar__dropdown wgg-netbar__mobile-only">
			<input type="checkbox"
				id="wgg-user-menu-overflow-checkbox"
				role="button"
				aria-haspopup="true"
				aria-labelledby="wgg-user-menu-overflow-label"
			>
			<label
				id="wgg-user-menu-overflow-label"
		        for="wgg-user-menu-overflow-checkbox"
				class="wgg-netbar__icon-button"
			><span class="wgg-netbar-icon wgg-netbar-icon--overflow"></span><span>Toggle personal tools menu</span></label>

			<div class="wgg-netbar__dropdown-content">
				<div id="p-user-menu" class="mw-portlet mw-portlet-user-menu"  >
				    
				    <ul>
				            <li id="pt-createaccount-overflow" class="mw-list-item wgg-netbar__mobile-only"><a data-mw="interface" class=""  href="/wiki/Special:CreateAccount?returnto=Items" title="You are encouraged to create an account and log in; however, it is not mandatory"><span>Create account</span></a></li>
				            <li id="pt-login-overflow" class="mw-list-item wgg-netbar__mobile-only"><a data-mw="interface" class=""  href="/wiki/Special:UserLogin?returnto=Items" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o"><span>Log in</span></a></li>
				    </ul>
				    
				</div>
			</div>
		</div>
</nav>

</header>

<div id="mw-page-base" class="noprint"></div>
<div id="mw-head-base" class="noprint"></div>
<div class="content-wrapper">
	<div id="mw-navigation">
		<h2>Navigation menu</h2>
		<div id="mw-head">
			<div id="left-navigation">
				
<div id="p-namespaces" class="mw-portlet mw-portlet-namespaces vector-menu-tabs vector-menu-tabs-legacy vectorTabs vector-menu" aria-labelledby="p-namespaces-label"  >
	<h3
		id="p-namespaces-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">Namespaces</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="ca-nstab-main" class="selected mw-list-item"><a href="/wiki/Items" title="View the content page [c]" accesskey="c"><span>Page</span></a></li><li id="ca-talk" class="new mw-list-item"><a href="/wiki/Talk:Items?action=edit&amp;redlink=1" rel="discussion" class="new" title="Discussion about the content page (page does not exist) [t]" accesskey="t"><span>Discussion</span></a></li>
		</ul>
		
	</div>
</div>

				
<div id="p-variants" class="mw-portlet mw-portlet-variants emptyPortlet vector-menu-dropdown vector-menu" aria-labelledby="p-variants-label"  >
	<input type="checkbox"
		id="p-variants-checkbox"
		role="button"
		aria-haspopup="true"
		data-event-name="ui.dropdown-p-variants"
		class="vector-menu-checkbox"
		aria-labelledby="p-variants-label"
	>
	<label
		id="p-variants-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">English</span>
	</label>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			
		</ul>
		
	</div>
</div>

			</div>
			<div id="right-navigation">
				
<div id="p-views" class="mw-portlet mw-portlet-views vector-menu-tabs vector-menu-tabs-legacy vectorTabs vector-menu" aria-labelledby="p-views-label"  >
	<h3
		id="p-views-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">Views</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="ca-view" class="selected mw-list-item"><a href="/wiki/Items"><span>Read</span></a></li><li id="ca-edit" class="mw-list-item"><a href="/wiki/Special:CreateAccount?warning=accountrequiredtoedit&amp;returnto=Items&amp;returntoquery=action%3Dedit" title="Edit this page [e]" accesskey="e"><span>Sign up to edit</span></a></li><li id="ca-viewsource" class="mw-list-item"><a href="/wiki/Items?action=edit" title="This page is protected.&#10;You can view its source [e]" accesskey="e"><span>View source</span></a></li><li id="ca-history" class="mw-list-item"><a href="/wiki/Items?action=history" title="Past revisions of this page [h]" accesskey="h"><span>History</span></a></li>
		</ul>
		
	</div>
</div>

				
<div id="p-cactions" class="mw-portlet mw-portlet-cactions vectorMenu vector-menu-dropdown vector-menu" aria-labelledby="p-cactions-label"  title="More options" >
	<input type="checkbox"
		id="p-cactions-checkbox"
		role="button"
		aria-haspopup="true"
		data-event-name="ui.dropdown-p-cactions"
		class="vector-menu-checkbox"
		aria-labelledby="p-cactions-label"
	>
	<label
		id="p-cactions-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">More</span>
	</label>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="ca-cargo-purge" class="mw-list-item"><a href="/wiki/Items?action=purge"><span>Purge cache</span></a></li>
		</ul>
		
	</div>
</div>

				
<div id="p-search" role="search" class="vector-search-box-vue  vector-search-box-show-thumbnail vector-search-box-auto-expand-width vector-search-box">
	<h3 >Search</h3>
	<form action="/wiki/Special:Search" id="searchform" class="vector-search-box-form">
		<div id="simpleSearch"
			class="vector-search-box-inner"
			 data-search-loc="header-navigation">
			<input class="vector-search-box-input"
				 type="search" name="search" placeholder="Search Backpack Battles Wiki" aria-label="Search Backpack Battles Wiki" autocapitalize="sentences" title="Search Backpack Battles Wiki [f]" accesskey="f" id="searchInput"
			>
			<input id="mw-searchButton"
				 class="searchButton mw-fallbackSearchButton" type="submit" name="fulltext" title="Search the pages for this text" value="Search">
			<input id="searchButton"
				 class="searchButton" type="submit" name="go" title="Go to a page with this exact name if it exists" value="Go">
		</div>
	</form>
</div>

			</div>
		</div>
		
<div id="mw-panel">
	<div id="p-logo" role="banner">
		<a class="mw-wiki-logo" href="/"
			title="Visit the main page"></a>
	</div>
	
<div id="p-Content" class="mw-portlet mw-portlet-Content vector-menu-portal portal vector-menu" aria-labelledby="p-Content-label"  >
	<h3
		id="p-Content-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">Content</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="n-mainpage-description" class="mw-list-item"><a href="/" title="Visit the main page [z]" accesskey="z"><span>Main page</span></a></li><li id="n-Backpack-Battles" class="mw-list-item"><a href="/wiki/Backpack_Battles"><span>Backpack Battles</span></a></li><li id="n-Items" class="mw-list-item"><a href="/wiki/Items"><span>Items</span></a></li><li id="n-Game-Mechanics" class="mw-list-item"><a href="/wiki/Game_Mechanics"><span>Game Mechanics</span></a></li><li id="n-Characters" class="mw-list-item"><a href="/wiki/Characters"><span>Characters</span></a></li><li id="n-Version-History" class="mw-list-item"><a href="/wiki/Version_History"><span>Version History</span></a></li>
		</ul>
		
	</div>
</div>

	
<div id="p-navigation" class="mw-portlet mw-portlet-navigation vector-menu-portal portal vector-menu" aria-labelledby="p-navigation-label"  >
	<h3
		id="p-navigation-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">Navigation</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="n-portal" class="mw-list-item"><a href="/wiki/Backpack_Battles_Wiki:Community_portal" title="About the project, what you can do, where to find things"><span>Community portal</span></a></li><li id="n-recentchanges" class="mw-list-item"><a href="/wiki/Special:RecentChanges" title="A list of recent changes in the wiki [r]" accesskey="r"><span>Recent changes</span></a></li><li id="n-randompage" class="mw-list-item"><a href="/wiki/Special:Random" title="Load a random page [x]" accesskey="x"><span>Random page</span></a></li><li id="n-help-mediawiki" class="mw-list-item"><a href="https://www.mediawiki.org/wiki/Special:MyLanguage/Help:Contents"><span>Help about MediaWiki</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-tb" class="mw-portlet mw-portlet-tb vector-menu-portal portal vector-menu" aria-labelledby="p-tb-label"  >
	<h3
		id="p-tb-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">Tools</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="t-whatlinkshere" class="mw-list-item"><a href="/wiki/Special:WhatLinksHere/Items" title="A list of all wiki pages that link here [j]" accesskey="j"><span>What links here</span></a></li><li id="t-recentchangeslinked" class="mw-list-item"><a href="/wiki/Special:RecentChangesLinked/Items" rel="nofollow" title="Recent changes in pages linked from this page [k]" accesskey="k"><span>Related changes</span></a></li><li id="t-newpage" class="mw-list-item"><a href="/wiki/Special:NewPage" accesskey="]"><span>New page</span></a></li><li id="t-specialpages" class="mw-list-item"><a href="/wiki/Special:SpecialPages" title="A list of all special pages [q]" accesskey="q"><span>Special pages</span></a></li><li id="t-print" class="mw-list-item"><a href="javascript:print();" rel="alternate" title="Printable version of this page [p]" accesskey="p"><span>Printable version</span></a></li><li id="t-permalink" class="mw-list-item"><a href="/wiki/Items?oldid=10081" title="Permanent link to this revision of this page"><span>Permanent link</span></a></li><li id="t-info" class="mw-list-item"><a href="/wiki/Items?action=info" title="More information about this page"><span>Page information</span></a></li><li id="t-cargopagevalueslink" class="mw-list-item"><a href="/wiki/Items?action=pagevalues" rel="cargo-pagevalues"><span>Cargo data</span></a></li>
		</ul>
		
	</div>
</div>

	
</div>

	</div>
	<div id="content" class="mw-body" role="main">
		<aside id="wikigg-sl-header" data-mw="WggShowcaseLayout">
        <div
            class="wikigg-showcase__unit wikigg-showcase__reserved--lb"
            data-wgg-unit-type="internal"
        ></div>
</aside>

		<div class="content-body">
			<main>
				<a id="top"></a>
				<div id="siteNotice"></div>
				<div class="mw-indicators">
				</div>
				<h1 id="firstHeading" class="firstHeading mw-first-heading"><span class="mw-page-title-main">Items</span></h1>
				<div id="bodyContent" class="vector-body">
					<div id="siteSub" class="noprint">From Backpack Battles Wiki</div>
					<div id="contentSub"></div>
					<div id="contentSub2"></div>
					
					<div id="jump-to-nav"></div>
					<a class="mw-jump-link" href="#mw-head">Jump to navigation</a>
					<a class="mw-jump-link" href="#searchInput">Jump to search</a>
					<div id="mw-content-text" class="mw-body-content"><div class="mw-content-ltr mw-parser-output" lang="en" dir="ltr"><p><b>Items</b> can be placed in the inventory where they will be brought into battle, or kept in the storage where they will not have any effect.
</p><p>Each shop item has a 10% chance to go on sale at half of the cost rounded up. The player may sell items at half price by dragging them into the <a href="/wiki/Characters#Chestnut" title="Characters">chest</a>. Buying an item on sale and selling it will result in no gold change.
</p><p>Items can be combined using <a href="/wiki/Recipe" title="Recipe">recipes</a>.
</p>
<div id="toc" class="toc" role="navigation" aria-labelledby="mw-toc-heading"><input type="checkbox" role="button" id="toctogglecheckbox" class="toctogglecheckbox" style="display:none" /><div class="toctitle" lang="en" dir="ltr"><h2 id="mw-toc-heading">Contents</h2><span class="toctogglespan"><label class="toctogglelabel" for="toctogglecheckbox"></label></span></div>
<ul>
<li class="toclevel-1 tocsection-1"><a href="#Class_Items"><span class="tocnumber">1</span> <span class="toctext">Class Items</span></a></li>
<li class="toclevel-1 tocsection-2"><a href="#Types"><span class="tocnumber">2</span> <span class="toctext">Types</span></a>
<ul>
<li class="toclevel-2 tocsection-3"><a href="#Extra_Types"><span class="tocnumber">2.1</span> <span class="toctext">Extra Types</span></a></li>
</ul>
</li>
<li class="toclevel-1 tocsection-4"><a href="#Rarities"><span class="tocnumber">3</span> <span class="toctext">Rarities</span></a></li>
</ul>
</div>

<h2><span class="mw-headline" id="Class_Items">Class Items</span></h2>
<p><a href="/wiki/Neutral_items" title="Neutral items"><img alt="Neutral items" src="/images/thumb/3/37/NeutralItems.png/150px-NeutralItems.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Ranger_items" title="Ranger items"><img alt="Ranger items" src="/images/thumb/a/a7/RangerItems.png/150px-RangerItems.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Reaper_items" title="Reaper items"><img alt="Reaper items" src="/images/thumb/d/df/ReaperItems.png/150px-ReaperItems.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Berserker_items" title="Berserker items"><img alt="Berserker items" src="/images/thumb/6/6a/BerserkerItems.png/150px-BerserkerItems.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Pyromancer_items" title="Pyromancer items"><img alt="Pyromancer items" src="/images/thumb/6/6e/PyromancerItems.png/150px-PyromancerItems.png" decoding="async" loading="lazy" width="150" height="132" /></a>
</p>
<h2><span class="mw-headline" id="Types">Types</span></h2>
<p><a href="/wiki/Accessory" title="Accessory"><img alt="Accessory" src="/images/thumb/f/f4/Accessory.png/150px-Accessory.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Armor" title="Armor"><img alt="Armor" src="/images/thumb/0/06/Armor.png/150px-Armor.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Bag" title="Bag"><img alt="Bag" src="/images/thumb/d/d4/Bag.png/150px-Bag.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Food" title="Food"><img alt="Food" src="/images/thumb/c/c6/Food.png/150px-Food.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Gemstone" title="Gemstone"><img alt="Gemstone" src="/images/thumb/0/0a/Gemstone.png/150px-Gemstone.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Gloves" title="Gloves"><img alt="Gloves" src="/images/thumb/3/37/Gloves.png/150px-Gloves.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Helmet" title="Helmet"><img alt="Helmet" src="/images/thumb/3/37/Helmet.png/150px-Helmet.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Pet" title="Pet"><img alt="Pet" src="/images/thumb/1/11/Pet.png/150px-Pet.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Playing_Card" title="Playing Card"><img alt="Playing Card" src="/images/thumb/8/81/PlayingCard.png/150px-PlayingCard.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Potion" title="Potion"><img alt="Potion" src="/images/thumb/7/7a/Potion.png/150px-Potion.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Shield" title="Shield"><img alt="Shield" src="/images/thumb/c/cf/Shield.png/150px-Shield.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Shoes" title="Shoes"><img alt="Shoes" src="/images/thumb/8/82/Shoes.png/150px-Shoes.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Skill" title="Skill"><img alt="Skill" src="/images/thumb/7/74/Skill.png/150px-Skill.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Spell_Scroll" title="Spell Scroll"><img alt="Spell Scroll" src="/images/thumb/0/01/Spell_Scroll.png/150px-Spell_Scroll.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Weapon" title="Weapon"><img alt="Weapon" src="/images/thumb/f/fb/Weapon.png/150px-Weapon.png" decoding="async" loading="lazy" width="150" height="132" /></a>
</p>
<h4><span class="mw-headline" id="Extra_Types">Extra Types</span></h4>
<p><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/8/8f/Melee.png/150px-Melee.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/c/c8/Ranged.png/150px-Ranged.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/3/35/Effect.png/150px-Effect.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/a/a7/Nature.png/150px-Nature.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/f/f7/Magic.png/150px-Magic.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/a/a3/Holy.png/150px-Holy.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/0/0e/Dark.png/150px-Dark.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/3/3a/Vampiric.png/150px-Vampiric.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Fire" title="Fire"><img alt="Fire" src="/images/thumb/3/30/Fire.png/150px-Fire.png" decoding="async" loading="lazy" width="150" height="132" /></a>
<a href="/wiki/Ice" title="Ice"><img alt="Ice" src="/images/thumb/7/77/Ice.png/150px-Ice.png" decoding="async" loading="lazy" width="150" height="132" /></a>
</p>
<h2><span class="mw-headline" id="Rarities"><a href="/wiki/Rarity" title="Rarity">Rarities</a></span></h2>
<p><a href="/wiki/Common" title="Common"><img alt="Common" src="/images/thumb/5/5f/Common.png/150px-Common.png" decoding="async" loading="lazy" width="150" height="150" /></a>
<a href="/wiki/Rare" title="Rare"><img alt="Rare" src="/images/thumb/9/9f/Rare.png/150px-Rare.png" decoding="async" loading="lazy" width="150" height="150" /></a>
<a href="/wiki/Epic" title="Epic"><img alt="Epic" src="/images/thumb/2/26/Epic.png/150px-Epic.png" decoding="async" loading="lazy" width="150" height="156" /></a>
<a href="/wiki/Legendary" title="Legendary"><img alt="Legendary" src="/images/thumb/e/e2/Legendary.png/150px-Legendary.png" decoding="async" loading="lazy" width="150" height="146" /></a>
<a href="/wiki/Godly" title="Godly"><img alt="Godly" src="/images/thumb/1/19/Godly.png/150px-Godly.png" decoding="async" loading="lazy" width="150" height="150" /></a>
<a href="/wiki/Unique" title="Unique"><img alt="Unique" src="/images/thumb/9/93/Unique.png/150px-Unique.png" decoding="async" loading="lazy" width="150" height="150" /></a>
</p><p><br />
</p>
<table class="navbox mw-collapsible" data-expandtext="Show" data-collapsetext="Hide"><tbody><tr class="navbox-title"><th colspan="2" scope="col"><span class="navbox-vde"><a href="/wiki/Template:Items" title="Template:Items"><span title="View this navbox template">v</span></a> · <span class="plainlinks"><a class="external text" href="https://backpackbattles.wiki.gg/wiki/Template_talk:Items"><span title="Discuss this navbox template">d</span></a> · <a class="external text" href="https://backpackbattles.wiki.gg/wiki/Template:Items?action=edit"><span title="Edit this navbox template">e</span></a></span></span><a class="mw-selflink selflink">Items</a></th></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Accessory" title="Accessory">Accessory</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Acorn_Collar" title="Acorn Collar">Acorn Collar</a>&#160;• <a href="/wiki/Amulet_of_Alchemy" title="Amulet of Alchemy">Amulet of Alchemy</a>&#160;• <a href="/wiki/Amulet_of_Darkness" title="Amulet of Darkness">Amulet of Darkness</a>&#160;• <a href="/wiki/Amulet_of_Energy" title="Amulet of Energy">Amulet of Energy</a>&#160;• <a href="/wiki/Amulet_of_Feasting" title="Amulet of Feasting">Amulet of Feasting</a>&#160;• <a href="/wiki/Amulet_of_Life" title="Amulet of Life">Amulet of Life</a>&#160;• <a href="/wiki/Amulet_of_Steel" title="Amulet of Steel">Amulet of Steel</a>&#160;• <a href="/wiki/Amulet_of_the_Wild" title="Amulet of the Wild">Amulet of the Wild</a>&#160;• <a href="/wiki/Anvil" title="Anvil">Anvil</a>&#160;• <a href="/wiki/Bag_of_Stones" title="Bag of Stones">Bag of Stones</a>&#160;• <a href="/wiki/Blood_Amulet" title="Blood Amulet">Blood Amulet</a>&#160;• <a href="/wiki/Book_of_Ice" title="Book of Ice">Book of Ice</a>&#160;• <a href="/wiki/Box_of_Riches" title="Box of Riches">Box of Riches</a>&#160;• <a href="/wiki/Bunch_of_Coins" title="Bunch of Coins">Bunch of Coins</a>&#160;• <a href="/wiki/Burning_Banner" title="Burning Banner">Burning Banner</a>&#160;• <a href="/wiki/Cauldron" title="Cauldron">Cauldron</a>&#160;• <a href="/wiki/Customer_Card" title="Customer Card">Customer Card</a>&#160;• <a href="/wiki/Dark_Lantern" title="Dark Lantern">Dark Lantern</a>&#160;• <a href="/wiki/Deck_of_Cards" title="Deck of Cards">Deck of Cards</a>&#160;• <a href="/wiki/Deerwood_Guardian" title="Deerwood Guardian">Deerwood Guardian</a>&#160;• <a href="/wiki/Djinn_Lamp" title="Djinn Lamp">Djinn Lamp</a>&#160;• <a href="/wiki/Draconic_Orb" title="Draconic Orb">Draconic Orb</a>&#160;• <a href="/wiki/Dragon_Nest" title="Dragon Nest">Dragon Nest</a>&#160;• <a href="/wiki/Fanfare" title="Fanfare">Fanfare</a>&#160;• <a href="/wiki/Flame" title="Flame">Flame</a>&#160;• <a href="/wiki/Flame_Badge" title="Flame Badge">Flame Badge</a>&#160;• <a href="/wiki/Flute" title="Flute">Flute</a>&#160;• <a href="/wiki/Friendly_Fire" title="Friendly Fire">Friendly Fire</a>&#160;• <a href="/wiki/Frozen_Flame" title="Frozen Flame">Frozen Flame</a>&#160;• <a href="/wiki/Healing_Herbs" title="Healing Herbs">Healing Herbs</a>&#160;• <a href="/wiki/Heart_Container" title="Heart Container">Heart Container</a>&#160;• <a href="/wiki/Heart_of_Darkness" title="Heart of Darkness">Heart of Darkness</a>&#160;• <a href="/wiki/King_Crown" title="King Crown">King Crown</a>&#160;• <a href="/wiki/Leaf_Badge" title="Leaf Badge">Leaf Badge</a>&#160;• <a href="/wiki/Lucky_Clover" title="Lucky Clover">Lucky Clover</a>&#160;• <a href="/wiki/Lucky_Piggy" title="Lucky Piggy">Lucky Piggy</a>&#160;• <a href="/wiki/Mana_Orb" title="Mana Orb">Mana Orb</a>&#160;• <a href="/wiki/Maneki-neko" title="Maneki-neko">Maneki-neko</a>&#160;• <a href="/wiki/Mega_Clover" title="Mega Clover">Mega Clover</a>&#160;• <a href="/wiki/Miss_Fortune" title="Miss Fortune">Miss Fortune</a>&#160;• <a href="/wiki/Mr._Struggles" title="Mr. Struggles">Mr. Struggles</a>&#160;• <a href="/wiki/Mrs._Struggles" title="Mrs. Struggles">Mrs. Struggles</a>&#160;• <a href="/wiki/Nocturnal_Lock_Lifter" title="Nocturnal Lock Lifter">Nocturnal Lock Lifter</a>&#160;• <a href="/wiki/Oil_Lamp" title="Oil Lamp">Oil Lamp</a>&#160;• <a href="/wiki/Piercing_Arrow" title="Piercing Arrow">Piercing Arrow</a>&#160;• <a href="/wiki/Piggybank" title="Piggybank">Piggybank</a>&#160;• <a href="/wiki/Platinum_Customer_Card" title="Platinum Customer Card">Platinum Customer Card</a>&#160;• <a href="/wiki/Pocket_Sand" title="Pocket Sand">Pocket Sand</a>&#160;• <a href="/wiki/Poison_Ivy" title="Poison Ivy">Poison Ivy</a>&#160;• <a href="/wiki/Present" title="Present">Present</a>&#160;• <a href="/wiki/Prismatic_Orb" title="Prismatic Orb">Prismatic Orb</a>&#160;• <a href="/wiki/Rainbow_Badge" title="Rainbow Badge">Rainbow Badge</a>&#160;• <a href="/wiki/Shaman_Mask" title="Shaman Mask">Shaman Mask</a>&#160;• <a href="/wiki/Shepherd%27s_Crook" class="mw-redirect" title="Shepherd&#39;s Crook">Shepherd's Crook</a>&#160;• <a href="/wiki/Shiny_Shell" title="Shiny Shell">Shiny Shell</a>&#160;• <a href="/wiki/Skull_Badge" title="Skull Badge">Skull Badge</a>&#160;• <a href="/wiki/Snowball" title="Snowball">Snowball</a>&#160;• <a href="/wiki/Spiked_Collar" title="Spiked Collar">Spiked Collar</a>&#160;• <a href="/wiki/Stable_Recombobulator" title="Stable Recombobulator">Stable Recombobulator</a>&#160;• <a href="/wiki/Stone_Badge" title="Stone Badge">Stone Badge</a>&#160;• <a href="/wiki/Time_Dilator" title="Time Dilator">Time Dilator</a>&#160;• <a href="/wiki/Unidentified_Amulet" title="Unidentified Amulet">Unidentified Amulet</a>&#160;• <a href="/wiki/Unstable_Recombobulator" title="Unstable Recombobulator">Unstable Recombobulator</a>&#160;• <a href="/wiki/Walrus_Tusk" title="Walrus Tusk">Walrus Tusk</a>&#160;• <a href="/wiki/Whetstone" title="Whetstone">Whetstone</a>&#160;• <a href="/wiki/Wolf_Badge" title="Wolf Badge">Wolf Badge</a>&#160;• <a href="/wiki/Wolf_Emblem" title="Wolf Emblem">Wolf Emblem</a>&#160;• <a href="/wiki/Wonky_Snowman" title="Wonky Snowman">Wonky Snowman</a>&#160;• <a href="/wiki/Yggdrasil_Leaf" title="Yggdrasil Leaf">Yggdrasil Leaf</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Armor" title="Armor">Armor</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Corrupted_Armor" title="Corrupted Armor">Corrupted Armor</a>&#160;• <a href="/wiki/Dragonscale_Armor" title="Dragonscale Armor">Dragonscale Armor</a>&#160;• <a href="/wiki/Holy_Armor" title="Holy Armor">Holy Armor</a>&#160;• <a href="/wiki/Ice_Armor" title="Ice Armor">Ice Armor</a>&#160;• <a href="/wiki/Leather_Armor" title="Leather Armor">Leather Armor</a>&#160;• <a href="/wiki/Moon_Armor" title="Moon Armor">Moon Armor</a>&#160;• <a href="/wiki/Stone_Armor" title="Stone Armor">Stone Armor</a>&#160;• <a href="/wiki/Sun_Armor" title="Sun Armor">Sun Armor</a>&#160;• <a href="/wiki/Vampiric_Armor" title="Vampiric Armor">Vampiric Armor</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Bag" title="Bag">Bag</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Box_of_Prosperity" title="Box of Prosperity">Box of Prosperity</a>&#160;• <a href="/wiki/Duffle_Bag" title="Duffle Bag">Duffle Bag</a>&#160;• <a href="/wiki/Fanny_Pack" title="Fanny Pack">Fanny Pack</a>&#160;• <a href="/wiki/Fire_Pit" title="Fire Pit">Fire Pit</a>&#160;• <a href="/wiki/Leather_Bag" title="Leather Bag">Leather Bag</a>&#160;• <a href="/wiki/Offering_Bowl" title="Offering Bowl">Offering Bowl</a>&#160;• <a href="/wiki/Potion_Belt" title="Potion Belt">Potion Belt</a>&#160;• <a href="/wiki/Protective_Purse" title="Protective Purse">Protective Purse</a>&#160;• <a href="/wiki/Ranger_Bag" title="Ranger Bag">Ranger Bag</a>&#160;• <a href="/wiki/Relic_Case" title="Relic Case">Relic Case</a>&#160;• <a href="/wiki/Sack_of_Surprises" title="Sack of Surprises">Sack of Surprises</a>&#160;• <a href="/wiki/Stamina_Sack" title="Stamina Sack">Stamina Sack</a>&#160;• <a href="/wiki/Storage_Coffin" title="Storage Coffin">Storage Coffin</a>&#160;• <a href="/wiki/Utility_Pouch" title="Utility Pouch">Utility Pouch</a>&#160;• <a href="/wiki/Vineweave_Basket" title="Vineweave Basket">Vineweave Basket</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Food" title="Food">Food</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Banana" title="Banana">Banana</a>&#160;• <a href="/wiki/Big_Bowl_of_Treats" title="Big Bowl of Treats">Big Bowl of Treats</a>&#160;• <a href="/wiki/Blueberries" title="Blueberries">Blueberries</a>&#160;• <a href="/wiki/Carrot" title="Carrot">Carrot</a>&#160;• <a href="/wiki/Cheese" title="Cheese">Cheese</a>&#160;• <a href="/wiki/Chili_Pepper" title="Chili Pepper">Chili Pepper</a>&#160;• <a href="/wiki/Doom_Cap" title="Doom Cap">Doom Cap</a>&#160;• <a href="/wiki/Fly_Agaric" title="Fly Agaric">Fly Agaric</a>&#160;• <a href="/wiki/Garlic" title="Garlic">Garlic</a>&#160;• <a href="/wiki/Gingerbread_Jerry" title="Gingerbread Jerry">Gingerbread Jerry</a>&#160;• <a href="/wiki/Pineapple" title="Pineapple">Pineapple</a>&#160;• <a href="/wiki/Pumpkin" title="Pumpkin">Pumpkin</a>&#160;• <a href="/wiki/Snowcake" title="Snowcake">Snowcake</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Gemstone" title="Gemstone">Gemstone</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Amethyst" title="Amethyst">Amethyst</a>&#160;• <a href="/wiki/Badger_Rune" title="Badger Rune">Badger Rune</a>&#160;• <a href="/wiki/Burning_Coal" title="Burning Coal">Burning Coal</a>&#160;• <a href="/wiki/Corrupted_Crystal" title="Corrupted Crystal">Corrupted Crystal</a>&#160;• <a href="/wiki/Elephant_Rune" title="Elephant Rune">Elephant Rune</a>&#160;• <a href="/wiki/Emerald" title="Emerald">Emerald</a>&#160;• <a href="/wiki/Hawk_Rune" title="Hawk Rune">Hawk Rune</a>&#160;• <a href="/wiki/Lump_of_Coal" title="Lump of Coal">Lump of Coal</a>&#160;• <a href="/wiki/Ruby" title="Ruby">Ruby</a>&#160;• <a href="/wiki/Sapphire" title="Sapphire">Sapphire</a>&#160;• <a href="/wiki/Tim" title="Tim">Tim</a>&#160;• <a href="/wiki/Topaz" title="Topaz">Topaz</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Gloves" title="Gloves">Gloves</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Dragon_Claws" title="Dragon Claws">Dragon Claws</a>&#160;• <a href="/wiki/Gloves_of_Haste" title="Gloves of Haste">Gloves of Haste</a>&#160;• <a href="/wiki/Gloves_of_Power" title="Gloves of Power">Gloves of Power</a>&#160;• <a href="/wiki/Vampiric_Gloves" title="Vampiric Gloves">Vampiric Gloves</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Helmet" title="Helmet">Helmet</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Cap_of_Discomfort" title="Cap of Discomfort">Cap of Discomfort</a>&#160;• <a href="/wiki/Cap_of_Resilience" title="Cap of Resilience">Cap of Resilience</a>&#160;• <a href="/wiki/Glowing_Crown" title="Glowing Crown">Glowing Crown</a>&#160;• <a href="/wiki/Stone_Helm" title="Stone Helm">Stone Helm</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Pet" title="Pet">Pet</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Amethyst_Egg" title="Amethyst Egg">Amethyst Egg</a>&#160;• <a href="/wiki/Armored_Courage_Puppy" title="Armored Courage Puppy">Armored Courage Puppy</a>&#160;• <a href="/wiki/Armored_Power_Puppy" title="Armored Power Puppy">Armored Power Puppy</a>&#160;• <a href="/wiki/Armored_Wisdom_Puppy" title="Armored Wisdom Puppy">Armored Wisdom Puppy</a>&#160;• <a href="/wiki/Blood_Goobert" title="Blood Goobert">Blood Goobert</a>&#160;• <a href="/wiki/Carrot_Goobert" title="Carrot Goobert">Carrot Goobert</a>&#160;• <a href="/wiki/Cheese_Goobert" title="Cheese Goobert">Cheese Goobert</a>&#160;• <a href="/wiki/Chili_Goobert" title="Chili Goobert">Chili Goobert</a>&#160;• <a href="/wiki/Chtulhu" title="Chtulhu">Chtulhu</a>&#160;• <a href="/wiki/Courage_Puppy" title="Courage Puppy">Courage Puppy</a>&#160;• <a href="/wiki/Cubert" title="Cubert">Cubert</a>&#160;• <a href="/wiki/Emerald_Egg" title="Emerald Egg">Emerald Egg</a>&#160;• <a href="/wiki/Emerald_Whelp" title="Emerald Whelp">Emerald Whelp</a>&#160;• <a href="/wiki/Goobert" title="Goobert">Goobert</a>&#160;• <a href="/wiki/Goobling" title="Goobling">Goobling</a>&#160;• <a href="/wiki/Hedgehog" title="Hedgehog">Hedgehog</a>&#160;• <a href="/wiki/Ice_Dragon" title="Ice Dragon">Ice Dragon</a>&#160;• <a href="/wiki/Jynx_torquilla" title="Jynx torquilla">Jynx torquilla</a>&#160;• <a href="/wiki/King_Goobert" title="King Goobert">King Goobert</a>&#160;• <a href="/wiki/Light_Goobert" title="Light Goobert">Light Goobert</a>&#160;• <a href="/wiki/Obsidian_Dragon" title="Obsidian Dragon">Obsidian Dragon</a>&#160;• <a href="/wiki/Phoenix" title="Phoenix">Phoenix</a>&#160;• <a href="/wiki/Poison_Goobert" title="Poison Goobert">Poison Goobert</a>&#160;• <a href="/wiki/Pop" title="Pop">Pop</a>&#160;• <a href="/wiki/Power_Puppy" title="Power Puppy">Power Puppy</a>&#160;• <a href="/wiki/Rainbow_Goobert_Deathslushy_Mansquisher" title="Rainbow Goobert Deathslushy Mansquisher">Rainbow Goobert Deathslushy Mansquisher</a>&#160;• <a href="/wiki/Rainbow_Goobert_Epicglob_Uberviscous" title="Rainbow Goobert Epicglob Uberviscous">Rainbow Goobert Epicglob Uberviscous</a>&#160;• <a href="/wiki/Rainbow_Goobert_Megasludge_Alphapuddle" title="Rainbow Goobert Megasludge Alphapuddle">Rainbow Goobert Megasludge Alphapuddle</a>&#160;• <a href="/wiki/Rainbow_Goobert_Omegaooze_Primeslime" title="Rainbow Goobert Omegaooze Primeslime">Rainbow Goobert Omegaooze Primeslime</a>&#160;• <a href="/wiki/Rat" title="Rat">Rat</a>&#160;• <a href="/wiki/Rat_Chef" title="Rat Chef">Rat Chef</a>&#160;• <a href="/wiki/Ruby_Chonk" title="Ruby Chonk">Ruby Chonk</a>&#160;• <a href="/wiki/Ruby_Egg" title="Ruby Egg">Ruby Egg</a>&#160;• <a href="/wiki/Ruby_Whelp" title="Ruby Whelp">Ruby Whelp</a>&#160;• <a href="/wiki/Sapphire_Egg" title="Sapphire Egg">Sapphire Egg</a>&#160;• <a href="/wiki/Sapphire_Whelp" title="Sapphire Whelp">Sapphire Whelp</a>&#160;• <a href="/wiki/Snake" title="Snake">Snake</a>&#160;• <a href="/wiki/Squirrel" title="Squirrel">Squirrel</a>&#160;• <a href="/wiki/Squirrel_Archer" title="Squirrel Archer">Squirrel Archer</a>&#160;• <a href="/wiki/Steel_Goobert" title="Steel Goobert">Steel Goobert</a>&#160;• <a href="/wiki/Stone_Golem" title="Stone Golem">Stone Golem</a>&#160;• <a href="/wiki/Toad" title="Toad">Toad</a>&#160;• <a href="/wiki/Unsettling_Presence" title="Unsettling Presence">Unsettling Presence</a>&#160;• <a href="/wiki/Wisdom_Puppy" title="Wisdom Puppy">Wisdom Puppy</a>&#160;• <a href="/wiki/Wolpertinger" title="Wolpertinger">Wolpertinger</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Playing_Card" title="Playing Card">Playing Card</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Ace_of_Spades" title="Ace of Spades">Ace of Spades</a>&#160;• <a href="/wiki/Darkest_Lotus" title="Darkest Lotus">Darkest Lotus</a>&#160;• <a href="/wiki/Holo_Fire_Lizard" title="Holo Fire Lizard">Holo Fire Lizard</a>&#160;• <a href="/wiki/Jimbo" title="Jimbo">Jimbo</a>&#160;• <a href="/wiki/Reverse!" title="Reverse!">Reverse!</a>&#160;• <a href="/wiki/The_Fool" title="The Fool">The Fool</a>&#160;• <a href="/wiki/The_Lovers" title="The Lovers">The Lovers</a>&#160;• <a href="/wiki/White-Eyes_Blue_Dragon" title="White-Eyes Blue Dragon">White-Eyes Blue Dragon</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Potion" title="Potion">Potion</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Demonic_Flask" title="Demonic Flask">Demonic Flask</a>&#160;• <a href="/wiki/Divine_Potion" title="Divine Potion">Divine Potion</a>&#160;• <a href="/wiki/Health_Potion" title="Health Potion">Health Potion</a>&#160;• <a href="/wiki/Heroic_Potion" title="Heroic Potion">Heroic Potion</a>&#160;• <a href="/wiki/Mana_Potion" title="Mana Potion">Mana Potion</a>&#160;• <a href="/wiki/Pestilence_Flask" title="Pestilence Flask">Pestilence Flask</a>&#160;• <a href="/wiki/Stone_Skin_Potion" title="Stone Skin Potion">Stone Skin Potion</a>&#160;• <a href="/wiki/Strong_Demonic_Flask" title="Strong Demonic Flask">Strong Demonic Flask</a>&#160;• <a href="/wiki/Strong_Divine_Potion" title="Strong Divine Potion">Strong Divine Potion</a>&#160;• <a href="/wiki/Strong_Health_Potion" title="Strong Health Potion">Strong Health Potion</a>&#160;• <a href="/wiki/Strong_Heroic_Potion" title="Strong Heroic Potion">Strong Heroic Potion</a>&#160;• <a href="/wiki/Strong_Mana_Potion" title="Strong Mana Potion">Strong Mana Potion</a>&#160;• <a href="/wiki/Strong_Pestilence_Flask" title="Strong Pestilence Flask">Strong Pestilence Flask</a>&#160;• <a href="/wiki/Strong_Stone_Skin_Potion" title="Strong Stone Skin Potion">Strong Stone Skin Potion</a>&#160;• <a href="/wiki/Strong_Vampiric_Potion" title="Strong Vampiric Potion">Strong Vampiric Potion</a>&#160;• <a href="/wiki/Vampiric_Potion" title="Vampiric Potion">Vampiric Potion</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Shield" title="Shield">Shield</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Frozen_Buckler" title="Frozen Buckler">Frozen Buckler</a>&#160;• <a href="/wiki/Moon_Shield" title="Moon Shield">Moon Shield</a>&#160;• <a href="/wiki/Shield_of_Valor" title="Shield of Valor">Shield of Valor</a>&#160;• <a href="/wiki/Spiked_Shield" title="Spiked Shield">Spiked Shield</a>&#160;• <a href="/wiki/Sun_Shield" title="Sun Shield">Sun Shield</a>&#160;• <a href="/wiki/Wooden_Buckler" title="Wooden Buckler">Wooden Buckler</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Shoes" title="Shoes">Shoes</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Dragonskin_Boots" title="Dragonskin Boots">Dragonskin Boots</a>&#160;• <a href="/wiki/Leather_Boots" title="Leather Boots">Leather Boots</a>&#160;• <a href="/wiki/Stone_Shoes" title="Stone Shoes">Stone Shoes</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Weapon" title="Weapon">Weapon</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Amethyst_Whelp" title="Amethyst Whelp">Amethyst Whelp</a>&#160;• <a href="/wiki/Armored_Courage_Puppy" title="Armored Courage Puppy">Armored Courage Puppy</a>&#160;• <a href="/wiki/Artifact_Stone:_Cold" title="Artifact Stone: Cold">Artifact Stone: Cold</a>&#160;• <a href="/wiki/Artifact_Stone:_Death" title="Artifact Stone: Death">Artifact Stone: Death</a>&#160;• <a href="/wiki/Artifact_Stone:_Heat" title="Artifact Stone: Heat">Artifact Stone: Heat</a>&#160;• <a href="/wiki/Axe" title="Axe">Axe</a>&#160;• <a href="/wiki/Belladonna%27s_Shade" title="Belladonna&#39;s Shade">Belladonna's Shade</a>&#160;• <a href="/wiki/Belladonna%27s_Whisper" title="Belladonna&#39;s Whisper">Belladonna's Whisper</a>&#160;• <a href="/wiki/Blood_Harvester" title="Blood Harvester">Blood Harvester</a>&#160;• <a href="/wiki/Bloodthorne" title="Bloodthorne">Bloodthorne</a>&#160;• <a href="/wiki/Bloody_Dagger" title="Bloody Dagger">Bloody Dagger</a>&#160;• <a href="/wiki/Bow_and_Arrow" title="Bow and Arrow">Bow and Arrow</a>&#160;• <a href="/wiki/Brass_Knuckles" title="Brass Knuckles">Brass Knuckles</a>&#160;• <a href="/wiki/Broom" title="Broom">Broom</a>&#160;• <a href="/wiki/Burning_Blade" title="Burning Blade">Burning Blade</a>&#160;• <a href="/wiki/Burning_Sword" title="Burning Sword">Burning Sword</a>&#160;• <a href="/wiki/Burning_Torch" title="Burning Torch">Burning Torch</a>&#160;• <a href="/wiki/Busted_Blade" title="Busted Blade">Busted Blade</a>&#160;• <a href="/wiki/Chain_Whip" title="Chain Whip">Chain Whip</a>&#160;• <a href="/wiki/Claws_of_Attack" title="Claws of Attack">Claws of Attack</a>&#160;• <a href="/wiki/Courage_Puppy" title="Courage Puppy">Courage Puppy</a>&#160;• <a href="/wiki/Critwood_Staff" title="Critwood Staff">Critwood Staff</a>&#160;• <a href="/wiki/Crossblades" title="Crossblades">Crossblades</a>&#160;• <a href="/wiki/Cursed_Dagger" title="Cursed Dagger">Cursed Dagger</a>&#160;• <a href="/wiki/Dagger" title="Dagger">Dagger</a>&#160;• <a href="/wiki/Dancing_Dragon" title="Dancing Dragon">Dancing Dragon</a>&#160;• <a href="/wiki/Darksaber" title="Darksaber">Darksaber</a>&#160;• <a href="/wiki/Death_Scythe" title="Death Scythe">Death Scythe</a>&#160;• <a href="/wiki/Double_Axe" title="Double Axe">Double Axe</a>&#160;• <a href="/wiki/Eggscalibur" title="Eggscalibur">Eggscalibur</a>&#160;• <a href="/wiki/Emerald_Whelp" title="Emerald Whelp">Emerald Whelp</a>&#160;• <a href="/wiki/Falcon_Blade" title="Falcon Blade">Falcon Blade</a>&#160;• <a href="/wiki/Fancy_Fencing_Rapier" title="Fancy Fencing Rapier">Fancy Fencing Rapier</a>&#160;• <a href="/wiki/Flame_Whip" title="Flame Whip">Flame Whip</a>&#160;• <a href="/wiki/Forging_Hammer" title="Forging Hammer">Forging Hammer</a>&#160;• <a href="/wiki/Fortuna%27s_Grace" title="Fortuna&#39;s Grace">Fortuna's Grace</a>&#160;• <a href="/wiki/Fortuna%27s_Hope" title="Fortuna&#39;s Hope">Fortuna's Hope</a>&#160;• <a href="/wiki/Frostbite" title="Frostbite">Frostbite</a>&#160;• <a href="/wiki/Hammer" title="Hammer">Hammer</a>&#160;• <a href="/wiki/Hero_Longsword" title="Hero Longsword">Hero Longsword</a>&#160;• <a href="/wiki/Hero_Sword" title="Hero Sword">Hero Sword</a>&#160;• <a href="/wiki/Holy_Spear" title="Holy Spear">Holy Spear</a>&#160;• <a href="/wiki/Hungry_Blade" title="Hungry Blade">Hungry Blade</a>&#160;• <a href="/wiki/Ice_Dragon" title="Ice Dragon">Ice Dragon</a>&#160;• <a href="/wiki/Impractically_Large_Greatsword" title="Impractically Large Greatsword">Impractically Large Greatsword</a>&#160;• <a href="/wiki/Katana" title="Katana">Katana</a>&#160;• <a href="/wiki/Lightsaber" title="Lightsaber">Lightsaber</a>&#160;• <a href="/wiki/Magic_Staff" title="Magic Staff">Magic Staff</a>&#160;• <a href="/wiki/Magic_Torch" title="Magic Torch">Magic Torch</a>&#160;• <a href="/wiki/Manathirst" title="Manathirst">Manathirst</a>&#160;• <a href="/wiki/Molten_Dagger" title="Molten Dagger">Molten Dagger</a>&#160;• <a href="/wiki/Molten_Spear" title="Molten Spear">Molten Spear</a>&#160;• <a href="/wiki/Obsidian_Dragon" title="Obsidian Dragon">Obsidian Dragon</a>&#160;• <a href="/wiki/Pan" title="Pan">Pan</a>&#160;• <a href="/wiki/Pandamonium" title="Pandamonium">Pandamonium</a>&#160;• <a href="/wiki/Phoenix" title="Phoenix">Phoenix</a>&#160;• <a href="/wiki/Poison_Dagger" title="Poison Dagger">Poison Dagger</a>&#160;• <a href="/wiki/Pop" title="Pop">Pop</a>&#160;• <a href="/wiki/Prismatic_Sword" title="Prismatic Sword">Prismatic Sword</a>&#160;• <a href="/wiki/Pumpkin" title="Pumpkin">Pumpkin</a>&#160;• <a href="/wiki/Ripsaw_Blade" title="Ripsaw Blade">Ripsaw Blade</a>&#160;• <a href="/wiki/Ruby_Chonk" title="Ruby Chonk">Ruby Chonk</a>&#160;• <a href="/wiki/Ruby_Whelp" title="Ruby Whelp">Ruby Whelp</a>&#160;• <a href="/wiki/Sapphire_Whelp" title="Sapphire Whelp">Sapphire Whelp</a>&#160;• <a href="/wiki/Serpent_Staff" title="Serpent Staff">Serpent Staff</a>&#160;• <a href="/wiki/Shell_Totem" title="Shell Totem">Shell Totem</a>&#160;• <a href="/wiki/Shortbow" title="Shortbow">Shortbow</a>&#160;• <a href="/wiki/Shovel" title="Shovel">Shovel</a>&#160;• <a href="/wiki/Snow_Stick" title="Snow Stick">Snow Stick</a>&#160;• <a href="/wiki/Spear" title="Spear">Spear</a>&#160;• <a href="/wiki/Spectral_Dagger" title="Spectral Dagger">Spectral Dagger</a>&#160;• <a href="/wiki/Spiked_Staff" title="Spiked Staff">Spiked Staff</a>&#160;• <a href="/wiki/Squirrel_Archer" title="Squirrel Archer">Squirrel Archer</a>&#160;• <a href="/wiki/Staff_of_Fire" title="Staff of Fire">Staff of Fire</a>&#160;• <a href="/wiki/Staff_of_Unhealing" title="Staff of Unhealing">Staff of Unhealing</a>&#160;• <a href="/wiki/Stone" title="Stone">Stone</a>&#160;• <a href="/wiki/Stone_Golem" title="Stone Golem">Stone Golem</a>&#160;• <a href="/wiki/Thorn_Whip" title="Thorn Whip">Thorn Whip</a>&#160;• <a href="/wiki/Thornbloom" title="Thornbloom">Thornbloom</a>&#160;• <a href="/wiki/Torch" title="Torch">Torch</a>&#160;• <a href="/wiki/Tusk_Piercer" title="Tusk Piercer">Tusk Piercer</a>&#160;• <a href="/wiki/Tusk_Poker" title="Tusk Poker">Tusk Poker</a>&#160;• <a href="/wiki/Villain_Sword" title="Villain Sword">Villain Sword</a>&#160;• <a href="/wiki/Wooden_Sword" title="Wooden Sword">Wooden Sword</a></div></td></tr></tbody></table>
<!-- 
NewPP limit report
Cached time: 20250129100935
Cache expiry: 2592000
Reduced expiry: false
Complications: [show‐toc]
CPU time usage: 0.207 seconds
Real time usage: 0.682 seconds
Preprocessor visited node count: 558/1000000
Post‐expand include size: 10397/4194304 bytes
Template argument size: 33/4194304 bytes
Highest expansion depth: 6/100
Expensive parser function count: 0/500
Unstrip recursion depth: 0/20
Unstrip post‐expand size: 8112/10000000 bytes
Lua time usage: 0.008/15.000 seconds
Lua memory usage: 640278/52428800 bytes
-->
<!--
Transclusion expansion time report (%,ms,calls,template)
100.00%  131.328      1 Template:Items
100.00%  131.328      1 -total
 97.80%  128.439      1 Template:Navbox
-->

<!-- Saved in parser cache with key backpackbattles_en:pcache:idhash:315-0!canonical and timestamp 20250129100934 and revision id 10081. Rendering was triggered because: page-view
 -->
</div>
<div class="printfooter" data-nosnippet="">Retrieved from "<a dir="ltr" href="https://backpackbattles.wiki.gg/wiki/Items?oldid=10081">https://backpackbattles.wiki.gg/wiki/Items?oldid=10081</a>"</div></div>
					<div id="catlinks" class="catlinks" data-mw="interface"><div id="mw-normal-catlinks" class="mw-normal-catlinks"><a href="/wiki/Special:Categories" title="Special:Categories">Category</a>: <ul><li><a href="/wiki/Category:Items" title="Category:Items">Items</a></li></ul></div></div>
				</div>
			</main>
			<aside id="wikigg-sl-rail" data-mw="WggShowcaseLayout">
        <div
            class="wikigg-showcase__unit wikigg-showcase__reserved--mrec"
            data-wgg-unit-type="pubco"
        ><div id="nn_mpu_test"></div><div id="nn_mpu1"></div></div>
        <div
            class="wikigg-showcase__unit "
            data-wgg-unit-type="internal"
        ></div>
        <div
            class="wikigg-showcase__unit "
            data-wgg-unit-type="pubco"
        ><div id="nn_mpu2"></div></div>
</aside>

		</div>
		<aside id="wikigg-sl-footer" data-mw="WggShowcaseLayout">
        <div
            class="wikigg-showcase__unit wikigg-showcase__reserved--lb"
            data-wgg-unit-type="internal"
        ></div>
</aside>

	</div>
</div>
<div id='mw-data-after-content'>
	<div class="mw-cookiewarning-container"><div class="mw-cookiewarning-text"><span>Cookies help us deliver our services. By using our services, you agree to our use of cookies.</span></div><form method="POST"><div class='oo-ui-layout oo-ui-horizontalLayout'><span class='oo-ui-widget oo-ui-widget-enabled oo-ui-buttonElement oo-ui-buttonElement-framed oo-ui-labelElement oo-ui-flaggedElement-progressive oo-ui-buttonWidget'><a role='button' tabindex='0' href='https://www.indie.io/privacy-policy' rel='nofollow' class='oo-ui-buttonElement-button'><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon oo-ui-image-progressive'></span><span class='oo-ui-labelElement-label'>More information</span><span class='oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator oo-ui-image-progressive'></span></a></span><span class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-buttonElement oo-ui-buttonElement-framed oo-ui-labelElement oo-ui-flaggedElement-primary oo-ui-flaggedElement-progressive oo-ui-buttonInputWidget'><button type='submit' tabindex='0' name='disablecookiewarning' value='OK' class='oo-ui-inputWidget-input oo-ui-buttonElement-button'><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon oo-ui-image-invert'></span><span class='oo-ui-labelElement-label'>OK</span><span class='oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator oo-ui-image-invert'></span></button></span></div></form></div>
</div>


<footer id="footer" class="mw-footer" >
	<ul id="footer-info">
	<li id="footer-info-lastmod"> This page was last edited on 8 November 2024, at 05:27.</li>
	<li id="footer-info-copyright"><div class="wikigg-copyright-box">Pages that were created prior to November 2023 are adapted from the Backpack Battles Fandom wiki.<br />Page content is under <a class="external" rel="nofollow" href="https://creativecommons.org/licenses/by-sa/4.0">Creative Commons Attribution-ShareAlike 4.0 License</a> unless otherwise noted.</div></li>
</ul>

	<ul id="footer-places">
	<li id="footer-places-wikigg-tos"><a href="https://www.indie.io/terms-of-service">Terms of Service</a></li>
	<li id="footer-places-wikigg-privacy"><a href="https://www.indie.io/privacy-policy">Privacy policy</a></li>
	<li id="footer-places-wikigg-support"><a href="https://support.wiki.gg">Support Wiki</a></li>
	<li id="footer-places-wikigg-servicedesk"><a href="https://wiki.gg/go/servicedesk">Send a ticket to wiki.gg</a></li>
	<li id="footer-places-wikigg-statuspage"><a href="https://wikiggstatus.com">Status page</a></li>
	<li id="footer-places-wikigg-pcmp"><a href="#" class="nn-cmp-show" style="display: none">Manage cookie settings</a></li>
</ul>

	<ul id="footer-icons" class="noprint">
	<li id="footer-copyrightico"><a href="https://creativecommons.org/licenses/by-sa/4.0" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><img src="https://commons.wiki.gg/images/1/14/CC-BY-SA_footer_badge.svg?2" alt="Creative Commons Attribution-ShareAlike 4.0 License" height="31" width="88" loading="lazy"></a></li>
	<li id="footer-poweredbyico"><a href="https://www.mediawiki.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><img src="/mw-1.43/resources/assets/poweredby_mediawiki.svg" alt="Powered by MediaWiki" width="88" height="31" loading="lazy"></a></li>
	<li id="footer-partofico"><a href="https://wiki.gg" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><img src="https://commons.wiki.gg/images/d/d1/Network_footer_badge.svg?2" alt="Part of wiki.gg" height="31" width="88" loading="lazy"></a></li>
</ul>

</footer>


<script>(RLQ=window.RLQ||[]).push(function(){mw.config.set({"wgBackendResponseTime":194,"wgPageParseReport":{"limitreport":{"cputime":"0.207","walltime":"0.682","ppvisitednodes":{"value":558,"limit":1000000},"postexpandincludesize":{"value":10397,"limit":4194304},"templateargumentsize":{"value":33,"limit":4194304},"expansiondepth":{"value":6,"limit":100},"expensivefunctioncount":{"value":0,"limit":500},"unstrip-depth":{"value":0,"limit":20},"unstrip-size":{"value":8112,"limit":10000000},"timingprofile":["100.00%  131.328      1 Template:Items","100.00%  131.328      1 -total"," 97.80%  128.439      1 Template:Navbox"]},"scribunto":{"limitreport-timeusage":{"value":"0.008","limit":"15.000"},"limitreport-memusage":{"value":640278,"limit":52428800}},"cachereport":{"timestamp":"20250129100935","ttl":2592000,"transientcontent":false}}});});</script>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'950255716dabdada',t:'MTc0OTk5MzMwMC4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script><script defer src="https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon='{"rayId":"950255716dabdada","serverTiming":{"name":{"cfExtPri":true,"cfEdge":true,"cfOrigin":true,"cfL4":true,"cfSpeedBrain":true,"cfCacheStatus":true}},"version":"2025.6.2","token":"26b2c6e7c852417d8f9d11b0c7f02309"}' crossorigin="anonymous"></script>
</body>
</html>