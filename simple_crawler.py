import requests
from bs4 import BeautifulSoup
import json
import time
import logging
import re
from urllib.parse import urljoin
from typing import Dict, List, Optional, Any

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def extract_cost(text: str) -> str:
    """Extract cost from text."""
    if not text:
        return "0"
    # Look for number followed by Gold or gold icon
    cost_match = re.search(r'(\d+)', text)
    return cost_match.group(1) if cost_match else "0"

def normalize_text(text: str) -> str:
    """Normalize text by removing extra whitespaces and special characters."""
    if not text:
        return ""
    # Remove wiki markup and extra whitespace
    text = re.sub(r'\[\[.*?\]\]', '', text)  # Remove wiki links
    text = re.sub(r'\{\{.*?\}\}', '', text)  # Remove templates
    text = re.sub(r'<.*?>', '', text)        # Remove HTML tags
    text = re.sub(r'\s+', ' ', text)         # Normalize whitespace
    return text.strip()

def parse_items_from_page(html: str, base_url: str) -> List[Dict[str, Any]]:
    """Parse items from a wiki page."""
    soup = BeautifulSoup(html, "html.parser")
    items = []
    
    logger.info(f"Parsing items from {base_url}")
    
    # Find all table rows
    all_rows = soup.find_all("tr")
    logger.info(f"Found {len(all_rows)} table rows")
    
    # Look for rows that contain item data
    processed_rows = 0
    for i, row in enumerate(all_rows):
        cols = row.find_all(["td", "th"])

        # Skip rows that don't have enough columns
        if len(cols) < 5:
            continue

        # Skip navigation rows but be more specific about headers
        row_text = row.get_text().strip().lower()
        if any(skip_text in row_text for skip_text in ['category:', 'retrieved from', 'cookies help', 'terms of service']):
            continue

        # Skip actual header row (contains all header words together)
        if ('name' in row_text and 'effect' in row_text and 'item type' in row_text and
            'rarity' in row_text and 'cost' in row_text):
            logger.info("Found header row, skipping...")
            continue

        processed_rows += 1
        if processed_rows <= 5:  # Debug first few rows
            logger.info(f"Processing row {i}: {len(cols)} columns")
            logger.info(f"First column HTML: {str(cols[0])[:200]}...")
            logger.info(f"First column text: {cols[0].get_text()[:100]}...")

        try:
            # Check if this looks like an item row (has image and name link)
            first_col = cols[0]
            has_img = first_col.find("img") is not None
            has_link = first_col.find("a") is not None

            if processed_rows <= 5:  # Debug first few rows
                logger.info(f"Row {i}: has_img={has_img}, has_link={has_link}")

            if not (has_img and has_link):
                continue
            
            # Extract item data - the name is actually in the second column!
            name_cell = cols[1] if len(cols) > 1 else first_col
            name_link = name_cell.find("a")
            raw_name = name_link.get_text() if name_link else ""
            name = normalize_text(raw_name)

            if processed_rows <= 5:  # Debug first few rows
                logger.info(f"Row {i}: raw name = '{raw_name}', normalized name = '{name}'")

            # Get image URL
            img = first_col.find("img")
            image_url = urljoin(base_url, img['src']) if img and img.get('src') else ""

            if not name:
                if processed_rows <= 5:
                    logger.info(f"Row {i}: skipping because name is empty")
                continue

            # Extract other fields - correct column mapping:
            # Col 1: Image, Col 2: Name, Col 3: Effect, Col 4: Item Type, Col 5: Rarity, Col 6: Cost
            effect = normalize_text(cols[2].get_text()) if len(cols) > 2 else ""
            item_type = normalize_text(cols[3].get_text()) if len(cols) > 3 else ""
            rarity = normalize_text(cols[4].get_text()) if len(cols) > 4 else ""
            cost = extract_cost(cols[5].get_text()) if len(cols) > 5 else "0"

            if processed_rows <= 5:  # Debug first few rows
                logger.info(f"Row {i}: effect='{effect[:50]}...', type='{item_type}', rarity='{rarity}', cost='{cost}'")

            item = {
                "name": name,
                "type": item_type,
                "rarity": rarity,
                "cost": cost,
                "effect": effect,
                "image_url": image_url,
                "source_url": base_url
            }
            items.append(item)
            logger.info(f"Extracted item: {name}")

        except Exception as e:
            logger.warning(f"Error parsing item row {i}: {e}")
            if processed_rows <= 5:
                import traceback
                logger.warning(f"Full traceback: {traceback.format_exc()}")
            continue
    
    logger.info(f"Total items extracted from {base_url}: {len(items)}")
    return items

def fetch_page(url: str) -> Optional[str]:
    """Fetch a web page with error handling."""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        return response.text
    except Exception as e:
        logger.error(f"Error fetching {url}: {e}")
        return None

def parse_recipes_from_page(html: str, base_url: str) -> List[Dict[str, Any]]:
    """Parse recipes from the recipe page."""
    soup = BeautifulSoup(html, "html.parser")
    recipes = []

    logger.info(f"Parsing recipes from {base_url}")

    # Find all table rows
    all_rows = soup.find_all("tr")
    logger.info(f"Found {len(all_rows)} table rows")

    # Look for rows that contain recipe data
    for row in all_rows:
        cols = row.find_all(["td", "th"])

        # Skip rows that don't have enough columns
        if len(cols) < 3:
            continue

        # Skip header rows
        row_text = row.get_text().strip().lower()
        if any(skip_text in row_text for skip_text in ['class', 'name', 'type', 'ingredients', 'catalyst']):
            if 'class' in row_text and 'name' in row_text:
                logger.info("Found recipe header row, skipping...")
            continue

        try:
            # Check if this looks like a recipe row
            if len(cols) >= 5:  # Full recipe table format
                class_restriction = normalize_text(cols[0].get_text())
                result_name = normalize_text(cols[1].get_text())
                # Skip type column (cols[2])
                ingredients_text = cols[3].get_text()
                catalyst = normalize_text(cols[4].get_text())
            elif len(cols) >= 3:  # Simplified recipe table
                result_name = normalize_text(cols[0].get_text())
                ingredients_text = cols[1].get_text()
                catalyst = normalize_text(cols[2].get_text()) if len(cols) > 2 else ""
                class_restriction = ""
            else:
                continue

            if not result_name:
                continue

            # Parse ingredients
            ingredients = []
            if ingredients_text:
                # Split by common separators and clean up
                ingredient_parts = re.split(r'[+,&]|\s+\+\s+', ingredients_text)
                ingredients = [normalize_text(ing) for ing in ingredient_parts if ing.strip()]

            recipe = {
                "result": result_name,
                "ingredients": ingredients,
                "catalyst": catalyst,
                "class_restriction": class_restriction,
                "source_url": base_url
            }
            recipes.append(recipe)

        except Exception as e:
            logger.warning(f"Error parsing recipe row: {e}")
            continue

    logger.info(f"Total recipes extracted from {base_url}: {len(recipes)}")
    return recipes

def main():
    """Main crawler function."""
    item_urls = [
        "https://backpackbattles.wiki.gg/wiki/Neutral_items",
        "https://backpackbattles.wiki.gg/wiki/Ranger_items",
        "https://backpackbattles.wiki.gg/wiki/Reaper_items",
        "https://backpackbattles.wiki.gg/wiki/Berserker_items",
        "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"
    ]

    recipe_urls = [
        "https://backpackbattles.wiki.gg/wiki/Recipe"
    ]

    all_items = []
    all_recipes = []

    # Crawl items
    for url in item_urls:
        logger.info(f"Processing items: {url}")
        html = fetch_page(url)

        if html:
            items = parse_items_from_page(html, url)
            all_items.extend(items)
            time.sleep(2)  # Be respectful to the server
        else:
            logger.error(f"Failed to fetch {url}")

    # Crawl recipes
    for url in recipe_urls:
        logger.info(f"Processing recipes: {url}")
        html = fetch_page(url)

        if html:
            recipes = parse_recipes_from_page(html, url)
            all_recipes.extend(recipes)
            time.sleep(2)  # Be respectful to the server
        else:
            logger.error(f"Failed to fetch {url}")

    # Save results
    output = {
        "items": all_items,
        "recipes": all_recipes,
        "metadata": {
            "total_items": len(all_items),
            "total_recipes": len(all_recipes),
            "crawl_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "item_sources": item_urls,
            "recipe_sources": recipe_urls
        }
    }

    with open("comprehensive_backpack_battles_data.json", "w", encoding="utf-8") as f:
        json.dump(output, f, indent=2, ensure_ascii=False)

    logger.info(f"Crawling completed. Found {len(all_items)} items and {len(all_recipes)} recipes total.")
    logger.info("Results saved to comprehensive_backpack_battles_data.json")

if __name__ == "__main__":
    main()
