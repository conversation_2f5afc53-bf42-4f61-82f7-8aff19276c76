{"os": "Windows-11-10.0.26100-SP0", "python": "CPython 3.12.3", "startedAt": "2025-06-15T04:45:34.085389Z", "program": "C:\\Users\\<USER>\\Desktop\\Games\\BBATTLES\\rl_agent.py", "codePath": "rl_agent.py", "root": "C:\\Users\\<USER>\\Desktop\\Games\\BBATTLES", "host": "DESKTOP-<PERSON>", "executable": "C:\\Users\\<USER>\\anaconda3\\python.exe", "codePathLocal": "rl_agent.py", "cpu_count": 12, "cpu_count_logical": 24, "gpu": "NVIDIA GeForce RTX 3080", "gpu_count": 1, "disk": {"/": {"total": "2047253745664", "used": "1727736573952"}}, "memory": {"total": "34265108480"}, "cpu": {"count": 12, "countLogical": 24}, "gpu_nvidia": [{"name": "NVIDIA GeForce RTX 3080", "memoryTotal": "10737418240", "cudaCores": 8704, "architecture": "Ampere", "uuid": "GPU-edfa4d0f-c46c-8871-7f83-507eb9c95e6e"}], "cudaVersion": "12.9"}