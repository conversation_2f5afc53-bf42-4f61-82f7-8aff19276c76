<!DOCTYPE html>
<html class="client-nojs view-unknown theme-none skin-theme-clientpref-day" lang="en" dir="ltr">
<head>
<meta charset="UTF-8">
<title>Recipe - The Backpack Battles Wiki</title>
<script>(function(){var className="client-js view-unknown theme-none skin-theme-clientpref-day";var cookie=document.cookie.match(/(?:^|; )commons_enmwclientpreferences=([^;]+)/);if(cookie){cookie[1].split('%2C').forEach(function(pref){className=className.replace(new RegExp('(^| )'+pref.replace(/-clientpref-\w+$|[^\w-]+/g,'')+'-clientpref-\\w+( |$)'),'$1'+pref+'$2');});}document.documentElement.className=className;}());RLCONF={"wgBreakFrames":false,"wgSeparatorTransformTable":["",""],"wgDigitTransformTable":["",""],"wgDefaultDateFormat":"dmy","wgMonthNames":["","January","February","March","April","May","June","July","August","September","October","November","December"],"wgRequestId":"7f591cfe6022d7f063560e63","wgCanonicalNamespace":"","wgCanonicalSpecialPageName":false,"wgNamespaceNumber":0,"wgPageName":"Recipe","wgTitle":"Recipe","wgCurRevisionId":10501,"wgRevisionId":10501,"wgArticleId":382,"wgIsArticle":true,"wgIsRedirect":false,"wgAction":"view","wgUserName":null,"wgUserGroups":["*"],
"wgCategories":["Items"],"wgPageViewLanguage":"en","wgPageContentLanguage":"en","wgPageContentModel":"wikitext","wgRelevantPageName":"Recipe","wgRelevantArticleId":382,"wgIsProbablyEditable":false,"wgRelevantPageIsProbablyEditable":false,"wgRestrictionEdit":[],"wgRestrictionMove":[],"wgPageFormsTargetName":null,"wgPageFormsAutocompleteValues":[],"wgPageFormsAutocompleteOnAllChars":false,"wgPageFormsFieldProperties":[],"wgPageFormsCargoFields":[],"wgPageFormsDependentFields":[],"wgPageFormsCalendarValues":[],"wgPageFormsCalendarParams":[],"wgPageFormsCalendarHTML":null,"wgPageFormsGridValues":[],"wgPageFormsGridParams":[],"wgPageFormsContLangYes":null,"wgPageFormsContLangNo":null,"wgPageFormsContLangMonths":[],"wgPageFormsHeightForMinimizingInstances":800,"wgPageFormsDelayReload":false,"wgPageFormsShowOnSelect":[],"wgPageFormsScriptPath":"/mw-1.43/extensions/PageForms","edgValues":[],"wgPageFormsEDSettings":null,"wgAmericanDates":false,"wgCargoDefaultQueryLimit":2000,
"wgCargoMapClusteringMinimum":80,"wgCargoMonthNamesShort":["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],"egAppHost":"https://app.wiki.gg","wgMediaViewerOnClick":true,"wgMediaViewerEnabledByDefault":true,"wgCiteReferencePreviewsActive":true,"wgCheckUserClientHintsHeadersJsApi":["architecture","bitness","brands","fullVersionList","mobile","model","platform","platformVersion"]};RLSTATE={"ext.globalCssJs.user.styles":"ready","site.styles":"ready","user.styles":"ready","ext.globalCssJs.user":"ready","user":"ready","user.options":"loading","mediawiki.page.gallery.styles":"ready","jquery.tablesorter.styles":"ready","skins.vector.styles.legacy":"ready","jquery.makeCollapsible.styles":"ready","ext.CookieWarning.styles":"ready","oojs-ui-core.styles":"ready","oojs-ui.styles.indicators":"ready","mediawiki.widgets.styles":"ready","oojs-ui-core.icons":"ready","ext.removeredlinks.styles":"ready","ext.usergroupbadges.styles":"ready","ext.embedVideo.styles":"ready",
"ext.globalui.styles":"ready"};RLPAGEMODULES=["mediawiki.page.gallery","ext.cargo.main","jquery.tablesorter","site","mediawiki.page.ready","jquery.makeCollapsible","skins.vector.legacy.js","ext.CookieWarning","ext.removeredlinks","ext.themes.switcher","ext.gadget.interwikiDropdownButton","mmv.bootstrap","ext.checkUser.clientHints","ext.embedVideo.overlay","ext.cargo.purge","ext.globalui"];</script>
<script>(RLQ=window.RLQ||[]).push(function(){mw.loader.impl(function(){return["user.options@12s5i",function($,jQuery,require,module){mw.user.tokens.set({"patrolToken":"+\\","watchToken":"+\\","csrfToken":"+\\"});
}];});});</script>
<link rel="stylesheet" href="/load.php?lang=en&amp;modules=ext.CookieWarning.styles%7Cext.embedVideo.styles%7Cext.globalui.styles%7Cext.removeredlinks.styles%7Cext.usergroupbadges.styles%7Cjquery.makeCollapsible.styles%7Cjquery.tablesorter.styles%7Cmediawiki.page.gallery.styles%7Cmediawiki.widgets.styles%7Coojs-ui-core.icons%2Cstyles%7Coojs-ui.styles.indicators%7Cskins.vector.styles.legacy&amp;only=styles&amp;skin=vector">
<script async="" src="/load.php?lang=en&amp;modules=startup&amp;only=scripts&amp;raw=1&amp;skin=vector"></script>
<meta name="ResourceLoaderDynamicStyles" content="">
<link rel="stylesheet" href="/load.php?lang=en&amp;modules=site.styles&amp;only=styles&amp;skin=vector">
<script async src="/load.php?lang=en&skin=vector&modules=ext.themes.apply&only=scripts&skin=vector&raw=1"></script>
<meta name="generator" content="MediaWiki 1.43.0">
<meta name="robots" content="max-image-preview:standard">
<meta name="format-detection" content="telephone=no">
<meta name="description" content="Recipes are items that combine into other items. Recipe items placed next to each other in the backpack will be combined in the next shop phase. Items can be locked from combining by right clicking on them. In some recipes a catalyst item is used, it remains unchanged while the other ingredients are...">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, minimum-scale=0.25, maximum-scale=5.0">
<style type="text/css">@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/vietnamese/wght/normal.woff2);unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/cyrillic/wght/normal.woff2);unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/latin-ext/wght/normal.woff2);unicode-range:U+0100-02AF,U+0304,U+0308,U+0329,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/cyrillic-ext/wght/normal.woff2);unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/latin/wght/normal.woff2);unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;font-display:swap;}</style>
<link rel="apple-touch-icon" href="/images/4/4a/Site-favicon.ico">
<link rel="icon" href="/images/4/4a/Site-favicon.ico">
<link rel="search" type="application/opensearchdescription+xml" href="/rest.php/v1/search" title="Backpack Battles Wiki (en)">
<link rel="EditURI" type="application/rsd+xml" href="https://backpackbattles.wiki.gg/api.php?action=rsd">
<link rel="canonical" href="https://backpackbattles.wiki.gg/wiki/Recipe">
<link rel="license" href="https://creativecommons.org/licenses/by-sa/4.0">
<link rel="alternate" type="application/atom+xml" title="Backpack Battles Wiki Atom feed" href="/wiki/Special:RecentChanges?feed=atom">
	<meta property="fb:app_id" content="1025655121420775" prefix="fb: http://www.facebook.com/2008/fbml">

	<meta property="og:type" content="article">

	<meta property="og:site_name" content="Backpack Battles Wiki">

	<meta property="og:title" content="Recipe">

	<meta property="og:description" content="Recipes are items that combine into other items. Recipe items placed next to each other in the backpack will be combined in the next shop phase. Items can be locked from combining by right clicking on them. In some recipes a catalyst item is used, it remains unchanged while the other ingredients are...">

	<meta property="og:url" content="https://backpackbattles.wiki.gg/wiki/Recipe">

<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-P68LVFKN');</script>
<!-- End Google Tag Manager -->
</head>
<body class="wgg-dom-version-1_43 skin-vector-legacy mediawiki ltr sitedir-ltr mw-hide-empty-elt ns-0 ns-subject page-Recipe rootpage-Recipe skin-vector action-view skin--responsive"><header id="wgg-netbar">
	<div class="wgg-netbar__left">
		<div class="wgg-netbar__logo">
			<a href="/" aria-label="wiki.gg">
				<img height=25 alt="wiki.gg logo" src="https://commons.wiki.gg/images/2/27/Network_header_logo.svg">
			</a>
		</div>
	</div>

	<nav class="wgg-netbar__user-links" aria-label="Personal tools">
		<div id="p-personal" class="mw-portlet mw-portlet-personal"  >
		    
		    <ul>
		            <li id="pt-createaccount" class="mw-list-item wgg-netbar__desktop-only"><a data-mw="interface" class=""  href="/wiki/Special:CreateAccount?returnto=Recipe" title="You are encouraged to create an account and log in; however, it is not mandatory"><span>Create account</span></a></li>
		            <li id="pt-login" class="mw-list-item wgg-netbar__desktop-only"><a data-mw="interface" class=""  href="/wiki/Special:UserLogin?returnto=Recipe" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o"><span>Log in</span></a></li>
		    </ul>
		    
		</div>

		<div id="wgg-user-menu-overflow" class="wgg-netbar__dropdown wgg-netbar__mobile-only">
			<input type="checkbox"
				id="wgg-user-menu-overflow-checkbox"
				role="button"
				aria-haspopup="true"
				aria-labelledby="wgg-user-menu-overflow-label"
			>
			<label
				id="wgg-user-menu-overflow-label"
		        for="wgg-user-menu-overflow-checkbox"
				class="wgg-netbar__icon-button"
			><span class="wgg-netbar-icon wgg-netbar-icon--overflow"></span><span>Toggle personal tools menu</span></label>

			<div class="wgg-netbar__dropdown-content">
				<div id="p-user-menu" class="mw-portlet mw-portlet-user-menu"  >
				    
				    <ul>
				            <li id="pt-createaccount-overflow" class="mw-list-item wgg-netbar__mobile-only"><a data-mw="interface" class=""  href="/wiki/Special:CreateAccount?returnto=Recipe" title="You are encouraged to create an account and log in; however, it is not mandatory"><span>Create account</span></a></li>
				            <li id="pt-login-overflow" class="mw-list-item wgg-netbar__mobile-only"><a data-mw="interface" class=""  href="/wiki/Special:UserLogin?returnto=Recipe" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o"><span>Log in</span></a></li>
				    </ul>
				    
				</div>
			</div>
		</div>
</nav>

</header>

<div id="mw-page-base" class="noprint"></div>
<div id="mw-head-base" class="noprint"></div>
<div class="content-wrapper">
	<div id="mw-navigation">
		<h2>Navigation menu</h2>
		<div id="mw-head">
			<div id="left-navigation">
				
<div id="p-namespaces" class="mw-portlet mw-portlet-namespaces vector-menu-tabs vector-menu-tabs-legacy vectorTabs vector-menu" aria-labelledby="p-namespaces-label"  >
	<h3
		id="p-namespaces-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">Namespaces</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="ca-nstab-main" class="selected mw-list-item"><a href="/wiki/Recipe" title="View the content page [c]" accesskey="c"><span>Page</span></a></li><li id="ca-talk" class="new mw-list-item"><a href="/wiki/Talk:Recipe?action=edit&amp;redlink=1" rel="discussion" class="new" title="Discussion about the content page (page does not exist) [t]" accesskey="t"><span>Discussion</span></a></li>
		</ul>
		
	</div>
</div>

				
<div id="p-variants" class="mw-portlet mw-portlet-variants emptyPortlet vector-menu-dropdown vector-menu" aria-labelledby="p-variants-label"  >
	<input type="checkbox"
		id="p-variants-checkbox"
		role="button"
		aria-haspopup="true"
		data-event-name="ui.dropdown-p-variants"
		class="vector-menu-checkbox"
		aria-labelledby="p-variants-label"
	>
	<label
		id="p-variants-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">English</span>
	</label>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			
		</ul>
		
	</div>
</div>

			</div>
			<div id="right-navigation">
				
<div id="p-views" class="mw-portlet mw-portlet-views vector-menu-tabs vector-menu-tabs-legacy vectorTabs vector-menu" aria-labelledby="p-views-label"  >
	<h3
		id="p-views-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">Views</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="ca-view" class="selected mw-list-item"><a href="/wiki/Recipe"><span>Read</span></a></li><li id="ca-edit" class="mw-list-item"><a href="/wiki/Special:CreateAccount?warning=accountrequiredtoedit&amp;returnto=Recipe&amp;returntoquery=action%3Dedit" title="Edit this page [e]" accesskey="e"><span>Sign up to edit</span></a></li><li id="ca-viewsource" class="mw-list-item"><a href="/wiki/Recipe?action=edit" title="This page is protected.&#10;You can view its source [e]" accesskey="e"><span>View source</span></a></li><li id="ca-history" class="mw-list-item"><a href="/wiki/Recipe?action=history" title="Past revisions of this page [h]" accesskey="h"><span>History</span></a></li>
		</ul>
		
	</div>
</div>

				
<div id="p-cactions" class="mw-portlet mw-portlet-cactions vectorMenu vector-menu-dropdown vector-menu" aria-labelledby="p-cactions-label"  title="More options" >
	<input type="checkbox"
		id="p-cactions-checkbox"
		role="button"
		aria-haspopup="true"
		data-event-name="ui.dropdown-p-cactions"
		class="vector-menu-checkbox"
		aria-labelledby="p-cactions-label"
	>
	<label
		id="p-cactions-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">More</span>
	</label>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="ca-cargo-purge" class="mw-list-item"><a href="/wiki/Recipe?action=purge"><span>Purge cache</span></a></li>
		</ul>
		
	</div>
</div>

				
<div id="p-search" role="search" class="vector-search-box-vue  vector-search-box-show-thumbnail vector-search-box-auto-expand-width vector-search-box">
	<h3 >Search</h3>
	<form action="/wiki/Special:Search" id="searchform" class="vector-search-box-form">
		<div id="simpleSearch"
			class="vector-search-box-inner"
			 data-search-loc="header-navigation">
			<input class="vector-search-box-input"
				 type="search" name="search" placeholder="Search Backpack Battles Wiki" aria-label="Search Backpack Battles Wiki" autocapitalize="sentences" title="Search Backpack Battles Wiki [f]" accesskey="f" id="searchInput"
			>
			<input id="mw-searchButton"
				 class="searchButton mw-fallbackSearchButton" type="submit" name="fulltext" title="Search the pages for this text" value="Search">
			<input id="searchButton"
				 class="searchButton" type="submit" name="go" title="Go to a page with this exact name if it exists" value="Go">
		</div>
	</form>
</div>

			</div>
		</div>
		
<div id="mw-panel">
	<div id="p-logo" role="banner">
		<a class="mw-wiki-logo" href="/"
			title="Visit the main page"></a>
	</div>
	
<div id="p-Content" class="mw-portlet mw-portlet-Content vector-menu-portal portal vector-menu" aria-labelledby="p-Content-label"  >
	<h3
		id="p-Content-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">Content</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="n-mainpage-description" class="mw-list-item"><a href="/" title="Visit the main page [z]" accesskey="z"><span>Main page</span></a></li><li id="n-Backpack-Battles" class="mw-list-item"><a href="/wiki/Backpack_Battles"><span>Backpack Battles</span></a></li><li id="n-Items" class="mw-list-item"><a href="/wiki/Items"><span>Items</span></a></li><li id="n-Game-Mechanics" class="mw-list-item"><a href="/wiki/Game_Mechanics"><span>Game Mechanics</span></a></li><li id="n-Characters" class="mw-list-item"><a href="/wiki/Characters"><span>Characters</span></a></li><li id="n-Version-History" class="mw-list-item"><a href="/wiki/Version_History"><span>Version History</span></a></li>
		</ul>
		
	</div>
</div>

	
<div id="p-navigation" class="mw-portlet mw-portlet-navigation vector-menu-portal portal vector-menu" aria-labelledby="p-navigation-label"  >
	<h3
		id="p-navigation-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">Navigation</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="n-portal" class="mw-list-item"><a href="/wiki/Backpack_Battles_Wiki:Community_portal" title="About the project, what you can do, where to find things"><span>Community portal</span></a></li><li id="n-recentchanges" class="mw-list-item"><a href="/wiki/Special:RecentChanges" title="A list of recent changes in the wiki [r]" accesskey="r"><span>Recent changes</span></a></li><li id="n-randompage" class="mw-list-item"><a href="/wiki/Special:Random" title="Load a random page [x]" accesskey="x"><span>Random page</span></a></li><li id="n-help-mediawiki" class="mw-list-item"><a href="https://www.mediawiki.org/wiki/Special:MyLanguage/Help:Contents"><span>Help about MediaWiki</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-tb" class="mw-portlet mw-portlet-tb vector-menu-portal portal vector-menu" aria-labelledby="p-tb-label"  >
	<h3
		id="p-tb-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">Tools</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="t-whatlinkshere" class="mw-list-item"><a href="/wiki/Special:WhatLinksHere/Recipe" title="A list of all wiki pages that link here [j]" accesskey="j"><span>What links here</span></a></li><li id="t-recentchangeslinked" class="mw-list-item"><a href="/wiki/Special:RecentChangesLinked/Recipe" rel="nofollow" title="Recent changes in pages linked from this page [k]" accesskey="k"><span>Related changes</span></a></li><li id="t-newpage" class="mw-list-item"><a href="/wiki/Special:NewPage" accesskey="]"><span>New page</span></a></li><li id="t-specialpages" class="mw-list-item"><a href="/wiki/Special:SpecialPages" title="A list of all special pages [q]" accesskey="q"><span>Special pages</span></a></li><li id="t-print" class="mw-list-item"><a href="javascript:print();" rel="alternate" title="Printable version of this page [p]" accesskey="p"><span>Printable version</span></a></li><li id="t-permalink" class="mw-list-item"><a href="/wiki/Recipe?oldid=10501" title="Permanent link to this revision of this page"><span>Permanent link</span></a></li><li id="t-info" class="mw-list-item"><a href="/wiki/Recipe?action=info" title="More information about this page"><span>Page information</span></a></li><li id="t-cargopagevalueslink" class="mw-list-item"><a href="/wiki/Recipe?action=pagevalues" rel="cargo-pagevalues"><span>Cargo data</span></a></li>
		</ul>
		
	</div>
</div>

	
</div>

	</div>
	<div id="content" class="mw-body" role="main">
		<aside id="wikigg-sl-header" data-mw="WggShowcaseLayout">
        <div
            class="wikigg-showcase__unit wikigg-showcase__reserved--lb"
            data-wgg-unit-type="internal"
        ></div>
</aside>

		<div class="content-body">
			<main>
				<a id="top"></a>
				<div id="siteNotice"></div>
				<div class="mw-indicators">
				</div>
				<h1 id="firstHeading" class="firstHeading mw-first-heading"><span class="mw-page-title-main">Recipe</span></h1>
				<div id="bodyContent" class="vector-body">
					<div id="siteSub" class="noprint">From Backpack Battles Wiki</div>
					<div id="contentSub"></div>
					<div id="contentSub2"></div>
					
					<div id="jump-to-nav"></div>
					<a class="mw-jump-link" href="#mw-head">Jump to navigation</a>
					<a class="mw-jump-link" href="#searchInput">Jump to search</a>
					<div id="mw-content-text" class="mw-body-content"><div class="mw-content-ltr mw-parser-output" lang="en" dir="ltr"><div class="thumb tright"><div class="thumbinner" style="width:302px;"><a href="/wiki/File:Recipe_Book.png" class="image"><img src="/images/thumb/5/5e/Recipe_Book.png/300px-Recipe_Book.png" decoding="async" loading="lazy" width="300" height="169" class="thumbimage" /></a>  <div class="thumbcaption"><div class="magnify"><a href="/wiki/File:Recipe_Book.png" class="internal" title="Enlarge"></a></div>Recipe Book</div></div></div>
<p><b>Recipes</b> are <a href="/wiki/Items" title="Items">items</a> that combine into other items. Recipe items placed next to each other in the backpack will be combined in the next shop phase. Items can be locked from combining by right clicking on them. In some recipes a catalyst item is used, it remains unchanged while the other ingredients are lost to generate a new item.
</p><p>Recipes can be viewed in the game by clicking on the "Recipes" button in the main menu or the shop. Each item will start off as a black silhouette before you have unlocked that item by completing its recipe.
</p>
<div style="clear:both"></div>
<h2><span class="mw-headline" id="List_of_Recipes">List of Recipes</span></h2>
<table class="navbox mw-collapsible mw-collapsed" data-expandtext="Show" data-collapsetext="Hide"><tbody><tr class="navbox-title"><th colspan="2" scope="col"><span class="navbox-vde"><a href="/wiki/Template:RecipesImages" title="Template:RecipesImages"><span title="View this navbox template">v</span></a> · <span class="plainlinks"><a class="external text" href="https://backpackbattles.wiki.gg/wiki/Template_talk:RecipesImages"><span title="Discuss this navbox template">d</span></a> · <a class="external text" href="https://backpackbattles.wiki.gg/wiki/Template:RecipesImages?action=edit"><span title="Edit this navbox template">e</span></a></span></span>Neutral recipe items</th></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><td class="navbox-list no-group" colspan="2"><div class="hlist"><div id="mw-category-media">
<ul class="gallery mw-gallery-packed">
		<li class="gallerybox" style="width: 98.666666666667px"><div style="width: 98.666666666667px">
			<div class="thumb" style="width: 96.666666666667px;"><div style="margin:0px auto;"><a href="/wiki/Amulet_of_Alchemy"><img alt="" src="/images/thumb/b/b1/AmuletofAlchemy.png/145px-AmuletofAlchemy.png" decoding="async" loading="lazy" width="97" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 100px"><div style="width: 100px">
			<div class="thumb" style="width: 98px;"><div style="margin:0px auto;"><a href="/wiki/Bunch_of_Coins"><img alt="" src="/images/a/ab/BunchofCoins.png" decoding="async" loading="lazy" width="98" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 104px"><div style="width: 104px">
			<div class="thumb" style="width: 102px;"><div style="margin:0px auto;"><a href="/wiki/Heart_of_Darkness"><img alt="" src="/images/thumb/f/fd/HeartofDarkness.png/153px-HeartofDarkness.png" decoding="async" loading="lazy" width="102" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 96px"><div style="width: 96px">
			<div class="thumb" style="width: 94px;"><div style="margin:0px auto;"><a href="/wiki/Amulet_of_Feasting"><img alt="" src="/images/thumb/6/6f/AmuletofFeasting.png/141px-AmuletofFeasting.png" decoding="async" loading="lazy" width="94" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 96px"><div style="width: 96px">
			<div class="thumb" style="width: 94px;"><div style="margin:0px auto;"><a href="/wiki/Amulet_of_Energy"><img alt="" src="/images/thumb/7/76/AmuletofEnergy.png/141px-AmuletofEnergy.png" decoding="async" loading="lazy" width="94" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 99.333333333333px"><div style="width: 99.333333333333px">
			<div class="thumb" style="width: 97.333333333333px;"><div style="margin:0px auto;"><a href="/wiki/Amulet_of_Darkness"><img alt="" src="/images/thumb/d/d9/AmuletofDarkness.png/146px-AmuletofDarkness.png" decoding="async" loading="lazy" width="98" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 96px"><div style="width: 96px">
			<div class="thumb" style="width: 94px;"><div style="margin:0px auto;"><a href="/wiki/Amulet_of_Steel"><img alt="" src="/images/thumb/6/62/AmuletofSteel.png/141px-AmuletofSteel.png" decoding="async" loading="lazy" width="94" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 145.33333333333px"><div style="width: 145.33333333333px">
			<div class="thumb" style="width: 143.33333333333px;"><div style="margin:0px auto;"><a href="/wiki/Platinum_Customer_Card"><img alt="" src="/images/a/a1/PlatinumCustomerCard.png" decoding="async" loading="lazy" width="144" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Shepherd%27s_Crook"><img alt="" src="/images/thumb/9/9e/ShepherdsCrook.png/68px-ShepherdsCrook.png" decoding="async" loading="lazy" width="46" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 104px"><div style="width: 104px">
			<div class="thumb" style="width: 102px;"><div style="margin:0px auto;"><a href="/wiki/Snowball"><img alt="" src="/images/0/04/Snowball.png" decoding="async" loading="lazy" width="102" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 98px"><div style="width: 98px">
			<div class="thumb" style="width: 96px;"><div style="margin:0px auto;"><a href="/wiki/King_Crown"><img alt="" src="/images/thumb/1/1a/KingCrown.png/144px-KingCrown.png" decoding="async" loading="lazy" width="96" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 96px"><div style="width: 96px">
			<div class="thumb" style="width: 94px;"><div style="margin:0px auto;"><a href="/wiki/Amulet_of_Life"><img alt="" src="/images/thumb/3/3c/AmuletofLife.png/141px-AmuletofLife.png" decoding="async" loading="lazy" width="94" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 96px"><div style="width: 96px">
			<div class="thumb" style="width: 94px;"><div style="margin:0px auto;"><a href="/wiki/Amulet_of_the_Wild"><img alt="" src="/images/thumb/5/55/AmuletoftheWild.png/141px-AmuletoftheWild.png" decoding="async" loading="lazy" width="94" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 77.333333333333px"><div style="width: 77.333333333333px">
			<div class="thumb" style="width: 75.333333333333px;"><div style="margin:0px auto;"><a href="/wiki/Stone_Armor"><img alt="" src="/images/thumb/8/8d/StoneArmor.png/113px-StoneArmor.png" decoding="async" loading="lazy" width="76" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 77.333333333333px"><div style="width: 77.333333333333px">
			<div class="thumb" style="width: 75.333333333333px;"><div style="margin:0px auto;"><a href="/wiki/Moon_Armor"><img alt="" src="/images/thumb/c/c4/MoonArmor.png/113px-MoonArmor.png" decoding="async" loading="lazy" width="76" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 76px"><div style="width: 76px">
			<div class="thumb" style="width: 74px;"><div style="margin:0px auto;"><a href="/wiki/Ice_Armor"><img alt="" src="/images/thumb/a/ad/IceArmor.png/111px-IceArmor.png" decoding="async" loading="lazy" width="74" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 76px"><div style="width: 76px">
			<div class="thumb" style="width: 74px;"><div style="margin:0px auto;"><a href="/wiki/Corrupted_Armor"><img alt="" src="/images/thumb/9/9c/CorruptedArmor.png/111px-CorruptedArmor.png" decoding="async" loading="lazy" width="74" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 82.666666666667px"><div style="width: 82.666666666667px">
			<div class="thumb" style="width: 80.666666666667px;"><div style="margin:0px auto;"><a href="/wiki/Vampiric_Armor"><img alt="" src="/images/thumb/f/fb/VampiricArmor.png/121px-VampiricArmor.png" decoding="async" loading="lazy" width="81" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 101.33333333333px"><div style="width: 101.33333333333px">
			<div class="thumb" style="width: 99.333333333333px;"><div style="margin:0px auto;"><a href="/wiki/Box_of_Prosperity"><img alt="" src="/images/thumb/9/94/BoxofProsperity.png/149px-BoxofProsperity.png" decoding="async" loading="lazy" width="100" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 88px"><div style="width: 88px">
			<div class="thumb" style="width: 86px;"><div style="margin:0px auto;"><a href="/wiki/Burning_Coal"><img alt="" src="/images/b/bc/BurningCoal.png" decoding="async" loading="lazy" width="86" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 218px"><div style="width: 218px">
			<div class="thumb" style="width: 216px;"><div style="margin:0px auto;"><a href="/wiki/Gloves_of_Power"><img alt="" src="/images/thumb/1/19/GlovesofPower.png/324px-GlovesofPower.png" decoding="async" loading="lazy" width="216" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 202px"><div style="width: 202px">
			<div class="thumb" style="width: 200px;"><div style="margin:0px auto;"><a href="/wiki/Vampiric_Gloves"><img alt="" src="/images/thumb/b/b8/VampiricGloves.png/300px-VampiricGloves.png" decoding="async" loading="lazy" width="200" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Cap_of_Discomfort"><img alt="" src="/images/thumb/a/a6/CapofDiscomfort.png/86px-CapofDiscomfort.png" decoding="async" loading="lazy" width="58" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Stone_Helm"><img alt="" src="/images/thumb/7/72/StoneHelm.png/87px-StoneHelm.png" decoding="async" loading="lazy" width="58" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 99.333333333333px"><div style="width: 99.333333333333px">
			<div class="thumb" style="width: 97.333333333333px;"><div style="margin:0px auto;"><a href="/wiki/Light_Goobert"><img alt="" src="/images/thumb/c/c2/LightGoobert.png/146px-LightGoobert.png" decoding="async" loading="lazy" width="98" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 68.666666666667px"><div style="width: 68.666666666667px">
			<div class="thumb" style="width: 66.666666666667px;"><div style="margin:0px auto;"><a href="/wiki/King_Goobert"><img alt="" src="/images/thumb/f/f0/KingGoobert.png/100px-KingGoobert.png" decoding="async" loading="lazy" width="67" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 99.333333333333px"><div style="width: 99.333333333333px">
			<div class="thumb" style="width: 97.333333333333px;"><div style="margin:0px auto;"><a href="/wiki/Steel_Goobert"><img alt="" src="/images/thumb/0/08/SteelGoobert.png/146px-SteelGoobert.png" decoding="async" loading="lazy" width="98" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 99.333333333333px"><div style="width: 99.333333333333px">
			<div class="thumb" style="width: 97.333333333333px;"><div style="margin:0px auto;"><a href="/wiki/Goobert"><img alt="" src="/images/thumb/c/c3/Goobert.png/146px-Goobert.png" decoding="async" loading="lazy" width="98" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 99.333333333333px"><div style="width: 99.333333333333px">
			<div class="thumb" style="width: 97.333333333333px;"><div style="margin:0px auto;"><a href="/wiki/Blood_Goobert"><img alt="" src="/images/thumb/0/02/BloodGoobert.png/146px-BloodGoobert.png" decoding="async" loading="lazy" width="98" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 77.333333333333px"><div style="width: 77.333333333333px">
			<div class="thumb" style="width: 75.333333333333px;"><div style="margin:0px auto;"><a href="/wiki/Strong_Heroic_Potion"><img alt="" src="/images/thumb/e/eb/StrongHeroicPotion.png/113px-StrongHeroicPotion.png" decoding="async" loading="lazy" width="76" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Vampiric_Potion"><img alt="" src="/images/thumb/5/54/VampiricPotion.png/53px-VampiricPotion.png" decoding="async" loading="lazy" width="36" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Strong_Health_Potion"><img alt="" src="/images/thumb/c/cd/StrongHealthPotion.png/74px-StrongHealthPotion.png" decoding="async" loading="lazy" width="50" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Mana_Potion"><img alt="" src="/images/thumb/c/c3/ManaPotion.png/71px-ManaPotion.png" decoding="async" loading="lazy" width="48" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Strong_Stone_Skin_Potion"><img alt="" src="/images/thumb/1/1e/StrongStoneSkinPotion.png/83px-StrongStoneSkinPotion.png" decoding="async" loading="lazy" width="56" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 102px"><div style="width: 102px">
			<div class="thumb" style="width: 100px;"><div style="margin:0px auto;"><a href="/wiki/Spiked_Shield"><img alt="" src="/images/thumb/2/25/SpikedShield.png/150px-SpikedShield.png" decoding="async" loading="lazy" width="100" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 99.333333333333px"><div style="width: 99.333333333333px">
			<div class="thumb" style="width: 97.333333333333px;"><div style="margin:0px auto;"><a href="/wiki/Frozen_Buckler"><img alt="" src="/images/thumb/a/ad/FrozenBuckler.png/146px-FrozenBuckler.png" decoding="async" loading="lazy" width="98" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 68px"><div style="width: 68px">
			<div class="thumb" style="width: 66px;"><div style="margin:0px auto;"><a href="/wiki/Moon_Shield"><img alt="" src="/images/thumb/0/08/MoonShield.png/99px-MoonShield.png" decoding="async" loading="lazy" width="66" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Stone_Shoes"><img alt="" src="/images/thumb/7/7d/StoneShoes.png/86px-StoneShoes.png" decoding="async" loading="lazy" width="58" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Hero_Sword"><img alt="" src="/images/thumb/9/9c/HeroSword.png/75px-HeroSword.png" decoding="async" loading="lazy" width="50" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 143.33333333333px"><div style="width: 143.33333333333px">
			<div class="thumb" style="width: 141.33333333333px;"><div style="margin:0px auto;"><a href="/wiki/Eggscalibur"><img alt="" src="/images/thumb/7/76/Eggscalibur.png/212px-Eggscalibur.png" decoding="async" loading="lazy" width="142" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 104px"><div style="width: 104px">
			<div class="thumb" style="width: 102px;"><div style="margin:0px auto;"><a href="/wiki/Crossblades"><img alt="" src="/images/thumb/8/8c/Crossblades.png/153px-Crossblades.png" decoding="async" loading="lazy" width="102" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Hero_Longsword"><img alt="" src="/images/thumb/f/f2/HeroLongsword.png/59px-HeroLongsword.png" decoding="async" loading="lazy" width="40" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Darksaber"><img alt="" src="/images/thumb/e/e8/Darksaber.png/35px-Darksaber.png" decoding="async" loading="lazy" width="24" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Katana"><img alt="" src="/images/thumb/e/e9/Katana.png/21px-Katana.png" decoding="async" loading="lazy" width="14" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Bloody_Dagger"><img alt="" src="/images/thumb/a/a1/BloodyDagger.png/75px-BloodyDagger.png" decoding="async" loading="lazy" width="50" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 286px"><div style="width: 286px">
			<div class="thumb" style="width: 284px;"><div style="margin:0px auto;"><a href="/wiki/Pandamonium"><img alt="" src="/images/thumb/a/a6/Pandamonium.png/426px-Pandamonium.png" decoding="async" loading="lazy" width="284" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Magic_Torch"><img alt="" src="/images/thumb/0/01/MagicTorch.png/65px-MagicTorch.png" decoding="async" loading="lazy" width="44" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Holy_Spear"><img alt="" src="/images/thumb/a/a1/HolySpear.png/30px-HolySpear.png" decoding="async" loading="lazy" width="20" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Serpent_Staff"><img alt="" src="/images/thumb/2/25/SerpentStaff.png/45px-SerpentStaff.png" decoding="async" loading="lazy" width="30" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Prismatic_Sword"><img alt="" src="/images/thumb/3/3f/PrismaticSword.png/54px-PrismaticSword.png" decoding="async" loading="lazy" width="36" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Spectral_Dagger"><img alt="" src="/images/thumb/1/12/SpectralDagger.png/56px-SpectralDagger.png" decoding="async" loading="lazy" width="38" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Poison_Dagger"><img alt="" src="/images/thumb/f/f4/PoisonDagger.png/75px-PoisonDagger.png" decoding="async" loading="lazy" width="50" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Burning_Torch"><img alt="" src="/images/thumb/9/9c/BurningTorch.png/60px-BurningTorch.png" decoding="async" loading="lazy" width="40" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Shovel"><img alt="" src="/images/thumb/d/d5/Shovel_item.png/35px-Shovel_item.png" decoding="async" loading="lazy" width="24" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Manathirst"><img alt="" src="/images/thumb/5/53/Manathirst.png/50px-Manathirst.png" decoding="async" loading="lazy" width="34" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Magic_Staff"><img alt="" src="/images/thumb/7/7b/MagicStaff.png/38px-MagicStaff.png" decoding="async" loading="lazy" width="26" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Frostbite"><img alt="" src="/images/thumb/e/ee/Frostbite.png/46px-Frostbite.png" decoding="async" loading="lazy" width="31" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Shell_Totem"><img alt="" src="/images/thumb/b/b3/ShellTotem.png/76px-ShellTotem.png" decoding="async" loading="lazy" width="51" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 186px"><div style="width: 186px">
			<div class="thumb" style="width: 184px;"><div style="margin:0px auto;"><a href="/wiki/Claws_of_Attack"><img alt="" src="/images/thumb/f/f4/ClawsofAttack.png/276px-ClawsofAttack.png" decoding="async" loading="lazy" width="184" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Falcon_Blade"><img alt="" src="/images/thumb/f/f2/FalconBlade.png/57px-FalconBlade.png" decoding="async" loading="lazy" width="38" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Snow_Stick"><img alt="" src="/images/thumb/8/8f/SnowStick.png/38px-SnowStick.png" decoding="async" loading="lazy" width="26" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 99.333333333333px"><div style="width: 99.333333333333px">
			<div class="thumb" style="width: 97.333333333333px;"><div style="margin:0px auto;"><a href="/wiki/Thornbloom"><img alt="" src="/images/thumb/6/6f/Thornbloom.png/146px-Thornbloom.png" decoding="async" loading="lazy" width="98" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Torch"><img alt="" src="/images/thumb/b/b2/Torch.png/68px-Torch.png" decoding="async" loading="lazy" width="46" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Bloodthorne"><img alt="" src="/images/thumb/0/02/Bloodthorne.png/50px-Bloodthorne.png" decoding="async" loading="lazy" width="34" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 96.666666666667px"><div style="width: 96.666666666667px">
			<div class="thumb" style="width: 94.666666666667px;"><div style="margin:0px auto;"><a href="/wiki/Ruby_Whelp"><img alt="" src="/images/thumb/2/2a/RubyWhelp.png/142px-RubyWhelp.png" decoding="async" loading="lazy" width="95" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 97.333333333333px"><div style="width: 97.333333333333px">
			<div class="thumb" style="width: 95.333333333333px;"><div style="margin:0px auto;"><a href="/wiki/Stone_Golem"><img alt="" src="/images/thumb/8/8e/StoneGolem.png/143px-StoneGolem.png" decoding="async" loading="lazy" width="96" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
</ul>
</div></div></td></tr></tbody></table>
<table class="navbox mw-collapsible mw-collapsed" data-expandtext="Show" data-collapsetext="Hide"><tbody><tr class="navbox-title"><th colspan="2" scope="col"><span class="navbox-vde"><a href="/wiki/Template:RecipesImages" title="Template:RecipesImages"><span title="View this navbox template">v</span></a> · <span class="plainlinks"><a class="external text" href="https://backpackbattles.wiki.gg/wiki/Template_talk:RecipesImages"><span title="Discuss this navbox template">d</span></a> · <a class="external text" href="https://backpackbattles.wiki.gg/wiki/Template:RecipesImages?action=edit"><span title="Edit this navbox template">e</span></a></span></span><a href="/wiki/Ranger#Recipes" title="Ranger">Ranger recipe items</a></th></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><td class="navbox-list no-group" colspan="2"><div class="hlist"><div id="mw-category-media">
<ul class="gallery mw-gallery-packed">
		<li class="gallerybox" style="width: 178.66666666667px"><div style="width: 178.66666666667px">
			<div class="thumb" style="width: 176.66666666667px;"><div style="margin:0px auto;"><a href="/wiki/Lucky_Piggy"><img alt="" src="/images/thumb/6/64/LuckyPiggy.png/265px-LuckyPiggy.png" decoding="async" loading="lazy" width="177" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 92.666666666667px"><div style="width: 92.666666666667px">
			<div class="thumb" style="width: 90.666666666667px;"><div style="margin:0px auto;"><a href="/wiki/Carrot_Goobert"><img alt="" src="/images/thumb/8/88/CarrotGoobert.png/136px-CarrotGoobert.png" decoding="async" loading="lazy" width="91" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 99.333333333333px"><div style="width: 99.333333333333px">
			<div class="thumb" style="width: 97.333333333333px;"><div style="margin:0px auto;"><a href="/wiki/Rainbow_Goobert_Megasludge_Alphapuddle"><img alt="" src="/images/thumb/d/d7/RainbowGoobertMegasludgeAlphapuddle.png/146px-RainbowGoobertMegasludgeAlphapuddle.png" decoding="async" loading="lazy" width="98" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Rat_Chef"><img alt="" src="/images/thumb/7/75/RatChef.png/75px-RatChef.png" decoding="async" loading="lazy" width="50" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 101.33333333333px"><div style="width: 101.33333333333px">
			<div class="thumb" style="width: 99.333333333333px;"><div style="margin:0px auto;"><a href="/wiki/Fortuna%27s_Grace"><img alt="" src="/images/thumb/d/d5/FortunasGrace.png/149px-FortunasGrace.png" decoding="async" loading="lazy" width="100" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Critwood_Staff"><img alt="" src="/images/thumb/3/33/CritwoodStaff.png/39px-CritwoodStaff.png" decoding="async" loading="lazy" width="26" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Belladonna%27s_Shade"><img alt="" src="/images/thumb/b/bf/BelladonnasShade.png/73px-BelladonnasShade.png" decoding="async" loading="lazy" width="49" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 99.333333333333px"><div style="width: 99.333333333333px">
			<div class="thumb" style="width: 97.333333333333px;"><div style="margin:0px auto;"><a href="/wiki/Belladonna%27s_Whisper"><img alt="" src="/images/a/ac/BelladonnasWhisper.png" decoding="async" loading="lazy" width="98" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 104.66666666667px"><div style="width: 104.66666666667px">
			<div class="thumb" style="width: 102.66666666667px;"><div style="margin:0px auto;"><a href="/wiki/Tusk_Piercer"><img alt="" src="/images/thumb/8/87/TuskPiercer.png/154px-TuskPiercer.png" decoding="async" loading="lazy" width="103" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Tusk_Poker"><img alt="" src="/images/thumb/b/ba/TuskPoker.png/69px-TuskPoker.png" decoding="async" loading="lazy" width="46" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Fortuna%27s_Hope"><img alt="" src="/images/thumb/8/81/FortunasHope.png/69px-FortunasHope.png" decoding="async" loading="lazy" width="46" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 104.66666666667px"><div style="width: 104.66666666667px">
			<div class="thumb" style="width: 102.66666666667px;"><div style="margin:0px auto;"><a href="/wiki/Squirrel_Archer"><img alt="" src="/images/thumb/a/aa/SquirrelArcher.png/154px-SquirrelArcher.png" decoding="async" loading="lazy" width="103" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
</ul>
</div></div></td></tr></tbody></table>
<table class="navbox mw-collapsible mw-collapsed" data-expandtext="Show" data-collapsetext="Hide"><tbody><tr class="navbox-title"><th colspan="2" scope="col"><span class="navbox-vde"><a href="/wiki/Template:RecipesImages" title="Template:RecipesImages"><span title="View this navbox template">v</span></a> · <span class="plainlinks"><a class="external text" href="https://backpackbattles.wiki.gg/wiki/Template_talk:RecipesImages"><span title="Discuss this navbox template">d</span></a> · <a class="external text" href="https://backpackbattles.wiki.gg/wiki/Template:RecipesImages?action=edit"><span title="Edit this navbox template">e</span></a></span></span><a href="/wiki/Reaper#Recipes" title="Reaper">Reaper recipe items</a></th></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><td class="navbox-list no-group" colspan="2"><div class="hlist"><div id="mw-category-media">
<ul class="gallery mw-gallery-packed">
		<li class="gallerybox" style="width: 146.66666666667px"><div style="width: 146.66666666667px">
			<div class="thumb" style="width: 144.66666666667px;"><div style="margin:0px auto;"><a href="/wiki/Doom_Cap"><img alt="" src="/images/thumb/4/42/DoomCap.png/217px-DoomCap.png" decoding="async" loading="lazy" width="145" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 99.333333333333px"><div style="width: 99.333333333333px">
			<div class="thumb" style="width: 97.333333333333px;"><div style="margin:0px auto;"><a href="/wiki/Poison_Goobert"><img alt="" src="/images/thumb/3/39/PoisonGoobert.png/146px-PoisonGoobert.png" decoding="async" loading="lazy" width="98" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 99.333333333333px"><div style="width: 99.333333333333px">
			<div class="thumb" style="width: 97.333333333333px;"><div style="margin:0px auto;"><a href="/wiki/Rainbow_Goobert_Omegaooze_Primeslime"><img alt="" src="/images/thumb/2/2c/RainbowGoobertOmegaoozePrimeslime.png/146px-RainbowGoobertOmegaoozePrimeslime.png" decoding="async" loading="lazy" width="98" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Strong_Divine_Potion"><img alt="" src="/images/thumb/3/31/StrongDivinePotion.png/76px-StrongDivinePotion.png" decoding="async" loading="lazy" width="51" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Strong_Demonic_Flask"><img alt="" src="/images/thumb/1/17/StrongDemonicFlask.png/74px-StrongDemonicFlask.png" decoding="async" loading="lazy" width="50" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Strong_Pestilence_Flask"><img alt="" src="/images/thumb/7/7a/StrongPestilenceFlask.png/74px-StrongPestilenceFlask.png" decoding="async" loading="lazy" width="50" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Strong_Mana_Potion"><img alt="" src="/images/thumb/b/bd/StrongManaPotion.png/72px-StrongManaPotion.png" decoding="async" loading="lazy" width="48" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Strong_Vampiric_Potion"><img alt="" src="/images/thumb/3/33/StrongVampiricPotion.png/71px-StrongVampiricPotion.png" decoding="async" loading="lazy" width="48" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Staff_of_Unhealing"><img alt="" src="/images/thumb/b/be/StaffofUnhealing.png/36px-StaffofUnhealing.png" decoding="async" loading="lazy" width="24" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 72px"><div style="width: 72px">
			<div class="thumb" style="width: 70px;"><div style="margin:0px auto;"><a href="/wiki/Ice_Dragon"><img alt="" src="/images/thumb/a/a3/IceDragon.png/105px-IceDragon.png" decoding="async" loading="lazy" width="70" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 69.333333333333px"><div style="width: 69.333333333333px">
			<div class="thumb" style="width: 67.333333333333px;"><div style="margin:0px auto;"><a href="/wiki/Ruby_Chonk"><img alt="" src="/images/thumb/6/6c/RubyChonk.png/101px-RubyChonk.png" decoding="async" loading="lazy" width="68" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
</ul>
</div></div></td></tr></tbody></table>
<table class="navbox mw-collapsible mw-collapsed" data-expandtext="Show" data-collapsetext="Hide"><tbody><tr class="navbox-title"><th colspan="2" scope="col"><span class="navbox-vde"><a href="/wiki/Template:RecipesImages" title="Template:RecipesImages"><span title="View this navbox template">v</span></a> · <span class="plainlinks"><a class="external text" href="https://backpackbattles.wiki.gg/wiki/Template_talk:RecipesImages"><span title="Discuss this navbox template">d</span></a> · <a class="external text" href="https://backpackbattles.wiki.gg/wiki/Template:RecipesImages?action=edit"><span title="Edit this navbox template">e</span></a></span></span><a href="/wiki/Berserker#Recipes" title="Berserker">Berserker recipe items</a></th></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><td class="navbox-list no-group" colspan="2"><div class="hlist"><div id="mw-category-media">
<ul class="gallery mw-gallery-packed">
		<li class="gallerybox" style="width: 78.666666666667px"><div style="width: 78.666666666667px">
			<div class="thumb" style="width: 76.666666666667px;"><div style="margin:0px auto;"><a href="/wiki/Dragonscale_Armor"><img alt="" src="/images/thumb/1/1f/DragonscaleArmor.png/115px-DragonscaleArmor.png" decoding="async" loading="lazy" width="77" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 184px"><div style="width: 184px">
			<div class="thumb" style="width: 182px;"><div style="margin:0px auto;"><a href="/wiki/Dragon_Claws"><img alt="" src="/images/thumb/c/c5/DragonClaws.png/273px-DragonClaws.png" decoding="async" loading="lazy" width="182" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 99.333333333333px"><div style="width: 99.333333333333px">
			<div class="thumb" style="width: 97.333333333333px;"><div style="margin:0px auto;"><a href="/wiki/Cheese_Goobert"><img alt="" src="/images/thumb/c/c4/CheeseGoobert.png/146px-CheeseGoobert.png" decoding="async" loading="lazy" width="98" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 196.66666666667px"><div style="width: 196.66666666667px">
			<div class="thumb" style="width: 194.66666666667px;"><div style="margin:0px auto;"><a href="/wiki/Armored_Power_Puppy"><img alt="" src="/images/thumb/8/8f/ArmoredPowerPuppy.png/292px-ArmoredPowerPuppy.png" decoding="async" loading="lazy" width="195" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Armored_Wisdom_Puppy"><img alt="" src="/images/thumb/5/5a/ArmoredWisdomPuppy.png/77px-ArmoredWisdomPuppy.png" decoding="async" loading="lazy" width="52" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Dragonskin_Boots"><img alt="" src="/images/thumb/6/6b/DragonskinBoots.png/86px-DragonskinBoots.png" decoding="async" loading="lazy" width="58" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 64.666666666667px"><div style="width: 64.666666666667px">
			<div class="thumb" style="width: 62.666666666667px;"><div style="margin:0px auto;"><a href="/wiki/Double_Axe"><img alt="" src="/images/thumb/d/d7/DoubleAxe.png/94px-DoubleAxe.png" decoding="async" loading="lazy" width="63" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Spiked_Staff"><img alt="" src="/images/thumb/a/a0/SpikedStaff.png/44px-SpikedStaff.png" decoding="async" loading="lazy" width="30" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Busted_Blade"><img alt="" src="/images/thumb/a/a9/BustedBlade.png/54px-BustedBlade.png" decoding="async" loading="lazy" width="36" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 100px"><div style="width: 100px">
			<div class="thumb" style="width: 98px;"><div style="margin:0px auto;"><a href="/wiki/Chain_Whip"><img alt="" src="/images/thumb/9/93/ChainWhip.png/147px-ChainWhip.png" decoding="async" loading="lazy" width="98" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 172px"><div style="width: 172px">
			<div class="thumb" style="width: 170px;"><div style="margin:0px auto;"><a href="/wiki/Armored_Courage_Puppy"><img alt="" src="/images/thumb/2/23/ArmoredCouragePuppy.png/255px-ArmoredCouragePuppy.png" decoding="async" loading="lazy" width="170" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
</ul>
</div></div></td></tr></tbody></table>
<table class="navbox mw-collapsible mw-collapsed" data-expandtext="Show" data-collapsetext="Hide"><tbody><tr class="navbox-title"><th colspan="2" scope="col"><span class="navbox-vde"><a href="/wiki/Template:RecipesImages" title="Template:RecipesImages"><span title="View this navbox template">v</span></a> · <span class="plainlinks"><a class="external text" href="https://backpackbattles.wiki.gg/wiki/Template_talk:RecipesImages"><span title="Discuss this navbox template">d</span></a> · <a class="external text" href="https://backpackbattles.wiki.gg/wiki/Template:RecipesImages?action=edit"><span title="Edit this navbox template">e</span></a></span></span><a href="/wiki/Pyromancer#Recipes" title="Pyromancer">Pyromancer recipe items</a></th></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><td class="navbox-list no-group" colspan="2"><div class="hlist"><div id="mw-category-media">
<ul class="gallery mw-gallery-packed">
		<li class="gallerybox" style="width: 77.333333333333px"><div style="width: 77.333333333333px">
			<div class="thumb" style="width: 75.333333333333px;"><div style="margin:0px auto;"><a href="/wiki/Sun_Armor"><img alt="" src="/images/thumb/7/72/SunArmor.png/113px-SunArmor.png" decoding="async" loading="lazy" width="76" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 99.333333333333px"><div style="width: 99.333333333333px">
			<div class="thumb" style="width: 97.333333333333px;"><div style="margin:0px auto;"><a href="/wiki/Chili_Goobert"><img alt="" src="/images/thumb/6/66/ChiliGoobert.png/146px-ChiliGoobert.png" decoding="async" loading="lazy" width="98" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 99.333333333333px"><div style="width: 99.333333333333px">
			<div class="thumb" style="width: 97.333333333333px;"><div style="margin:0px auto;"><a href="/wiki/Rainbow_Goobert_Epicglob_Uberviscous"><img alt="" src="/images/thumb/c/ca/RainbowGoobertEpicglobUberviscous.png/146px-RainbowGoobertEpicglobUberviscous.png" decoding="async" loading="lazy" width="98" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 66px"><div style="width: 66px">
			<div class="thumb" style="width: 64px;"><div style="margin:0px auto;"><a href="/wiki/Sun_Shield"><img alt="" src="/images/thumb/1/19/SunShield.png/96px-SunShield.png" decoding="async" loading="lazy" width="64" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Staff_of_Fire"><img alt="" src="/images/thumb/0/07/StaffofFire.png/43px-StaffofFire.png" decoding="async" loading="lazy" width="29" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Burning_Blade"><img alt="" src="/images/thumb/3/36/BurningBlade.png/43px-BurningBlade.png" decoding="async" loading="lazy" width="29" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 105.33333333333px"><div style="width: 105.33333333333px">
			<div class="thumb" style="width: 103.33333333333px;"><div style="margin:0px auto;"><a href="/wiki/Flame_Whip"><img alt="" src="/images/thumb/d/d6/FlameWhip.png/155px-FlameWhip.png" decoding="async" loading="lazy" width="104" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Burning_Sword"><img alt="" src="/images/thumb/4/4d/BurningSword.png/63px-BurningSword.png" decoding="async" loading="lazy" width="42" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Molten_Dagger"><img alt="" src="/images/thumb/6/6b/MoltenDagger.png/59px-MoltenDagger.png" decoding="async" loading="lazy" width="40" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 62px"><div style="width: 62px">
			<div class="thumb" style="width: 60px;"><div style="margin:0px auto;"><a href="/wiki/Molten_Spear"><img alt="" src="/images/thumb/a/a2/MoltenSpear.png/37px-MoltenSpear.png" decoding="async" loading="lazy" width="25" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 99.333333333333px"><div style="width: 99.333333333333px">
			<div class="thumb" style="width: 97.333333333333px;"><div style="margin:0px auto;"><a href="/wiki/Amethyst_Whelp"><img alt="" src="/images/thumb/3/36/AmethystWhelp.png/146px-AmethystWhelp.png" decoding="async" loading="lazy" width="98" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 96.666666666667px"><div style="width: 96.666666666667px">
			<div class="thumb" style="width: 94.666666666667px;"><div style="margin:0px auto;"><a href="/wiki/Sapphire_Whelp"><img alt="" src="/images/thumb/3/3a/SapphireWhelp.png/142px-SapphireWhelp.png" decoding="async" loading="lazy" width="95" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 103.33333333333px"><div style="width: 103.33333333333px">
			<div class="thumb" style="width: 101.33333333333px;"><div style="margin:0px auto;"><a href="/wiki/Emerald_Whelp"><img alt="" src="/images/thumb/1/15/EmeraldWhelp.png/152px-EmeraldWhelp.png" decoding="async" loading="lazy" width="102" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
		<li class="gallerybox" style="width: 72px"><div style="width: 72px">
			<div class="thumb" style="width: 70px;"><div style="margin:0px auto;"><a href="/wiki/Obsidian_Dragon"><img alt="" src="/images/thumb/b/b7/ObsidianDragon.png/105px-ObsidianDragon.png" decoding="async" loading="lazy" width="70" height="100" /></a></div></div>
			<div class="gallerytext">
			</div>
		</div></li>
</ul>
</div></div></td></tr></tbody></table>
<p><br />
</p>
<table class="cargoTable noMerge sortable"><thead><tr><th class="field_Class">Class</th>
<th class="field_Name">Name</th>
<th class="field_Type">Type</th>
<th class="field_Ingredients">Ingredients</th>
<th class="field_Catalyst">Catalyst</th>
</tr></thead>
<tbody><tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Amulet_of_Alchemy" title="Amulet of Alchemy">Amulet of Alchemy</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Unidentified_Amulet" title="Unidentified Amulet">Unidentified Amulet</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Amulet_of_Energy" title="Amulet of Energy">Amulet of Energy</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Unidentified_Amulet" title="Unidentified Amulet">Unidentified Amulet</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Amulet_of_Feasting" title="Amulet of Feasting">Amulet of Feasting</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Unidentified_Amulet" title="Unidentified Amulet">Unidentified Amulet</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Amulet_of_Life" title="Amulet of Life">Amulet of Life</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Unidentified_Amulet" title="Unidentified Amulet">Unidentified Amulet</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Amulet_of_Steel" title="Amulet of Steel">Amulet of Steel</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Unidentified_Amulet" title="Unidentified Amulet">Unidentified Amulet</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Box_of_Prosperity" title="Box of Prosperity">Box of Prosperity</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Box_of_Riches" title="Box of Riches">Box of Riches</a></td>
<td class="field_Catalyst"><a href="/wiki/Maneki-neko" title="Maneki-neko">Maneki-neko</a></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Bunch_of_Coins" title="Bunch of Coins">Bunch of Coins</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Piggybank" title="Piggybank">Piggybank</a></td>
<td class="field_Catalyst"><a href="/wiki/Hammer" title="Hammer">Hammer</a></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Gloves_of_Power" title="Gloves of Power">Gloves of Power</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Gloves_of_Haste" title="Gloves of Haste">Gloves of Haste</a>&#160;+&#160;<a href="/wiki/Stone_Skin_Potion" title="Stone Skin Potion">Stone Skin Potion</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Goobert" title="Goobert">Goobert</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Goobling" title="Goobling">Goobling</a>&#160;+&#160;<a href="/wiki/Goobling" title="Goobling">Goobling</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Platinum_Customer_Card" title="Platinum Customer Card">Platinum Customer Card</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Customer_Card" title="Customer Card">Customer Card</a>&#160;+&#160;<a href="/wiki/Customer_Card" title="Customer Card">Customer Card</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Shepherd%27s_Crook" class="mw-redirect" title="Shepherd&#39;s Crook">Shepherd's Crook</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Broom" title="Broom">Broom</a>&#160;+&#160;<a href="/wiki/Whetstone" title="Whetstone">Whetstone</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Spiked_Shield" title="Spiked Shield">Spiked Shield</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Wooden_Buckler" title="Wooden Buckler">Wooden Buckler</a>&#160;+&#160;<a href="/wiki/Walrus_Tusk" title="Walrus Tusk">Walrus Tusk</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Steel_Goobert" title="Steel Goobert">Steel Goobert</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Goobert" title="Goobert">Goobert</a>&#160;+&#160;<a href="/wiki/Hero_Sword" title="Hero Sword">Hero Sword</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Stone_Armor" title="Stone Armor">Stone Armor</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Leather_Armor" title="Leather Armor">Leather Armor</a>&#160;+&#160;<a href="/wiki/Stone_Skin_Potion" title="Stone Skin Potion">Stone Skin Potion</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Stone_Helm" title="Stone Helm">Stone Helm</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Cap_of_Resilience" title="Cap of Resilience">Cap of Resilience</a>&#160;+&#160;<a href="/wiki/Stone_Skin_Potion" title="Stone Skin Potion">Stone Skin Potion</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Stone_Shoes" title="Stone Shoes">Stone Shoes</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Leather_Boots" title="Leather Boots">Leather Boots</a>&#160;+&#160;<a href="/wiki/Stone_Skin_Potion" title="Stone Skin Potion">Stone Skin Potion</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Strong_Health_Potion" title="Strong Health Potion">Strong Health Potion</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Health_Potion" title="Health Potion">Health Potion</a></td>
<td class="field_Catalyst"><a href="/wiki/Cauldron" title="Cauldron">Cauldron</a></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Strong_Health_Potion" title="Strong Health Potion">Strong Health Potion</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Health_Potion" title="Health Potion">Health Potion</a>&#160;+&#160;<a href="/wiki/Healing_Herbs" title="Healing Herbs">Healing Herbs</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Strong_Heroic_Potion" title="Strong Heroic Potion">Strong Heroic Potion</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Heroic_Potion" title="Heroic Potion">Heroic Potion</a></td>
<td class="field_Catalyst"><a href="/wiki/Cauldron" title="Cauldron">Cauldron</a></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Strong_Heroic_Potion" title="Strong Heroic Potion">Strong Heroic Potion</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Heroic_Potion" title="Heroic Potion">Heroic Potion</a>&#160;+&#160;<a href="/wiki/Banana" title="Banana">Banana</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Strong_Stone_Skin_Potion" title="Strong Stone Skin Potion">Strong Stone Skin Potion</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Stone_Skin_Potion" title="Stone Skin Potion">Stone Skin Potion</a>&#160;+&#160;<a href="/wiki/Stone" title="Stone">Stone</a>&#160;+&#160;<a href="/wiki/Stone" title="Stone">Stone</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Strong_Stone_Skin_Potion" title="Strong Stone Skin Potion">Strong Stone Skin Potion</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Stone_Skin_Potion" title="Stone Skin Potion">Stone Skin Potion</a></td>
<td class="field_Catalyst"><a href="/wiki/Cauldron" title="Cauldron">Cauldron</a></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Amulet_of_Darkness" title="Amulet of Darkness">Amulet of Darkness</a></td>
<td class="field_Type"><a href="/wiki/Accessory" title="Accessory">Accessory</a>&#160;<a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Unidentified_Amulet" title="Unidentified Amulet">Unidentified Amulet</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Heart_of_Darkness" title="Heart of Darkness">Heart of Darkness</a></td>
<td class="field_Type"><a href="/wiki/Accessory" title="Accessory">Accessory</a>&#160;<a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Heart_Container" title="Heart Container">Heart Container</a>&#160;+&#160;<a href="/wiki/Corrupted_Crystal" title="Corrupted Crystal">Corrupted Crystal</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/King_Crown" title="King Crown">King Crown</a></td>
<td class="field_Type"><a href="/wiki/Accessory" title="Accessory">Accessory</a>&#160;<a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Glowing_Crown" title="Glowing Crown">Glowing Crown</a>&#160;+&#160;<a href="/wiki/Box_of_Riches" title="Box of Riches">Box of Riches</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Snowball" title="Snowball">Snowball</a></td>
<td class="field_Type"><a href="/wiki/Accessory" title="Accessory">Accessory</a>&#160;<a href="/wiki/Ice" title="Ice"><img alt="Ice" src="/images/thumb/5/52/Icon_Ice.png/15px-Icon_Ice.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Wonky_Snowman" title="Wonky Snowman">Wonky Snowman</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Amulet_of_the_Wild" title="Amulet of the Wild">Amulet of the Wild</a></td>
<td class="field_Type"><a href="/wiki/Accessory" title="Accessory">Accessory</a>&#160;<a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Unidentified_Amulet" title="Unidentified Amulet">Unidentified Amulet</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Corrupted_Armor" title="Corrupted Armor">Corrupted Armor</a></td>
<td class="field_Type"><a href="/wiki/Armor" title="Armor">Armor</a>&#160;<a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Holy_Armor" title="Holy Armor">Holy Armor</a>&#160;+&#160;<a href="/wiki/Corrupted_Crystal" title="Corrupted Crystal">Corrupted Crystal</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Moon_Armor" title="Moon Armor">Moon Armor</a></td>
<td class="field_Type"><a href="/wiki/Armor" title="Armor">Armor</a>&#160;<a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Holy_Armor" title="Holy Armor">Holy Armor</a>&#160;+&#160;<a href="/wiki/Mana_Orb" title="Mana Orb">Mana Orb</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Ice_Armor" title="Ice Armor">Ice Armor</a></td>
<td class="field_Type"><a href="/wiki/Armor" title="Armor">Armor</a>&#160;<a href="/wiki/Ice" title="Ice"><img alt="Ice" src="/images/thumb/5/52/Icon_Ice.png/15px-Icon_Ice.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Leather_Armor" title="Leather Armor">Leather Armor</a>&#160;+&#160;<a href="/wiki/Snowball" title="Snowball">Snowball</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Vampiric_Armor" title="Vampiric Armor">Vampiric Armor</a></td>
<td class="field_Type"><a href="/wiki/Armor" title="Armor">Armor</a>&#160;<a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Leather_Armor" title="Leather Armor">Leather Armor</a>&#160;+&#160;<a href="/wiki/Blood_Amulet" title="Blood Amulet">Blood Amulet</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Burning_Coal" title="Burning Coal">Burning Coal</a></td>
<td class="field_Type"><a href="/wiki/Gemstone" title="Gemstone">Gemstone</a>&#160;<a href="/wiki/Fire" title="Fire"><img alt="Fire" src="/images/thumb/0/0a/Icon_Fire.png/15px-Icon_Fire.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Lump_of_Coal" title="Lump of Coal">Lump of Coal</a></td>
<td class="field_Catalyst"><a href="/wiki/Fire" title="Fire">Fire</a></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Vampiric_Gloves" title="Vampiric Gloves">Vampiric Gloves</a></td>
<td class="field_Type"><a href="/wiki/Gloves" title="Gloves">Gloves</a>&#160;<a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Gloves_of_Haste" title="Gloves of Haste">Gloves of Haste</a>&#160;+&#160;<a href="/wiki/Blood_Amulet" title="Blood Amulet">Blood Amulet</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Cap_of_Discomfort" title="Cap of Discomfort">Cap of Discomfort</a></td>
<td class="field_Type"><a href="/wiki/Helmet" title="Helmet">Helmet</a>&#160;<a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Cap_of_Resilience" title="Cap of Resilience">Cap of Resilience</a>&#160;+&#160;<a href="/wiki/Corrupted_Crystal" title="Corrupted Crystal">Corrupted Crystal</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/King_Goobert" title="King Goobert">King Goobert</a></td>
<td class="field_Type"><a href="/wiki/Pet" title="Pet">Pet</a>&#160;<a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Goobert" title="Goobert">Goobert</a>&#160;+&#160;<a href="/wiki/King_Crown" title="King Crown">King Crown</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Light_Goobert" title="Light Goobert">Light Goobert</a></td>
<td class="field_Type"><a href="/wiki/Pet" title="Pet">Pet</a>&#160;<a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Goobert" title="Goobert">Goobert</a>&#160;+&#160;<a href="/wiki/Lightsaber" title="Lightsaber">Lightsaber</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Blood_Goobert" title="Blood Goobert">Blood Goobert</a></td>
<td class="field_Type"><a href="/wiki/Pet" title="Pet">Pet</a>&#160;<a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Goobert" title="Goobert">Goobert</a>&#160;+&#160;<a href="/wiki/Blood_Amulet" title="Blood Amulet">Blood Amulet</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Mana_Potion" title="Mana Potion">Mana Potion</a></td>
<td class="field_Type"><a href="/wiki/Potion" title="Potion">Potion</a>&#160;<a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Health_Potion" title="Health Potion">Health Potion</a>&#160;+&#160;<a href="/wiki/Blueberries" title="Blueberries">Blueberries</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Vampiric_Potion" title="Vampiric Potion">Vampiric Potion</a></td>
<td class="field_Type"><a href="/wiki/Potion" title="Potion">Potion</a>&#160;<a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Strong_Health_Potion" title="Strong Health Potion">Strong Health Potion</a></td>
<td class="field_Catalyst"><a href="/wiki/Blood_Amulet" title="Blood Amulet">Blood Amulet</a></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Moon_Shield" title="Moon Shield">Moon Shield</a></td>
<td class="field_Type"><a href="/wiki/Shield" title="Shield">Shield</a>&#160;<a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Shield_of_Valor" title="Shield of Valor">Shield of Valor</a>&#160;+&#160;<a href="/wiki/Mana_Orb" title="Mana Orb">Mana Orb</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Frozen_Buckler" title="Frozen Buckler">Frozen Buckler</a></td>
<td class="field_Type"><a href="/wiki/Shield" title="Shield">Shield</a>&#160;<a href="/wiki/Ice" title="Ice"><img alt="Ice" src="/images/thumb/5/52/Icon_Ice.png/15px-Icon_Ice.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Wooden_Buckler" title="Wooden Buckler">Wooden Buckler</a>&#160;+&#160;<a href="/wiki/Snowball" title="Snowball">Snowball</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Stone_Golem" title="Stone Golem">Stone Golem</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>,&#160;<a href="/wiki/Pet" title="Pet">Pet</a>&#160;<a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Heart_Container" title="Heart Container">Heart Container</a>&#160;+&#160;<a href="/wiki/Stone" title="Stone">Stone</a>&#160;+&#160;<a href="/wiki/Stone" title="Stone">Stone</a>&#160;+&#160;<a href="/wiki/Stone" title="Stone">Stone</a>&#160;+&#160;<a href="/wiki/Stone" title="Stone">Stone</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Ruby_Whelp" title="Ruby Whelp">Ruby Whelp</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>,&#160;<a href="/wiki/Pet" title="Pet">Pet</a>&#160;<a href="/wiki/Fire" title="Fire"><img alt="Fire" src="/images/thumb/0/0a/Icon_Fire.png/15px-Icon_Fire.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Ruby_Egg" title="Ruby Egg">Ruby Egg</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Darksaber" title="Darksaber">Darksaber</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Lightsaber" title="Lightsaber">Lightsaber</a>&#160;+&#160;<a href="/wiki/Corrupted_Crystal" title="Corrupted Crystal">Corrupted Crystal</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Pandamonium" title="Pandamonium">Pandamonium</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Pan" title="Pan">Pan</a>&#160;+&#160;<a href="/wiki/Corrupted_Crystal" title="Corrupted Crystal">Corrupted Crystal</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Burning_Torch" title="Burning Torch">Burning Torch</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Fire" title="Fire"><img alt="Fire" src="/images/thumb/0/0a/Icon_Fire.png/15px-Icon_Fire.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Torch" title="Torch">Torch</a></td>
<td class="field_Catalyst"><a href="/wiki/Fire" title="Fire">Fire</a></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Eggscalibur" title="Eggscalibur">Eggscalibur</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Pan" title="Pan">Pan</a>&#160;+&#160;<a href="/wiki/Heroic_Potion" title="Heroic Potion">Heroic Potion</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Holy_Spear" title="Holy Spear">Holy Spear</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Spear" title="Spear">Spear</a>&#160;+&#160;<a href="/wiki/Glowing_Crown" title="Glowing Crown">Glowing Crown</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Shell_Totem" title="Shell Totem">Shell Totem</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Wooden_Sword" title="Wooden Sword">Wooden Sword</a>&#160;+&#160;<a href="/wiki/Shiny_Shell" title="Shiny Shell">Shiny Shell</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Snow_Stick" title="Snow Stick">Snow Stick</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Ice" title="Ice"><img alt="Ice" src="/images/thumb/5/52/Icon_Ice.png/15px-Icon_Ice.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Broom" title="Broom">Broom</a>&#160;+&#160;<a href="/wiki/Snowball" title="Snowball">Snowball</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Frostbite" title="Frostbite">Frostbite</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Ice" title="Ice"><img alt="Ice" src="/images/thumb/5/52/Icon_Ice.png/15px-Icon_Ice.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Hungry_Blade" title="Hungry Blade">Hungry Blade</a>&#160;+&#160;<a href="/wiki/Snowball" title="Snowball">Snowball</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Magic_Torch" title="Magic Torch">Magic Torch</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Fire" title="Fire"><img alt="Fire" src="/images/thumb/0/0a/Icon_Fire.png/15px-Icon_Fire.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Torch" title="Torch">Torch</a>&#160;+&#160;<a href="/wiki/Mana_Potion" title="Mana Potion">Mana Potion</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Spectral_Dagger" title="Spectral Dagger">Spectral Dagger</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Dagger" title="Dagger">Dagger</a>&#160;+&#160;<a href="/wiki/Mana_Potion" title="Mana Potion">Mana Potion</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Magic_Staff" title="Magic Staff">Magic Staff</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Broom" title="Broom">Broom</a>&#160;+&#160;<a href="/wiki/Mana_Orb" title="Mana Orb">Mana Orb</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Serpent_Staff" title="Serpent Staff">Serpent Staff</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Magic_Staff" title="Magic Staff">Magic Staff</a>&#160;+&#160;<a href="/wiki/Pestilence_Flask" title="Pestilence Flask">Pestilence Flask</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Prismatic_Sword" title="Prismatic Sword">Prismatic Sword</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Wooden_Sword" title="Wooden Sword">Wooden Sword</a>&#160;+&#160;<a href="/wiki/Prismatic_Orb" title="Prismatic Orb">Prismatic Orb</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Manathirst" title="Manathirst">Manathirst</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Hungry_Blade" title="Hungry Blade">Hungry Blade</a>&#160;+&#160;<a href="/wiki/Mana_Orb" title="Mana Orb">Mana Orb</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Claws_of_Attack" title="Claws of Attack">Claws of Attack</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Gloves_of_Haste" title="Gloves of Haste">Gloves of Haste</a>&#160;+&#160;<a href="/wiki/Walrus_Tusk" title="Walrus Tusk">Walrus Tusk</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Crossblades" title="Crossblades">Crossblades</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Falcon_Blade" title="Falcon Blade">Falcon Blade</a>&#160;+&#160;<a href="/wiki/Hero_Longsword" title="Hero Longsword">Hero Longsword</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Falcon_Blade" title="Falcon Blade">Falcon Blade</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Hero_Sword" title="Hero Sword">Hero Sword</a>&#160;+&#160;<a href="/wiki/Gloves_of_Haste" title="Gloves of Haste">Gloves of Haste</a>&#160;+&#160;<a href="/wiki/Gloves_of_Haste" title="Gloves of Haste">Gloves of Haste</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Hero_Longsword" title="Hero Longsword">Hero Longsword</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Hero_Sword" title="Hero Sword">Hero Sword</a>&#160;+&#160;<a href="/wiki/Whetstone" title="Whetstone">Whetstone</a>&#160;+&#160;<a href="/wiki/Whetstone" title="Whetstone">Whetstone</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Hero_Sword" title="Hero Sword">Hero Sword</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Wooden_Sword" title="Wooden Sword">Wooden Sword</a>&#160;+&#160;<a href="/wiki/Whetstone" title="Whetstone">Whetstone</a>&#160;+&#160;<a href="/wiki/Whetstone" title="Whetstone">Whetstone</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Katana" title="Katana">Katana</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Ripsaw_Blade" title="Ripsaw Blade">Ripsaw Blade</a>&#160;+&#160;<a href="/wiki/Whetstone" title="Whetstone">Whetstone</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Poison_Dagger" title="Poison Dagger">Poison Dagger</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Dagger" title="Dagger">Dagger</a>&#160;+&#160;<a href="/wiki/Pestilence_Flask" title="Pestilence Flask">Pestilence Flask</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Shovel" title="Shovel">Shovel</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Broom" title="Broom">Broom</a>&#160;+&#160;<a href="/wiki/Pan" title="Pan">Pan</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Torch" title="Torch">Torch</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Wooden_Sword" title="Wooden Sword">Wooden Sword</a>&#160;+&#160;<a href="/wiki/Lump_of_Coal" title="Lump of Coal">Lump of Coal</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Thornbloom" title="Thornbloom">Thornbloom</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Thorn_Whip" title="Thorn Whip">Thorn Whip</a>&#160;+&#160;<a href="/wiki/Heroic_Potion" title="Heroic Potion">Heroic Potion</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Bloody_Dagger" title="Bloody Dagger">Bloody Dagger</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Dagger" title="Dagger">Dagger</a>&#160;+&#160;<a href="/wiki/Blood_Amulet" title="Blood Amulet">Blood Amulet</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"></td>
<td class="field_Name"><a href="/wiki/Bloodthorne" title="Bloodthorne">Bloodthorne</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Hungry_Blade" title="Hungry Blade">Hungry Blade</a>&#160;+&#160;<a href="/wiki/Thorn_Whip" title="Thorn Whip">Thorn Whip</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Berserker" title="Berserker">Berserker</a></td>
<td class="field_Name"><a href="/wiki/Armored_Power_Puppy" title="Armored Power Puppy">Armored Power Puppy</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Power_Puppy" title="Power Puppy">Power Puppy</a></td>
<td class="field_Catalyst"><a href="/wiki/Utility_Pouch" title="Utility Pouch">Utility Pouch</a></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Berserker" title="Berserker">Berserker</a></td>
<td class="field_Name"><a href="/wiki/Armored_Power_Puppy" title="Armored Power Puppy">Armored Power Puppy</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Power_Puppy" title="Power Puppy">Power Puppy</a></td>
<td class="field_Catalyst"><a href="/wiki/Forging_Hammer" title="Forging Hammer">Forging Hammer</a></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Berserker" title="Berserker">Berserker</a></td>
<td class="field_Name"><a href="/wiki/Armored_Wisdom_Puppy" title="Armored Wisdom Puppy">Armored Wisdom Puppy</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Wisdom_Puppy" title="Wisdom Puppy">Wisdom Puppy</a></td>
<td class="field_Catalyst"><a href="/wiki/Utility_Pouch" title="Utility Pouch">Utility Pouch</a></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Berserker" title="Berserker">Berserker</a></td>
<td class="field_Name"><a href="/wiki/Armored_Wisdom_Puppy" title="Armored Wisdom Puppy">Armored Wisdom Puppy</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Wisdom_Puppy" title="Wisdom Puppy">Wisdom Puppy</a></td>
<td class="field_Catalyst"><a href="/wiki/Forging_Hammer" title="Forging Hammer">Forging Hammer</a></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Berserker" title="Berserker">Berserker</a></td>
<td class="field_Name"><a href="/wiki/Dragon_Claws" title="Dragon Claws">Dragon Claws</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Gloves_of_Haste" title="Gloves of Haste">Gloves of Haste</a></td>
<td class="field_Catalyst"><a href="/wiki/Utility_Pouch" title="Utility Pouch">Utility Pouch</a></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Berserker" title="Berserker">Berserker</a></td>
<td class="field_Name"><a href="/wiki/Dragon_Claws" title="Dragon Claws">Dragon Claws</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Gloves_of_Haste" title="Gloves of Haste">Gloves of Haste</a></td>
<td class="field_Catalyst"><a href="/wiki/Forging_Hammer" title="Forging Hammer">Forging Hammer</a></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Berserker" title="Berserker">Berserker</a></td>
<td class="field_Name"><a href="/wiki/Dragonscale_Armor" title="Dragonscale Armor">Dragonscale Armor</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Leather_Armor" title="Leather Armor">Leather Armor</a></td>
<td class="field_Catalyst"><a href="/wiki/Utility_Pouch" title="Utility Pouch">Utility Pouch</a></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Berserker" title="Berserker">Berserker</a></td>
<td class="field_Name"><a href="/wiki/Dragonscale_Armor" title="Dragonscale Armor">Dragonscale Armor</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Leather_Armor" title="Leather Armor">Leather Armor</a></td>
<td class="field_Catalyst"><a href="/wiki/Forging_Hammer" title="Forging Hammer">Forging Hammer</a></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Berserker" title="Berserker">Berserker</a></td>
<td class="field_Name"><a href="/wiki/Dragonskin_Boots" title="Dragonskin Boots">Dragonskin Boots</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Leather_Boots" title="Leather Boots">Leather Boots</a></td>
<td class="field_Catalyst"><a href="/wiki/Utility_Pouch" title="Utility Pouch">Utility Pouch</a></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Berserker" title="Berserker">Berserker</a></td>
<td class="field_Name"><a href="/wiki/Dragonskin_Boots" title="Dragonskin Boots">Dragonskin Boots</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Leather_Boots" title="Leather Boots">Leather Boots</a></td>
<td class="field_Catalyst"><a href="/wiki/Forging_Hammer" title="Forging Hammer">Forging Hammer</a></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Berserker" title="Berserker">Berserker</a></td>
<td class="field_Name"><a href="/wiki/Cheese_Goobert" title="Cheese Goobert">Cheese Goobert</a></td>
<td class="field_Type"><a href="/wiki/Pet" title="Pet">Pet</a>&#160;<a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Goobert" title="Goobert">Goobert</a>&#160;+&#160;<a href="/wiki/Cheese" title="Cheese">Cheese</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Berserker" title="Berserker">Berserker</a></td>
<td class="field_Name"><a href="/wiki/Armored_Courage_Puppy" title="Armored Courage Puppy">Armored Courage Puppy</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>,&#160;<a href="/wiki/Pet" title="Pet">Pet</a>&#160;<a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Courage_Puppy" title="Courage Puppy">Courage Puppy</a></td>
<td class="field_Catalyst"><a href="/wiki/Utility_Pouch" title="Utility Pouch">Utility Pouch</a></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Berserker" title="Berserker">Berserker</a></td>
<td class="field_Name"><a href="/wiki/Armored_Courage_Puppy" title="Armored Courage Puppy">Armored Courage Puppy</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>,&#160;<a href="/wiki/Pet" title="Pet">Pet</a>&#160;<a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Courage_Puppy" title="Courage Puppy">Courage Puppy</a></td>
<td class="field_Catalyst"><a href="/wiki/Forging_Hammer" title="Forging Hammer">Forging Hammer</a></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Berserker" title="Berserker">Berserker</a></td>
<td class="field_Name"><a href="/wiki/Chain_Whip" title="Chain Whip">Chain Whip</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Thorn_Whip" title="Thorn Whip">Thorn Whip</a></td>
<td class="field_Catalyst"><a href="/wiki/Forging_Hammer" title="Forging Hammer">Forging Hammer</a></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Berserker" title="Berserker">Berserker</a></td>
<td class="field_Name"><a href="/wiki/Chain_Whip" title="Chain Whip">Chain Whip</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Thorn_Whip" title="Thorn Whip">Thorn Whip</a></td>
<td class="field_Catalyst"><a href="/wiki/Utility_Pouch" title="Utility Pouch">Utility Pouch</a></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Berserker" title="Berserker">Berserker</a></td>
<td class="field_Name"><a href="/wiki/Spiked_Staff" title="Spiked Staff">Spiked Staff</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Magic_Staff" title="Magic Staff">Magic Staff</a>&#160;+&#160;<a href="/wiki/Spiked_Collar" title="Spiked Collar">Spiked Collar</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Berserker" title="Berserker">Berserker</a></td>
<td class="field_Name"><a href="/wiki/Busted_Blade" title="Busted Blade">Busted Blade</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Impractically_Large_Greatsword" title="Impractically Large Greatsword">Impractically Large Greatsword</a></td>
<td class="field_Catalyst"><a href="/wiki/Forging_Hammer" title="Forging Hammer">Forging Hammer</a></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Berserker" title="Berserker">Berserker</a></td>
<td class="field_Name"><a href="/wiki/Busted_Blade" title="Busted Blade">Busted Blade</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Impractically_Large_Greatsword" title="Impractically Large Greatsword">Impractically Large Greatsword</a></td>
<td class="field_Catalyst"><a href="/wiki/Utility_Pouch" title="Utility Pouch">Utility Pouch</a></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Berserker" title="Berserker">Berserker</a></td>
<td class="field_Name"><a href="/wiki/Double_Axe" title="Double Axe">Double Axe</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Axe" title="Axe">Axe</a>&#160;+&#160;<a href="/wiki/Axe" title="Axe">Axe</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Pyromancer" title="Pyromancer">Pyromancer</a></td>
<td class="field_Name"><a href="/wiki/Sun_Armor" title="Sun Armor">Sun Armor</a></td>
<td class="field_Type"><a href="/wiki/Armor" title="Armor">Armor</a>&#160;<a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Fire" title="Fire"><img alt="Fire" src="/images/thumb/0/0a/Icon_Fire.png/15px-Icon_Fire.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Holy_Armor" title="Holy Armor">Holy Armor</a>&#160;+&#160;<a href="/wiki/Flame" title="Flame">Flame</a>&#160;+&#160;<a href="/wiki/Flame" title="Flame">Flame</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Pyromancer" title="Pyromancer">Pyromancer</a></td>
<td class="field_Name"><a href="/wiki/Chili_Goobert" title="Chili Goobert">Chili Goobert</a></td>
<td class="field_Type"><a href="/wiki/Pet" title="Pet">Pet</a>&#160;<a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Goobert" title="Goobert">Goobert</a>&#160;+&#160;<a href="/wiki/Chili_Pepper" title="Chili Pepper">Chili Pepper</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Pyromancer" title="Pyromancer">Pyromancer</a></td>
<td class="field_Name"><a href="/wiki/Rainbow_Goobert_Epicglob_Uberviscous" title="Rainbow Goobert Epicglob Uberviscous">Rainbow Goobert Epicglob Uberviscous</a></td>
<td class="field_Type"><a href="/wiki/Pet" title="Pet">Pet</a>&#160;<a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Goobert" title="Goobert">Goobert</a>&#160;+&#160;<a href="/wiki/Blood_Goobert" title="Blood Goobert">Blood Goobert</a>&#160;+&#160;<a href="/wiki/Chili_Goobert" title="Chili Goobert">Chili Goobert</a>&#160;+&#160;<a href="/wiki/Light_Goobert" title="Light Goobert">Light Goobert</a>&#160;+&#160;<a href="/wiki/Steel_Goobert" title="Steel Goobert">Steel Goobert</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Pyromancer" title="Pyromancer">Pyromancer</a></td>
<td class="field_Name"><a href="/wiki/Sun_Shield" title="Sun Shield">Sun Shield</a></td>
<td class="field_Type"><a href="/wiki/Shield" title="Shield">Shield</a>&#160;<a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Fire" title="Fire"><img alt="Fire" src="/images/thumb/0/0a/Icon_Fire.png/15px-Icon_Fire.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Shield_of_Valor" title="Shield of Valor">Shield of Valor</a>&#160;+&#160;<a href="/wiki/Flame" title="Flame">Flame</a>&#160;+&#160;<a href="/wiki/Flame" title="Flame">Flame</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Pyromancer" title="Pyromancer">Pyromancer</a></td>
<td class="field_Name"><a href="/wiki/Emerald_Whelp" title="Emerald Whelp">Emerald Whelp</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>,&#160;<a href="/wiki/Pet" title="Pet">Pet</a>&#160;<a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Emerald_Egg" title="Emerald Egg">Emerald Egg</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Pyromancer" title="Pyromancer">Pyromancer</a></td>
<td class="field_Name"><a href="/wiki/Obsidian_Dragon" title="Obsidian Dragon">Obsidian Dragon</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>,&#160;<a href="/wiki/Pet" title="Pet">Pet</a>&#160;<a href="/wiki/Fire" title="Fire"><img alt="Fire" src="/images/thumb/0/0a/Icon_Fire.png/15px-Icon_Fire.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Ruby_Whelp" title="Ruby Whelp">Ruby Whelp</a>&#160;+&#160;<a href="/wiki/Draconic_Orb" title="Draconic Orb">Draconic Orb</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Pyromancer" title="Pyromancer">Pyromancer</a></td>
<td class="field_Name"><a href="/wiki/Sapphire_Whelp" title="Sapphire Whelp">Sapphire Whelp</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>,&#160;<a href="/wiki/Pet" title="Pet">Pet</a>&#160;<a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Sapphire_Egg" title="Sapphire Egg">Sapphire Egg</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Pyromancer" title="Pyromancer">Pyromancer</a></td>
<td class="field_Name"><a href="/wiki/Amethyst_Whelp" title="Amethyst Whelp">Amethyst Whelp</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Amethyst_Egg" title="Amethyst Egg">Amethyst Egg</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Pyromancer" title="Pyromancer">Pyromancer</a></td>
<td class="field_Name"><a href="/wiki/Staff_of_Fire" title="Staff of Fire">Staff of Fire</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Fire" title="Fire"><img alt="Fire" src="/images/thumb/0/0a/Icon_Fire.png/15px-Icon_Fire.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Magic_Staff" title="Magic Staff">Magic Staff</a>&#160;+&#160;<a href="/wiki/Draconic_Orb" title="Draconic Orb">Draconic Orb</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Pyromancer" title="Pyromancer">Pyromancer</a></td>
<td class="field_Name"><a href="/wiki/Burning_Blade" title="Burning Blade">Burning Blade</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Fire" title="Fire"><img alt="Fire" src="/images/thumb/0/0a/Icon_Fire.png/15px-Icon_Fire.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Burning_Sword" title="Burning Sword">Burning Sword</a>&#160;+&#160;<a href="/wiki/Whetstone" title="Whetstone">Whetstone</a>&#160;+&#160;<a href="/wiki/Whetstone" title="Whetstone">Whetstone</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Pyromancer" title="Pyromancer">Pyromancer</a></td>
<td class="field_Name"><a href="/wiki/Burning_Blade" title="Burning Blade">Burning Blade</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Fire" title="Fire"><img alt="Fire" src="/images/thumb/0/0a/Icon_Fire.png/15px-Icon_Fire.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Hero_Longsword" title="Hero Longsword">Hero Longsword</a>&#160;+&#160;<a href="/wiki/Flame" title="Flame">Flame</a>&#160;+&#160;<a href="/wiki/Flame" title="Flame">Flame</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Pyromancer" title="Pyromancer">Pyromancer</a></td>
<td class="field_Name"><a href="/wiki/Burning_Sword" title="Burning Sword">Burning Sword</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Fire" title="Fire"><img alt="Fire" src="/images/thumb/0/0a/Icon_Fire.png/15px-Icon_Fire.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Hero_Sword" title="Hero Sword">Hero Sword</a>&#160;+&#160;<a href="/wiki/Flame" title="Flame">Flame</a>&#160;+&#160;<a href="/wiki/Flame" title="Flame">Flame</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Pyromancer" title="Pyromancer">Pyromancer</a></td>
<td class="field_Name"><a href="/wiki/Flame_Whip" title="Flame Whip">Flame Whip</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Fire" title="Fire"><img alt="Fire" src="/images/thumb/0/0a/Icon_Fire.png/15px-Icon_Fire.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Thorn_Whip" title="Thorn Whip">Thorn Whip</a>&#160;+&#160;<a href="/wiki/Flame" title="Flame">Flame</a>&#160;+&#160;<a href="/wiki/Flame" title="Flame">Flame</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Pyromancer" title="Pyromancer">Pyromancer</a></td>
<td class="field_Name"><a href="/wiki/Molten_Dagger" title="Molten Dagger">Molten Dagger</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Fire" title="Fire"><img alt="Fire" src="/images/thumb/0/0a/Icon_Fire.png/15px-Icon_Fire.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Dagger" title="Dagger">Dagger</a>&#160;+&#160;<a href="/wiki/Flame" title="Flame">Flame</a>&#160;+&#160;<a href="/wiki/Flame" title="Flame">Flame</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Pyromancer" title="Pyromancer">Pyromancer</a></td>
<td class="field_Name"><a href="/wiki/Molten_Spear" title="Molten Spear">Molten Spear</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Fire" title="Fire"><img alt="Fire" src="/images/thumb/0/0a/Icon_Fire.png/15px-Icon_Fire.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Melee" title="Melee"><img alt="Melee" src="/images/thumb/2/2b/Icon_Melee.png/15px-Icon_Melee.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Spear" title="Spear">Spear</a>&#160;+&#160;<a href="/wiki/Flame" title="Flame">Flame</a>&#160;+&#160;<a href="/wiki/Flame" title="Flame">Flame</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Ranger" title="Ranger">Ranger</a></td>
<td class="field_Name"><a href="/wiki/Lucky_Piggy" title="Lucky Piggy">Lucky Piggy</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Piggybank" title="Piggybank">Piggybank</a>&#160;+&#160;<a href="/wiki/Lucky_Clover" title="Lucky Clover">Lucky Clover</a>&#160;+&#160;<a href="/wiki/Lucky_Clover" title="Lucky Clover">Lucky Clover</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Ranger" title="Ranger">Ranger</a></td>
<td class="field_Name"><a href="/wiki/Carrot_Goobert" title="Carrot Goobert">Carrot Goobert</a></td>
<td class="field_Type"><a href="/wiki/Pet" title="Pet">Pet</a>&#160;<a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Goobert" title="Goobert">Goobert</a>&#160;+&#160;<a href="/wiki/Carrot" title="Carrot">Carrot</a>&#160;+&#160;<a href="/wiki/Carrot" title="Carrot">Carrot</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Ranger" title="Ranger">Ranger</a></td>
<td class="field_Name"><a href="/wiki/Rat_Chef" title="Rat Chef">Rat Chef</a></td>
<td class="field_Type"><a href="/wiki/Pet" title="Pet">Pet</a>&#160;<a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Rat" title="Rat">Rat</a>&#160;+&#160;<a href="/wiki/Healing_Herbs" title="Healing Herbs">Healing Herbs</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Ranger" title="Ranger">Ranger</a></td>
<td class="field_Name"><a href="/wiki/Rainbow_Goobert_Megasludge_Alphapuddle" title="Rainbow Goobert Megasludge Alphapuddle">Rainbow Goobert Megasludge Alphapuddle</a></td>
<td class="field_Type"><a href="/wiki/Pet" title="Pet">Pet</a>&#160;<a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Goobert" title="Goobert">Goobert</a>&#160;+&#160;<a href="/wiki/Blood_Goobert" title="Blood Goobert">Blood Goobert</a>&#160;+&#160;<a href="/wiki/Light_Goobert" title="Light Goobert">Light Goobert</a>&#160;+&#160;<a href="/wiki/Carrot_Goobert" title="Carrot Goobert">Carrot Goobert</a>&#160;+&#160;<a href="/wiki/Steel_Goobert" title="Steel Goobert">Steel Goobert</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Ranger" title="Ranger">Ranger</a></td>
<td class="field_Name"><a href="/wiki/Squirrel_Archer" title="Squirrel Archer">Squirrel Archer</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>,&#160;<a href="/wiki/Pet" title="Pet">Pet</a>&#160;<a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Squirrel" title="Squirrel">Squirrel</a>&#160;+&#160;<a href="/wiki/Shortbow" title="Shortbow">Shortbow</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Ranger" title="Ranger">Ranger</a></td>
<td class="field_Name"><a href="/wiki/Critwood_Staff" title="Critwood Staff">Critwood Staff</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Magic_Staff" title="Magic Staff">Magic Staff</a>&#160;+&#160;<a href="/wiki/Acorn_Collar" title="Acorn Collar">Acorn Collar</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Ranger" title="Ranger">Ranger</a></td>
<td class="field_Name"><a href="/wiki/Belladonna%27s_Shade" title="Belladonna&#39;s Shade">Belladonna's Shade</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Shortbow" title="Shortbow">Shortbow</a>&#160;+&#160;<a href="/wiki/Pestilence_Flask" title="Pestilence Flask">Pestilence Flask</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Ranger" title="Ranger">Ranger</a></td>
<td class="field_Name"><a href="/wiki/Belladonna%27s_Whisper" title="Belladonna&#39;s Whisper">Belladonna's Whisper</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Bow_and_Arrow" title="Bow and Arrow">Bow and Arrow</a>&#160;+&#160;<a href="/wiki/Pestilence_Flask" title="Pestilence Flask">Pestilence Flask</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Ranger" title="Ranger">Ranger</a></td>
<td class="field_Name"><a href="/wiki/Fortuna%27s_Grace" title="Fortuna&#39;s Grace">Fortuna's Grace</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Bow_and_Arrow" title="Bow and Arrow">Bow and Arrow</a>&#160;+&#160;<a href="/wiki/Lucky_Clover" title="Lucky Clover">Lucky Clover</a>&#160;+&#160;<a href="/wiki/Lucky_Clover" title="Lucky Clover">Lucky Clover</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Ranger" title="Ranger">Ranger</a></td>
<td class="field_Name"><a href="/wiki/Fortuna%27s_Hope" title="Fortuna&#39;s Hope">Fortuna's Hope</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Shortbow" title="Shortbow">Shortbow</a>&#160;+&#160;<a href="/wiki/Lucky_Clover" title="Lucky Clover">Lucky Clover</a>&#160;+&#160;<a href="/wiki/Lucky_Clover" title="Lucky Clover">Lucky Clover</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Ranger" title="Ranger">Ranger</a></td>
<td class="field_Name"><a href="/wiki/Tusk_Piercer" title="Tusk Piercer">Tusk Piercer</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Bow_and_Arrow" title="Bow and Arrow">Bow and Arrow</a>&#160;+&#160;<a href="/wiki/Walrus_Tusk" title="Walrus Tusk">Walrus Tusk</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Ranger" title="Ranger">Ranger</a></td>
<td class="field_Name"><a href="/wiki/Tusk_Poker" title="Tusk Poker">Tusk Poker</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Shortbow" title="Shortbow">Shortbow</a>&#160;+&#160;<a href="/wiki/Walrus_Tusk" title="Walrus Tusk">Walrus Tusk</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Reaper" title="Reaper">Reaper</a>,&#160;<a href="/wiki/Pyromancer" title="Pyromancer">Pyromancer</a></td>
<td class="field_Name"><a href="/wiki/Ice_Dragon" title="Ice Dragon">Ice Dragon</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>,&#160;<a href="/wiki/Pet" title="Pet">Pet</a>&#160;<a href="/wiki/Ice" title="Ice"><img alt="Ice" src="/images/thumb/5/52/Icon_Ice.png/15px-Icon_Ice.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Ruby_Whelp" title="Ruby Whelp">Ruby Whelp</a>&#160;+&#160;<a href="/wiki/White-Eyes_Blue_Dragon" title="White-Eyes Blue Dragon">White-Eyes Blue Dragon</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Reaper" title="Reaper">Reaper</a></td>
<td class="field_Name"><a href="/wiki/Strong_Pestilence_Flask" title="Strong Pestilence Flask">Strong Pestilence Flask</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Pestilence_Flask" title="Pestilence Flask">Pestilence Flask</a>&#160;+&#160;<a href="/wiki/Fly_Agaric" title="Fly Agaric">Fly Agaric</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Reaper" title="Reaper">Reaper</a></td>
<td class="field_Name"><a href="/wiki/Strong_Pestilence_Flask" title="Strong Pestilence Flask">Strong Pestilence Flask</a></td>
<td class="field_Type"></td>
<td class="field_Ingredients"><a href="/wiki/Pestilence_Flask" title="Pestilence Flask">Pestilence Flask</a></td>
<td class="field_Catalyst"><a href="/wiki/Cauldron" title="Cauldron">Cauldron</a></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Reaper" title="Reaper">Reaper</a></td>
<td class="field_Name"><a href="/wiki/Doom_Cap" title="Doom Cap">Doom Cap</a></td>
<td class="field_Type"><a href="/wiki/Food" title="Food">Food</a>&#160;<a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Fly_Agaric" title="Fly Agaric">Fly Agaric</a>&#160;+&#160;<a href="/wiki/Demonic_Flask" title="Demonic Flask">Demonic Flask</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Reaper" title="Reaper">Reaper</a></td>
<td class="field_Name"><a href="/wiki/Poison_Goobert" title="Poison Goobert">Poison Goobert</a></td>
<td class="field_Type"><a href="/wiki/Pet" title="Pet">Pet</a>&#160;<a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Goobert" title="Goobert">Goobert</a>&#160;+&#160;<a href="/wiki/Fly_Agaric" title="Fly Agaric">Fly Agaric</a>&#160;+&#160;<a href="/wiki/Fly_Agaric" title="Fly Agaric">Fly Agaric</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Reaper" title="Reaper">Reaper</a></td>
<td class="field_Name"><a href="/wiki/Rainbow_Goobert_Omegaooze_Primeslime" title="Rainbow Goobert Omegaooze Primeslime">Rainbow Goobert Omegaooze Primeslime</a></td>
<td class="field_Type"><a href="/wiki/Pet" title="Pet">Pet</a>&#160;<a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Goobert" title="Goobert">Goobert</a>&#160;+&#160;<a href="/wiki/Blood_Goobert" title="Blood Goobert">Blood Goobert</a>&#160;+&#160;<a href="/wiki/Light_Goobert" title="Light Goobert">Light Goobert</a>&#160;+&#160;<a href="/wiki/Poison_Goobert" title="Poison Goobert">Poison Goobert</a>&#160;+&#160;<a href="/wiki/Steel_Goobert" title="Steel Goobert">Steel Goobert</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Reaper" title="Reaper">Reaper</a></td>
<td class="field_Name"><a href="/wiki/Strong_Demonic_Flask" title="Strong Demonic Flask">Strong Demonic Flask</a></td>
<td class="field_Type"><a href="/wiki/Potion" title="Potion">Potion</a>&#160;<a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Demonic_Flask" title="Demonic Flask">Demonic Flask</a></td>
<td class="field_Catalyst"><a href="/wiki/Cauldron" title="Cauldron">Cauldron</a></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Reaper" title="Reaper">Reaper</a></td>
<td class="field_Name"><a href="/wiki/Strong_Demonic_Flask" title="Strong Demonic Flask">Strong Demonic Flask</a></td>
<td class="field_Type"><a href="/wiki/Potion" title="Potion">Potion</a>&#160;<a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Demonic_Flask" title="Demonic Flask">Demonic Flask</a>&#160;+&#160;<a href="/wiki/Corrupted_Crystal" title="Corrupted Crystal">Corrupted Crystal</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Reaper" title="Reaper">Reaper</a></td>
<td class="field_Name"><a href="/wiki/Strong_Divine_Potion" title="Strong Divine Potion">Strong Divine Potion</a></td>
<td class="field_Type"><a href="/wiki/Potion" title="Potion">Potion</a>&#160;<a href="/wiki/Holy" title="Holy"><img alt="Holy" src="/images/thumb/2/23/Icon_Holy.png/15px-Icon_Holy.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Divine_Potion" title="Divine Potion">Divine Potion</a></td>
<td class="field_Catalyst"><a href="/wiki/Cauldron" title="Cauldron">Cauldron</a></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Reaper" title="Reaper">Reaper</a></td>
<td class="field_Name"><a href="/wiki/Strong_Mana_Potion" title="Strong Mana Potion">Strong Mana Potion</a></td>
<td class="field_Type"><a href="/wiki/Potion" title="Potion">Potion</a>&#160;<a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Mana_Potion" title="Mana Potion">Mana Potion</a></td>
<td class="field_Catalyst"><a href="/wiki/Cauldron" title="Cauldron">Cauldron</a></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Reaper" title="Reaper">Reaper</a></td>
<td class="field_Name"><a href="/wiki/Strong_Vampiric_Potion" title="Strong Vampiric Potion">Strong Vampiric Potion</a></td>
<td class="field_Type"><a href="/wiki/Potion" title="Potion">Potion</a>&#160;<a href="/wiki/Vampiric" title="Vampiric"><img alt="Vampiric" src="/images/thumb/7/74/Icon_Vampiric.png/15px-Icon_Vampiric.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Vampiric_Potion" title="Vampiric Potion">Vampiric Potion</a></td>
<td class="field_Catalyst"><a href="/wiki/Cauldron" title="Cauldron">Cauldron</a></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Reaper" title="Reaper">Reaper</a></td>
<td class="field_Name"><a href="/wiki/Ruby_Chonk" title="Ruby Chonk">Ruby Chonk</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>,&#160;<a href="/wiki/Pet" title="Pet">Pet</a>&#160;<a href="/wiki/Fire" title="Fire"><img alt="Fire" src="/images/thumb/0/0a/Icon_Fire.png/15px-Icon_Fire.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Effect" title="Effect"><img alt="Effect" src="/images/thumb/8/8a/Icon_Effect.png/15px-Icon_Effect.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Ranged" title="Ranged"><img alt="Ranged" src="/images/thumb/6/65/Icon_Ranged.png/15px-Icon_Ranged.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Ruby_Whelp" title="Ruby Whelp">Ruby Whelp</a>&#160;+&#160;<a href="/wiki/Holo_Fire_Lizard" title="Holo Fire Lizard">Holo Fire Lizard</a></td>
<td class="field_Catalyst"></td>
</tr>
<tr>
<td class="field_Class"><a href="/wiki/Reaper" title="Reaper">Reaper</a></td>
<td class="field_Name"><a href="/wiki/Staff_of_Unhealing" title="Staff of Unhealing">Staff of Unhealing</a></td>
<td class="field_Type"><a href="/wiki/Weapon" title="Weapon">Weapon</a>&#160;<a href="/wiki/Magic" title="Magic"><img alt="Magic" src="/images/thumb/4/4d/Icon_Magic.png/15px-Icon_Magic.png" decoding="async" loading="lazy" width="15" height="15" /></a><a href="/wiki/Dark" title="Dark"><img alt="Dark" src="/images/thumb/e/e9/Icon_Dark.png/15px-Icon_Dark.png" decoding="async" loading="lazy" width="15" height="15" /></a></td>
<td class="field_Ingredients"><a href="/wiki/Magic_Staff" title="Magic Staff">Magic Staff</a>&#160;+&#160;<a href="/wiki/Demonic_Flask" title="Demonic Flask">Demonic Flask</a></td>
<td class="field_Catalyst"></td>
</tr>
</tbody></table>
<table class="navbox mw-collapsible" data-expandtext="Show" data-collapsetext="Hide"><tbody><tr class="navbox-title"><th colspan="2" scope="col"><span class="navbox-vde"><a href="/wiki/Template:Items" title="Template:Items"><span title="View this navbox template">v</span></a> · <span class="plainlinks"><a class="external text" href="https://backpackbattles.wiki.gg/wiki/Template_talk:Items"><span title="Discuss this navbox template">d</span></a> · <a class="external text" href="https://backpackbattles.wiki.gg/wiki/Template:Items?action=edit"><span title="Edit this navbox template">e</span></a></span></span><a href="/wiki/Items" title="Items">Items</a></th></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Accessory" title="Accessory">Accessory</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Acorn_Collar" title="Acorn Collar">Acorn Collar</a>&#160;• <a href="/wiki/Amulet_of_Alchemy" title="Amulet of Alchemy">Amulet of Alchemy</a>&#160;• <a href="/wiki/Amulet_of_Darkness" title="Amulet of Darkness">Amulet of Darkness</a>&#160;• <a href="/wiki/Amulet_of_Energy" title="Amulet of Energy">Amulet of Energy</a>&#160;• <a href="/wiki/Amulet_of_Feasting" title="Amulet of Feasting">Amulet of Feasting</a>&#160;• <a href="/wiki/Amulet_of_Life" title="Amulet of Life">Amulet of Life</a>&#160;• <a href="/wiki/Amulet_of_Steel" title="Amulet of Steel">Amulet of Steel</a>&#160;• <a href="/wiki/Amulet_of_the_Wild" title="Amulet of the Wild">Amulet of the Wild</a>&#160;• <a href="/wiki/Anvil" title="Anvil">Anvil</a>&#160;• <a href="/wiki/Bag_of_Stones" title="Bag of Stones">Bag of Stones</a>&#160;• <a href="/wiki/Blood_Amulet" title="Blood Amulet">Blood Amulet</a>&#160;• <a href="/wiki/Book_of_Ice" title="Book of Ice">Book of Ice</a>&#160;• <a href="/wiki/Box_of_Riches" title="Box of Riches">Box of Riches</a>&#160;• <a href="/wiki/Bunch_of_Coins" title="Bunch of Coins">Bunch of Coins</a>&#160;• <a href="/wiki/Burning_Banner" title="Burning Banner">Burning Banner</a>&#160;• <a href="/wiki/Cauldron" title="Cauldron">Cauldron</a>&#160;• <a href="/wiki/Customer_Card" title="Customer Card">Customer Card</a>&#160;• <a href="/wiki/Dark_Lantern" title="Dark Lantern">Dark Lantern</a>&#160;• <a href="/wiki/Deck_of_Cards" title="Deck of Cards">Deck of Cards</a>&#160;• <a href="/wiki/Deerwood_Guardian" title="Deerwood Guardian">Deerwood Guardian</a>&#160;• <a href="/wiki/Djinn_Lamp" title="Djinn Lamp">Djinn Lamp</a>&#160;• <a href="/wiki/Draconic_Orb" title="Draconic Orb">Draconic Orb</a>&#160;• <a href="/wiki/Dragon_Nest" title="Dragon Nest">Dragon Nest</a>&#160;• <a href="/wiki/Fanfare" title="Fanfare">Fanfare</a>&#160;• <a href="/wiki/Flame" title="Flame">Flame</a>&#160;• <a href="/wiki/Flame_Badge" title="Flame Badge">Flame Badge</a>&#160;• <a href="/wiki/Flute" title="Flute">Flute</a>&#160;• <a href="/wiki/Friendly_Fire" title="Friendly Fire">Friendly Fire</a>&#160;• <a href="/wiki/Frozen_Flame" title="Frozen Flame">Frozen Flame</a>&#160;• <a href="/wiki/Healing_Herbs" title="Healing Herbs">Healing Herbs</a>&#160;• <a href="/wiki/Heart_Container" title="Heart Container">Heart Container</a>&#160;• <a href="/wiki/Heart_of_Darkness" title="Heart of Darkness">Heart of Darkness</a>&#160;• <a href="/wiki/King_Crown" title="King Crown">King Crown</a>&#160;• <a href="/wiki/Leaf_Badge" title="Leaf Badge">Leaf Badge</a>&#160;• <a href="/wiki/Lucky_Clover" title="Lucky Clover">Lucky Clover</a>&#160;• <a href="/wiki/Lucky_Piggy" title="Lucky Piggy">Lucky Piggy</a>&#160;• <a href="/wiki/Mana_Orb" title="Mana Orb">Mana Orb</a>&#160;• <a href="/wiki/Maneki-neko" title="Maneki-neko">Maneki-neko</a>&#160;• <a href="/wiki/Mega_Clover" title="Mega Clover">Mega Clover</a>&#160;• <a href="/wiki/Miss_Fortune" title="Miss Fortune">Miss Fortune</a>&#160;• <a href="/wiki/Mr._Struggles" title="Mr. Struggles">Mr. Struggles</a>&#160;• <a href="/wiki/Mrs._Struggles" title="Mrs. Struggles">Mrs. Struggles</a>&#160;• <a href="/wiki/Nocturnal_Lock_Lifter" title="Nocturnal Lock Lifter">Nocturnal Lock Lifter</a>&#160;• <a href="/wiki/Oil_Lamp" title="Oil Lamp">Oil Lamp</a>&#160;• <a href="/wiki/Piercing_Arrow" title="Piercing Arrow">Piercing Arrow</a>&#160;• <a href="/wiki/Piggybank" title="Piggybank">Piggybank</a>&#160;• <a href="/wiki/Platinum_Customer_Card" title="Platinum Customer Card">Platinum Customer Card</a>&#160;• <a href="/wiki/Pocket_Sand" title="Pocket Sand">Pocket Sand</a>&#160;• <a href="/wiki/Poison_Ivy" title="Poison Ivy">Poison Ivy</a>&#160;• <a href="/wiki/Present" title="Present">Present</a>&#160;• <a href="/wiki/Prismatic_Orb" title="Prismatic Orb">Prismatic Orb</a>&#160;• <a href="/wiki/Rainbow_Badge" title="Rainbow Badge">Rainbow Badge</a>&#160;• <a href="/wiki/Shaman_Mask" title="Shaman Mask">Shaman Mask</a>&#160;• <a href="/wiki/Shepherd%27s_Crook" class="mw-redirect" title="Shepherd&#39;s Crook">Shepherd's Crook</a>&#160;• <a href="/wiki/Shiny_Shell" title="Shiny Shell">Shiny Shell</a>&#160;• <a href="/wiki/Skull_Badge" title="Skull Badge">Skull Badge</a>&#160;• <a href="/wiki/Snowball" title="Snowball">Snowball</a>&#160;• <a href="/wiki/Spiked_Collar" title="Spiked Collar">Spiked Collar</a>&#160;• <a href="/wiki/Stable_Recombobulator" title="Stable Recombobulator">Stable Recombobulator</a>&#160;• <a href="/wiki/Stone_Badge" title="Stone Badge">Stone Badge</a>&#160;• <a href="/wiki/Time_Dilator" title="Time Dilator">Time Dilator</a>&#160;• <a href="/wiki/Unidentified_Amulet" title="Unidentified Amulet">Unidentified Amulet</a>&#160;• <a href="/wiki/Unstable_Recombobulator" title="Unstable Recombobulator">Unstable Recombobulator</a>&#160;• <a href="/wiki/Walrus_Tusk" title="Walrus Tusk">Walrus Tusk</a>&#160;• <a href="/wiki/Whetstone" title="Whetstone">Whetstone</a>&#160;• <a href="/wiki/Wolf_Badge" title="Wolf Badge">Wolf Badge</a>&#160;• <a href="/wiki/Wolf_Emblem" title="Wolf Emblem">Wolf Emblem</a>&#160;• <a href="/wiki/Wonky_Snowman" title="Wonky Snowman">Wonky Snowman</a>&#160;• <a href="/wiki/Yggdrasil_Leaf" title="Yggdrasil Leaf">Yggdrasil Leaf</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Armor" title="Armor">Armor</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Corrupted_Armor" title="Corrupted Armor">Corrupted Armor</a>&#160;• <a href="/wiki/Dragonscale_Armor" title="Dragonscale Armor">Dragonscale Armor</a>&#160;• <a href="/wiki/Holy_Armor" title="Holy Armor">Holy Armor</a>&#160;• <a href="/wiki/Ice_Armor" title="Ice Armor">Ice Armor</a>&#160;• <a href="/wiki/Leather_Armor" title="Leather Armor">Leather Armor</a>&#160;• <a href="/wiki/Moon_Armor" title="Moon Armor">Moon Armor</a>&#160;• <a href="/wiki/Stone_Armor" title="Stone Armor">Stone Armor</a>&#160;• <a href="/wiki/Sun_Armor" title="Sun Armor">Sun Armor</a>&#160;• <a href="/wiki/Vampiric_Armor" title="Vampiric Armor">Vampiric Armor</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Bag" title="Bag">Bag</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Box_of_Prosperity" title="Box of Prosperity">Box of Prosperity</a>&#160;• <a href="/wiki/Duffle_Bag" title="Duffle Bag">Duffle Bag</a>&#160;• <a href="/wiki/Fanny_Pack" title="Fanny Pack">Fanny Pack</a>&#160;• <a href="/wiki/Fire_Pit" title="Fire Pit">Fire Pit</a>&#160;• <a href="/wiki/Leather_Bag" title="Leather Bag">Leather Bag</a>&#160;• <a href="/wiki/Offering_Bowl" title="Offering Bowl">Offering Bowl</a>&#160;• <a href="/wiki/Potion_Belt" title="Potion Belt">Potion Belt</a>&#160;• <a href="/wiki/Protective_Purse" title="Protective Purse">Protective Purse</a>&#160;• <a href="/wiki/Ranger_Bag" title="Ranger Bag">Ranger Bag</a>&#160;• <a href="/wiki/Relic_Case" title="Relic Case">Relic Case</a>&#160;• <a href="/wiki/Sack_of_Surprises" title="Sack of Surprises">Sack of Surprises</a>&#160;• <a href="/wiki/Stamina_Sack" title="Stamina Sack">Stamina Sack</a>&#160;• <a href="/wiki/Storage_Coffin" title="Storage Coffin">Storage Coffin</a>&#160;• <a href="/wiki/Utility_Pouch" title="Utility Pouch">Utility Pouch</a>&#160;• <a href="/wiki/Vineweave_Basket" title="Vineweave Basket">Vineweave Basket</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Food" title="Food">Food</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Banana" title="Banana">Banana</a>&#160;• <a href="/wiki/Big_Bowl_of_Treats" title="Big Bowl of Treats">Big Bowl of Treats</a>&#160;• <a href="/wiki/Blueberries" title="Blueberries">Blueberries</a>&#160;• <a href="/wiki/Carrot" title="Carrot">Carrot</a>&#160;• <a href="/wiki/Cheese" title="Cheese">Cheese</a>&#160;• <a href="/wiki/Chili_Pepper" title="Chili Pepper">Chili Pepper</a>&#160;• <a href="/wiki/Doom_Cap" title="Doom Cap">Doom Cap</a>&#160;• <a href="/wiki/Fly_Agaric" title="Fly Agaric">Fly Agaric</a>&#160;• <a href="/wiki/Garlic" title="Garlic">Garlic</a>&#160;• <a href="/wiki/Gingerbread_Jerry" title="Gingerbread Jerry">Gingerbread Jerry</a>&#160;• <a href="/wiki/Pineapple" title="Pineapple">Pineapple</a>&#160;• <a href="/wiki/Pumpkin" title="Pumpkin">Pumpkin</a>&#160;• <a href="/wiki/Snowcake" title="Snowcake">Snowcake</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Gemstone" title="Gemstone">Gemstone</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Amethyst" title="Amethyst">Amethyst</a>&#160;• <a href="/wiki/Badger_Rune" title="Badger Rune">Badger Rune</a>&#160;• <a href="/wiki/Burning_Coal" title="Burning Coal">Burning Coal</a>&#160;• <a href="/wiki/Corrupted_Crystal" title="Corrupted Crystal">Corrupted Crystal</a>&#160;• <a href="/wiki/Elephant_Rune" title="Elephant Rune">Elephant Rune</a>&#160;• <a href="/wiki/Emerald" title="Emerald">Emerald</a>&#160;• <a href="/wiki/Hawk_Rune" title="Hawk Rune">Hawk Rune</a>&#160;• <a href="/wiki/Lump_of_Coal" title="Lump of Coal">Lump of Coal</a>&#160;• <a href="/wiki/Ruby" title="Ruby">Ruby</a>&#160;• <a href="/wiki/Sapphire" title="Sapphire">Sapphire</a>&#160;• <a href="/wiki/Tim" title="Tim">Tim</a>&#160;• <a href="/wiki/Topaz" title="Topaz">Topaz</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Gloves" title="Gloves">Gloves</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Dragon_Claws" title="Dragon Claws">Dragon Claws</a>&#160;• <a href="/wiki/Gloves_of_Haste" title="Gloves of Haste">Gloves of Haste</a>&#160;• <a href="/wiki/Gloves_of_Power" title="Gloves of Power">Gloves of Power</a>&#160;• <a href="/wiki/Vampiric_Gloves" title="Vampiric Gloves">Vampiric Gloves</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Helmet" title="Helmet">Helmet</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Cap_of_Discomfort" title="Cap of Discomfort">Cap of Discomfort</a>&#160;• <a href="/wiki/Cap_of_Resilience" title="Cap of Resilience">Cap of Resilience</a>&#160;• <a href="/wiki/Glowing_Crown" title="Glowing Crown">Glowing Crown</a>&#160;• <a href="/wiki/Stone_Helm" title="Stone Helm">Stone Helm</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Pet" title="Pet">Pet</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Amethyst_Egg" title="Amethyst Egg">Amethyst Egg</a>&#160;• <a href="/wiki/Armored_Courage_Puppy" title="Armored Courage Puppy">Armored Courage Puppy</a>&#160;• <a href="/wiki/Armored_Power_Puppy" title="Armored Power Puppy">Armored Power Puppy</a>&#160;• <a href="/wiki/Armored_Wisdom_Puppy" title="Armored Wisdom Puppy">Armored Wisdom Puppy</a>&#160;• <a href="/wiki/Blood_Goobert" title="Blood Goobert">Blood Goobert</a>&#160;• <a href="/wiki/Carrot_Goobert" title="Carrot Goobert">Carrot Goobert</a>&#160;• <a href="/wiki/Cheese_Goobert" title="Cheese Goobert">Cheese Goobert</a>&#160;• <a href="/wiki/Chili_Goobert" title="Chili Goobert">Chili Goobert</a>&#160;• <a href="/wiki/Chtulhu" title="Chtulhu">Chtulhu</a>&#160;• <a href="/wiki/Courage_Puppy" title="Courage Puppy">Courage Puppy</a>&#160;• <a href="/wiki/Cubert" title="Cubert">Cubert</a>&#160;• <a href="/wiki/Emerald_Egg" title="Emerald Egg">Emerald Egg</a>&#160;• <a href="/wiki/Emerald_Whelp" title="Emerald Whelp">Emerald Whelp</a>&#160;• <a href="/wiki/Goobert" title="Goobert">Goobert</a>&#160;• <a href="/wiki/Goobling" title="Goobling">Goobling</a>&#160;• <a href="/wiki/Hedgehog" title="Hedgehog">Hedgehog</a>&#160;• <a href="/wiki/Ice_Dragon" title="Ice Dragon">Ice Dragon</a>&#160;• <a href="/wiki/Jynx_torquilla" title="Jynx torquilla">Jynx torquilla</a>&#160;• <a href="/wiki/King_Goobert" title="King Goobert">King Goobert</a>&#160;• <a href="/wiki/Light_Goobert" title="Light Goobert">Light Goobert</a>&#160;• <a href="/wiki/Obsidian_Dragon" title="Obsidian Dragon">Obsidian Dragon</a>&#160;• <a href="/wiki/Phoenix" title="Phoenix">Phoenix</a>&#160;• <a href="/wiki/Poison_Goobert" title="Poison Goobert">Poison Goobert</a>&#160;• <a href="/wiki/Pop" title="Pop">Pop</a>&#160;• <a href="/wiki/Power_Puppy" title="Power Puppy">Power Puppy</a>&#160;• <a href="/wiki/Rainbow_Goobert_Deathslushy_Mansquisher" title="Rainbow Goobert Deathslushy Mansquisher">Rainbow Goobert Deathslushy Mansquisher</a>&#160;• <a href="/wiki/Rainbow_Goobert_Epicglob_Uberviscous" title="Rainbow Goobert Epicglob Uberviscous">Rainbow Goobert Epicglob Uberviscous</a>&#160;• <a href="/wiki/Rainbow_Goobert_Megasludge_Alphapuddle" title="Rainbow Goobert Megasludge Alphapuddle">Rainbow Goobert Megasludge Alphapuddle</a>&#160;• <a href="/wiki/Rainbow_Goobert_Omegaooze_Primeslime" title="Rainbow Goobert Omegaooze Primeslime">Rainbow Goobert Omegaooze Primeslime</a>&#160;• <a href="/wiki/Rat" title="Rat">Rat</a>&#160;• <a href="/wiki/Rat_Chef" title="Rat Chef">Rat Chef</a>&#160;• <a href="/wiki/Ruby_Chonk" title="Ruby Chonk">Ruby Chonk</a>&#160;• <a href="/wiki/Ruby_Egg" title="Ruby Egg">Ruby Egg</a>&#160;• <a href="/wiki/Ruby_Whelp" title="Ruby Whelp">Ruby Whelp</a>&#160;• <a href="/wiki/Sapphire_Egg" title="Sapphire Egg">Sapphire Egg</a>&#160;• <a href="/wiki/Sapphire_Whelp" title="Sapphire Whelp">Sapphire Whelp</a>&#160;• <a href="/wiki/Snake" title="Snake">Snake</a>&#160;• <a href="/wiki/Squirrel" title="Squirrel">Squirrel</a>&#160;• <a href="/wiki/Squirrel_Archer" title="Squirrel Archer">Squirrel Archer</a>&#160;• <a href="/wiki/Steel_Goobert" title="Steel Goobert">Steel Goobert</a>&#160;• <a href="/wiki/Stone_Golem" title="Stone Golem">Stone Golem</a>&#160;• <a href="/wiki/Toad" title="Toad">Toad</a>&#160;• <a href="/wiki/Unsettling_Presence" title="Unsettling Presence">Unsettling Presence</a>&#160;• <a href="/wiki/Wisdom_Puppy" title="Wisdom Puppy">Wisdom Puppy</a>&#160;• <a href="/wiki/Wolpertinger" title="Wolpertinger">Wolpertinger</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Playing_Card" title="Playing Card">Playing Card</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Ace_of_Spades" title="Ace of Spades">Ace of Spades</a>&#160;• <a href="/wiki/Darkest_Lotus" title="Darkest Lotus">Darkest Lotus</a>&#160;• <a href="/wiki/Holo_Fire_Lizard" title="Holo Fire Lizard">Holo Fire Lizard</a>&#160;• <a href="/wiki/Jimbo" title="Jimbo">Jimbo</a>&#160;• <a href="/wiki/Reverse!" title="Reverse!">Reverse!</a>&#160;• <a href="/wiki/The_Fool" title="The Fool">The Fool</a>&#160;• <a href="/wiki/The_Lovers" title="The Lovers">The Lovers</a>&#160;• <a href="/wiki/White-Eyes_Blue_Dragon" title="White-Eyes Blue Dragon">White-Eyes Blue Dragon</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Potion" title="Potion">Potion</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Demonic_Flask" title="Demonic Flask">Demonic Flask</a>&#160;• <a href="/wiki/Divine_Potion" title="Divine Potion">Divine Potion</a>&#160;• <a href="/wiki/Health_Potion" title="Health Potion">Health Potion</a>&#160;• <a href="/wiki/Heroic_Potion" title="Heroic Potion">Heroic Potion</a>&#160;• <a href="/wiki/Mana_Potion" title="Mana Potion">Mana Potion</a>&#160;• <a href="/wiki/Pestilence_Flask" title="Pestilence Flask">Pestilence Flask</a>&#160;• <a href="/wiki/Stone_Skin_Potion" title="Stone Skin Potion">Stone Skin Potion</a>&#160;• <a href="/wiki/Strong_Demonic_Flask" title="Strong Demonic Flask">Strong Demonic Flask</a>&#160;• <a href="/wiki/Strong_Divine_Potion" title="Strong Divine Potion">Strong Divine Potion</a>&#160;• <a href="/wiki/Strong_Health_Potion" title="Strong Health Potion">Strong Health Potion</a>&#160;• <a href="/wiki/Strong_Heroic_Potion" title="Strong Heroic Potion">Strong Heroic Potion</a>&#160;• <a href="/wiki/Strong_Mana_Potion" title="Strong Mana Potion">Strong Mana Potion</a>&#160;• <a href="/wiki/Strong_Pestilence_Flask" title="Strong Pestilence Flask">Strong Pestilence Flask</a>&#160;• <a href="/wiki/Strong_Stone_Skin_Potion" title="Strong Stone Skin Potion">Strong Stone Skin Potion</a>&#160;• <a href="/wiki/Strong_Vampiric_Potion" title="Strong Vampiric Potion">Strong Vampiric Potion</a>&#160;• <a href="/wiki/Vampiric_Potion" title="Vampiric Potion">Vampiric Potion</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Shield" title="Shield">Shield</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Frozen_Buckler" title="Frozen Buckler">Frozen Buckler</a>&#160;• <a href="/wiki/Moon_Shield" title="Moon Shield">Moon Shield</a>&#160;• <a href="/wiki/Shield_of_Valor" title="Shield of Valor">Shield of Valor</a>&#160;• <a href="/wiki/Spiked_Shield" title="Spiked Shield">Spiked Shield</a>&#160;• <a href="/wiki/Sun_Shield" title="Sun Shield">Sun Shield</a>&#160;• <a href="/wiki/Wooden_Buckler" title="Wooden Buckler">Wooden Buckler</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Shoes" title="Shoes">Shoes</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Dragonskin_Boots" title="Dragonskin Boots">Dragonskin Boots</a>&#160;• <a href="/wiki/Leather_Boots" title="Leather Boots">Leather Boots</a>&#160;• <a href="/wiki/Stone_Shoes" title="Stone Shoes">Stone Shoes</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Weapon" title="Weapon">Weapon</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Amethyst_Whelp" title="Amethyst Whelp">Amethyst Whelp</a>&#160;• <a href="/wiki/Armored_Courage_Puppy" title="Armored Courage Puppy">Armored Courage Puppy</a>&#160;• <a href="/wiki/Artifact_Stone:_Cold" title="Artifact Stone: Cold">Artifact Stone: Cold</a>&#160;• <a href="/wiki/Artifact_Stone:_Death" title="Artifact Stone: Death">Artifact Stone: Death</a>&#160;• <a href="/wiki/Artifact_Stone:_Heat" title="Artifact Stone: Heat">Artifact Stone: Heat</a>&#160;• <a href="/wiki/Axe" title="Axe">Axe</a>&#160;• <a href="/wiki/Belladonna%27s_Shade" title="Belladonna&#39;s Shade">Belladonna's Shade</a>&#160;• <a href="/wiki/Belladonna%27s_Whisper" title="Belladonna&#39;s Whisper">Belladonna's Whisper</a>&#160;• <a href="/wiki/Blood_Harvester" title="Blood Harvester">Blood Harvester</a>&#160;• <a href="/wiki/Bloodthorne" title="Bloodthorne">Bloodthorne</a>&#160;• <a href="/wiki/Bloody_Dagger" title="Bloody Dagger">Bloody Dagger</a>&#160;• <a href="/wiki/Bow_and_Arrow" title="Bow and Arrow">Bow and Arrow</a>&#160;• <a href="/wiki/Brass_Knuckles" title="Brass Knuckles">Brass Knuckles</a>&#160;• <a href="/wiki/Broom" title="Broom">Broom</a>&#160;• <a href="/wiki/Burning_Blade" title="Burning Blade">Burning Blade</a>&#160;• <a href="/wiki/Burning_Sword" title="Burning Sword">Burning Sword</a>&#160;• <a href="/wiki/Burning_Torch" title="Burning Torch">Burning Torch</a>&#160;• <a href="/wiki/Busted_Blade" title="Busted Blade">Busted Blade</a>&#160;• <a href="/wiki/Chain_Whip" title="Chain Whip">Chain Whip</a>&#160;• <a href="/wiki/Claws_of_Attack" title="Claws of Attack">Claws of Attack</a>&#160;• <a href="/wiki/Courage_Puppy" title="Courage Puppy">Courage Puppy</a>&#160;• <a href="/wiki/Critwood_Staff" title="Critwood Staff">Critwood Staff</a>&#160;• <a href="/wiki/Crossblades" title="Crossblades">Crossblades</a>&#160;• <a href="/wiki/Cursed_Dagger" title="Cursed Dagger">Cursed Dagger</a>&#160;• <a href="/wiki/Dagger" title="Dagger">Dagger</a>&#160;• <a href="/wiki/Dancing_Dragon" title="Dancing Dragon">Dancing Dragon</a>&#160;• <a href="/wiki/Darksaber" title="Darksaber">Darksaber</a>&#160;• <a href="/wiki/Death_Scythe" title="Death Scythe">Death Scythe</a>&#160;• <a href="/wiki/Double_Axe" title="Double Axe">Double Axe</a>&#160;• <a href="/wiki/Eggscalibur" title="Eggscalibur">Eggscalibur</a>&#160;• <a href="/wiki/Emerald_Whelp" title="Emerald Whelp">Emerald Whelp</a>&#160;• <a href="/wiki/Falcon_Blade" title="Falcon Blade">Falcon Blade</a>&#160;• <a href="/wiki/Fancy_Fencing_Rapier" title="Fancy Fencing Rapier">Fancy Fencing Rapier</a>&#160;• <a href="/wiki/Flame_Whip" title="Flame Whip">Flame Whip</a>&#160;• <a href="/wiki/Forging_Hammer" title="Forging Hammer">Forging Hammer</a>&#160;• <a href="/wiki/Fortuna%27s_Grace" title="Fortuna&#39;s Grace">Fortuna's Grace</a>&#160;• <a href="/wiki/Fortuna%27s_Hope" title="Fortuna&#39;s Hope">Fortuna's Hope</a>&#160;• <a href="/wiki/Frostbite" title="Frostbite">Frostbite</a>&#160;• <a href="/wiki/Hammer" title="Hammer">Hammer</a>&#160;• <a href="/wiki/Hero_Longsword" title="Hero Longsword">Hero Longsword</a>&#160;• <a href="/wiki/Hero_Sword" title="Hero Sword">Hero Sword</a>&#160;• <a href="/wiki/Holy_Spear" title="Holy Spear">Holy Spear</a>&#160;• <a href="/wiki/Hungry_Blade" title="Hungry Blade">Hungry Blade</a>&#160;• <a href="/wiki/Ice_Dragon" title="Ice Dragon">Ice Dragon</a>&#160;• <a href="/wiki/Impractically_Large_Greatsword" title="Impractically Large Greatsword">Impractically Large Greatsword</a>&#160;• <a href="/wiki/Katana" title="Katana">Katana</a>&#160;• <a href="/wiki/Lightsaber" title="Lightsaber">Lightsaber</a>&#160;• <a href="/wiki/Magic_Staff" title="Magic Staff">Magic Staff</a>&#160;• <a href="/wiki/Magic_Torch" title="Magic Torch">Magic Torch</a>&#160;• <a href="/wiki/Manathirst" title="Manathirst">Manathirst</a>&#160;• <a href="/wiki/Molten_Dagger" title="Molten Dagger">Molten Dagger</a>&#160;• <a href="/wiki/Molten_Spear" title="Molten Spear">Molten Spear</a>&#160;• <a href="/wiki/Obsidian_Dragon" title="Obsidian Dragon">Obsidian Dragon</a>&#160;• <a href="/wiki/Pan" title="Pan">Pan</a>&#160;• <a href="/wiki/Pandamonium" title="Pandamonium">Pandamonium</a>&#160;• <a href="/wiki/Phoenix" title="Phoenix">Phoenix</a>&#160;• <a href="/wiki/Poison_Dagger" title="Poison Dagger">Poison Dagger</a>&#160;• <a href="/wiki/Pop" title="Pop">Pop</a>&#160;• <a href="/wiki/Prismatic_Sword" title="Prismatic Sword">Prismatic Sword</a>&#160;• <a href="/wiki/Pumpkin" title="Pumpkin">Pumpkin</a>&#160;• <a href="/wiki/Ripsaw_Blade" title="Ripsaw Blade">Ripsaw Blade</a>&#160;• <a href="/wiki/Ruby_Chonk" title="Ruby Chonk">Ruby Chonk</a>&#160;• <a href="/wiki/Ruby_Whelp" title="Ruby Whelp">Ruby Whelp</a>&#160;• <a href="/wiki/Sapphire_Whelp" title="Sapphire Whelp">Sapphire Whelp</a>&#160;• <a href="/wiki/Serpent_Staff" title="Serpent Staff">Serpent Staff</a>&#160;• <a href="/wiki/Shell_Totem" title="Shell Totem">Shell Totem</a>&#160;• <a href="/wiki/Shortbow" title="Shortbow">Shortbow</a>&#160;• <a href="/wiki/Shovel" title="Shovel">Shovel</a>&#160;• <a href="/wiki/Snow_Stick" title="Snow Stick">Snow Stick</a>&#160;• <a href="/wiki/Spear" title="Spear">Spear</a>&#160;• <a href="/wiki/Spectral_Dagger" title="Spectral Dagger">Spectral Dagger</a>&#160;• <a href="/wiki/Spiked_Staff" title="Spiked Staff">Spiked Staff</a>&#160;• <a href="/wiki/Squirrel_Archer" title="Squirrel Archer">Squirrel Archer</a>&#160;• <a href="/wiki/Staff_of_Fire" title="Staff of Fire">Staff of Fire</a>&#160;• <a href="/wiki/Staff_of_Unhealing" title="Staff of Unhealing">Staff of Unhealing</a>&#160;• <a href="/wiki/Stone" title="Stone">Stone</a>&#160;• <a href="/wiki/Stone_Golem" title="Stone Golem">Stone Golem</a>&#160;• <a href="/wiki/Thorn_Whip" title="Thorn Whip">Thorn Whip</a>&#160;• <a href="/wiki/Thornbloom" title="Thornbloom">Thornbloom</a>&#160;• <a href="/wiki/Torch" title="Torch">Torch</a>&#160;• <a href="/wiki/Tusk_Piercer" title="Tusk Piercer">Tusk Piercer</a>&#160;• <a href="/wiki/Tusk_Poker" title="Tusk Poker">Tusk Poker</a>&#160;• <a href="/wiki/Villain_Sword" title="Villain Sword">Villain Sword</a>&#160;• <a href="/wiki/Wooden_Sword" title="Wooden Sword">Wooden Sword</a></div></td></tr></tbody></table>
<!-- 
NewPP limit report
Cached time: 20250203110944
Cache expiry: 2592000
Reduced expiry: false
Complications: []
CPU time usage: 0.928 seconds
Real time usage: 2.550 seconds
Preprocessor visited node count: 4251/1000000
Post‐expand include size: 47583/4194304 bytes
Template argument size: 322/4194304 bytes
Highest expansion depth: 6/100
Expensive parser function count: 0/500
Unstrip recursion depth: 0/20
Unstrip post‐expand size: 117596/10000000 bytes
Lua time usage: 0.015/15.000 seconds
Lua memory usage: 897022/52428800 bytes
-->
<!--
Transclusion expansion time report (%,ms,calls,template)
100.00% 2291.046      1 -total
 84.22% 1929.445      6 Template:Navbox
 81.36% 1863.934      1 Template:RecipesImages
  3.45%   78.935      1 Template:Items
  0.65%   14.968      5 Template:))
  0.45%   10.271      5 Template:((
  0.39%    9.044      1 Template:Clear
  0.13%    2.918     37 Template:Icon/melee
  0.12%    2.767     15 Template:Icon/fire
  0.11%    2.632     19 Template:Icon/nature
-->

<!-- Saved in parser cache with key backpackbattles_en:pcache:idhash:382-0!canonical and timestamp 20250203110942 and revision id 10501. Rendering was triggered because: page-view
 -->
</div>
<div class="printfooter" data-nosnippet="">Retrieved from "<a dir="ltr" href="https://backpackbattles.wiki.gg/wiki/Recipe?oldid=10501">https://backpackbattles.wiki.gg/wiki/Recipe?oldid=10501</a>"</div></div>
					<div id="catlinks" class="catlinks" data-mw="interface"><div id="mw-normal-catlinks" class="mw-normal-catlinks"><a href="/wiki/Special:Categories" title="Special:Categories">Category</a>: <ul><li><a href="/wiki/Category:Items" title="Category:Items">Items</a></li></ul></div></div>
				</div>
			</main>
			<aside id="wikigg-sl-rail" data-mw="WggShowcaseLayout">
        <div
            class="wikigg-showcase__unit wikigg-showcase__reserved--mrec"
            data-wgg-unit-type="pubco"
        ><div id="nn_mpu_test"></div><div id="nn_mpu1"></div></div>
        <div
            class="wikigg-showcase__unit "
            data-wgg-unit-type="internal"
        ></div>
        <div
            class="wikigg-showcase__unit "
            data-wgg-unit-type="pubco"
        ><div id="nn_mpu2"></div></div>
</aside>

		</div>
		<aside id="wikigg-sl-footer" data-mw="WggShowcaseLayout">
        <div
            class="wikigg-showcase__unit wikigg-showcase__reserved--lb"
            data-wgg-unit-type="internal"
        ></div>
</aside>

	</div>
</div>
<div id='mw-data-after-content'>
	<div class="mw-cookiewarning-container"><div class="mw-cookiewarning-text"><span>Cookies help us deliver our services. By using our services, you agree to our use of cookies.</span></div><form method="POST"><div class='oo-ui-layout oo-ui-horizontalLayout'><span class='oo-ui-widget oo-ui-widget-enabled oo-ui-buttonElement oo-ui-buttonElement-framed oo-ui-labelElement oo-ui-flaggedElement-progressive oo-ui-buttonWidget'><a role='button' tabindex='0' href='https://www.indie.io/privacy-policy' rel='nofollow' class='oo-ui-buttonElement-button'><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon oo-ui-image-progressive'></span><span class='oo-ui-labelElement-label'>More information</span><span class='oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator oo-ui-image-progressive'></span></a></span><span class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-buttonElement oo-ui-buttonElement-framed oo-ui-labelElement oo-ui-flaggedElement-primary oo-ui-flaggedElement-progressive oo-ui-buttonInputWidget'><button type='submit' tabindex='0' name='disablecookiewarning' value='OK' class='oo-ui-inputWidget-input oo-ui-buttonElement-button'><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon oo-ui-image-invert'></span><span class='oo-ui-labelElement-label'>OK</span><span class='oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator oo-ui-image-invert'></span></button></span></div></form></div>
</div>


<footer id="footer" class="mw-footer" >
	<ul id="footer-info">
	<li id="footer-info-lastmod"> This page was last edited on 9 November 2024, at 08:19.</li>
	<li id="footer-info-copyright"><div class="wikigg-copyright-box">Pages that were created prior to November 2023 are adapted from the Backpack Battles Fandom wiki.<br />Page content is under <a class="external" rel="nofollow" href="https://creativecommons.org/licenses/by-sa/4.0">Creative Commons Attribution-ShareAlike 4.0 License</a> unless otherwise noted.</div></li>
</ul>

	<ul id="footer-places">
	<li id="footer-places-wikigg-tos"><a href="https://www.indie.io/terms-of-service">Terms of Service</a></li>
	<li id="footer-places-wikigg-privacy"><a href="https://www.indie.io/privacy-policy">Privacy policy</a></li>
	<li id="footer-places-wikigg-support"><a href="https://support.wiki.gg">Support Wiki</a></li>
	<li id="footer-places-wikigg-servicedesk"><a href="https://wiki.gg/go/servicedesk">Send a ticket to wiki.gg</a></li>
	<li id="footer-places-wikigg-statuspage"><a href="https://wikiggstatus.com">Status page</a></li>
	<li id="footer-places-wikigg-pcmp"><a href="#" class="nn-cmp-show" style="display: none">Manage cookie settings</a></li>
</ul>

	<ul id="footer-icons" class="noprint">
	<li id="footer-copyrightico"><a href="https://creativecommons.org/licenses/by-sa/4.0" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><img src="https://commons.wiki.gg/images/1/14/CC-BY-SA_footer_badge.svg?2" alt="Creative Commons Attribution-ShareAlike 4.0 License" height="31" width="88" loading="lazy"></a></li>
	<li id="footer-poweredbyico"><a href="https://www.mediawiki.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><img src="/mw-1.43/resources/assets/poweredby_mediawiki.svg" alt="Powered by MediaWiki" width="88" height="31" loading="lazy"></a></li>
	<li id="footer-partofico"><a href="https://wiki.gg" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><img src="https://commons.wiki.gg/images/d/d1/Network_footer_badge.svg?2" alt="Part of wiki.gg" height="31" width="88" loading="lazy"></a></li>
</ul>

</footer>


<script>(RLQ=window.RLQ||[]).push(function(){mw.config.set({"wgBackendResponseTime":335,"wgPageParseReport":{"limitreport":{"cputime":"0.928","walltime":"2.550","ppvisitednodes":{"value":4251,"limit":1000000},"postexpandincludesize":{"value":47583,"limit":4194304},"templateargumentsize":{"value":322,"limit":4194304},"expansiondepth":{"value":6,"limit":100},"expensivefunctioncount":{"value":0,"limit":500},"unstrip-depth":{"value":0,"limit":20},"unstrip-size":{"value":117596,"limit":10000000},"timingprofile":["100.00% 2291.046      1 -total"," 84.22% 1929.445      6 Template:Navbox"," 81.36% 1863.934      1 Template:RecipesImages","  3.45%   78.935      1 Template:Items","  0.65%   14.968      5 Template:))","  0.45%   10.271      5 Template:((","  0.39%    9.044      1 Template:Clear","  0.13%    2.918     37 Template:Icon/melee","  0.12%    2.767     15 Template:Icon/fire","  0.11%    2.632     19 Template:Icon/nature"]},"scribunto":{"limitreport-timeusage":{"value":"0.015","limit":"15.000"},"limitreport-memusage":{"value":897022,"limit":52428800}},"cachereport":{"timestamp":"20250203110944","ttl":2592000,"transientcontent":false}}});});</script>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'950260ff9fa3f852',t:'MTc0OTk5Mzc3NC4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script><script defer src="https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon='{"rayId":"950260ff9fa3f852","serverTiming":{"name":{"cfExtPri":true,"cfEdge":true,"cfOrigin":true,"cfL4":true,"cfSpeedBrain":true,"cfCacheStatus":true}},"version":"2025.6.2","token":"26b2c6e7c852417d8f9d11b0c7f02309"}' crossorigin="anonymous"></script>
</body>
</html>