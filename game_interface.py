from __future__ import annotations
from dataclasses import dataclass, field, replace
from typing import List, Dict, Optional, Union, Tuple, Any, Type, TypeVar, Generic
from enum import Enum, auto
import json
import math
import logging
from functools import lru_cache

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

class ItemType(Enum):
    """Comprehensive enumeration of item types with extensibility."""
    WEAPON = auto()
    ARMOR = auto()
    ACCESSORY = auto()
    CONSUMABLE = auto()
    CRAFTING_MATERIAL = auto()
    SPECIAL = auto()
    QUEST_ITEM = auto()  # New type for quest-related items

class WeaponSubtype(Enum):
    """Enhanced weapon subtypes with more granularity."""
    MELEE = auto()
    RANGED = auto()
    MAGICAL = auto()
    THROWN = auto()
    TWO_HANDED = auto()

class ArmorSubtype(Enum):
    """More detailed armor subtypes."""
    LIGHT = auto()
    MEDIUM = auto()
    HEAVY = auto()
    MAGICAL_WARD = auto()
    ADAPTIVE = auto()

@dataclass(frozen=True)
class Dimensions:
    """Immutable spatial representation with advanced features."""
    width: int
    height: int
    shape: str = 'rectangular'
    rotation_allowed: bool = True
    max_rotations: int = 4

    def calculate_area(self) -> int:
        """Calculate the area occupied by the item."""
        return self.width * self.height

    def can_rotate(self) -> bool:
        """Check if the item can be rotated."""
        return self.rotation_allowed and self.max_rotations > 0

    def get_rotated_dimensions(self) -> Dimensions:
        """Return a new Dimensions object with width and height swapped."""
        return replace(self, width=self.height, height=self.width)

@dataclass
class ItemEffect:
    """Advanced effect calculation with more sophisticated scaling."""
    base_value: float
    type: str
    scaling_factors: Dict[str, float] = field(default_factory=dict)
    diminishing_returns: bool = False

    def calculate_scaled_effect(self, character_stats: Dict[str, float]) -> float:
        """Calculate scaled effect with optional diminishing returns."""
        scaled_value = self.base_value
        for stat, factor in self.scaling_factors.items():
            stat_value = character_stats.get(stat, 0)
            if self.diminishing_returns:
                # Apply logarithmic scaling for diminishing returns
                scaled_value += math.log1p(stat_value) * factor
            else:
                scaled_value += stat_value * factor
        return scaled_value

@dataclass
class ItemRequirements:
    """Enhanced requirements with more complex validation."""
    minimum_strength: Optional[int] = None
    skill_level: Optional[int] = None
    character_class: Optional[str] = None
    faction_reputation: Optional[Dict[str, int]] = None

    def meets_requirements(self, character_stats: Dict[str, Any]) -> bool:
        """Comprehensive requirement validation."""
        if self.minimum_strength and character_stats.get('strength', 0) < self.minimum_strength:
            return False
        if self.skill_level and character_stats.get('level', 0) < self.skill_level:
            return False
        if self.character_class and character_stats.get('class') != self.character_class:
            return False
        if self.faction_reputation:
            for faction, min_rep in self.faction_reputation.items():
                if character_stats.get(f'{faction}_reputation', 0) < min_rep:
                    return False
        return True

@dataclass
class Item:
    """Comprehensive and flexible item representation."""
    id: str
    name: str
    item_type: ItemType
    subtype: Optional[Union[WeaponSubtype, ArmorSubtype]] = None
    dimensions: Dimensions = field(default_factory=lambda: Dimensions(1, 1))
    effects: Dict[str, ItemEffect] = field(default_factory=dict)
    requirements: ItemRequirements = field(default_factory=ItemRequirements)
    rarity: str = 'common'
    weight: float = 0.0
    value: float = 0.0
    tags: List[str] = field(default_factory=list)
    unique_properties: Dict[str, Any] = field(default_factory=dict)

    def calculate_total_weight(self) -> float:
        """Calculate weight with potential modifiers."""
        base_weight = self.weight
        weight_modifiers = self.unique_properties.get('weight_modifiers', [])
        for modifier in weight_modifiers:
            base_weight *= modifier
        return base_weight

    @lru_cache(maxsize=128)
    def get_placement_score(self, inventory_position: Tuple[int, int], grid_size: Tuple[int, int] = (6, 4)) -> float:
        """Advanced placement scoring with caching."""
        x, y = inventory_position
        grid_width, grid_height = grid_size
        
        # Multi-factor placement scoring
        factors = {
            'corner_bonus': 1.2 if (x in [0, grid_width-1]) and (y in [0, grid_height-1]) else 1.0,
            'center_proximity': 1 - (abs(x - grid_width/2) + abs(y - grid_height/2)) / (grid_width + grid_height),
            'rarity_multiplier': {'common': 1.0, 'uncommon': 1.2, 'rare': 1.5, 'legendary': 2.0}.get(self.rarity, 1.0)
        }
        
        return math.prod(factors.values())

@dataclass
class Inventory:
    """Advanced inventory management with flexible configuration."""
    grid_width: int = 6
    grid_height: int = 4
    max_items: Optional[int] = None
    items: List[Item] = field(default_factory=list)
    grid: List[List[Optional[Item]]] = field(init=False)
    
    def __post_init__(self):
        """Initialize grid and set constraints."""
        self.grid = [[None for _ in range(self.grid_width)] for _ in range(self.grid_height)]
        
    def can_place_item(self, item: Item, x: int, y: int, rotated: bool = False) -> bool:
        """Comprehensive item placement validation."""
        if self.max_items is not None and len(self.items) >= self.max_items:
            logger.warning(f"Cannot place item: Inventory full (max {self.max_items} items)")
            return False
        
        dimensions = item.dimensions.get_rotated_dimensions() if rotated else item.dimensions
        width, height = dimensions.width, dimensions.height

        # Check grid boundaries
        if x + width > self.grid_width or y + height > self.grid_height:
            return False

        # Check for overlapping items
        for dx in range(width):
            for dy in range(height):
                if self.grid[y + dy][x + dx] is not None:
                    return False

        return True

    def place_item(self, item: Item, x: int, y: int, rotated: bool = False) -> bool:
        """Enhanced item placement with logging and validation."""
        if not self.can_place_item(item, x, y, rotated):
            logger.error(f"Cannot place item {item.name} at position ({x}, {y})")
            return False

        dimensions = item.dimensions.get_rotated_dimensions() if rotated else item.dimensions
        width, height = dimensions.width, dimensions.height

        for dx in range(width):
            for dy in range(height):
                self.grid[y + dy][x + dx] = item

        self.items.append(item)
        logger.info(f"Placed item {item.name} at position ({x}, {y})")
        return True

    def calculate_positional_synergies(self) -> Dict[str, float]:
        """Advanced synergy calculation with multiple interaction types."""
        synergies = {}
        for i, item1 in enumerate(self.items):
            for j, item2 in enumerate(self.items[i+1:], start=i+1):
                # Check adjacency and interaction potential
                interaction_bonus = self._calculate_item_interaction(item1, item2)
                if interaction_bonus > 0:
                    synergies[f"{item1.name}_{item2.name}"] = interaction_bonus
        return synergies

    def _calculate_item_interaction(self, item1: Item, item2: Item) -> float:
        """Calculate interaction bonus between two items."""
        interaction_score = 0.0
        
        # Type-based interactions
        if item1.item_type == item2.item_type:
            interaction_score += 0.5
        
        # Tag-based interactions
        common_tags = set(item1.tags) & set(item2.tags)
        interaction_score += len(common_tags) * 0.2
        
        return interaction_score

class InventoryManager:
    """Comprehensive inventory management with advanced rules."""
    @staticmethod
    def validate_item_interaction(item1: Item, item2: Item) -> bool:
        """Advanced interaction validation."""
        # Implement complex interaction rules
        incompatible_type_pairs = [
            (ItemType.WEAPON, ItemType.CRAFTING_MATERIAL),
            (ItemType.ARMOR, ItemType.CONSUMABLE)
        ]
        
        if (item1.item_type, item2.item_type) in incompatible_type_pairs or \
           (item2.item_type, item1.item_type) in incompatible_type_pairs:
            return False
        
        return True

    @staticmethod
    def calculate_crafting_probability(ingredients: List[Item], recipe: Dict[str, Any]) -> float:
        """Enhanced crafting probability calculation."""
        base_probability = 0.75
        
        # Factor in ingredient rarity
        rarity_multipliers = {
            'common': 1.0,
            'uncommon': 0.9,
            'rare': 0.8,
            'legendary': 0.6
        }
        
        rarity_factor = min(rarity_multipliers.get(item.rarity, 1.0) for item in ingredients)
        
        # Factor in total ingredient count
        ingredient_count_factor = 1 / math.log(len(ingredients) + 1)
        
        return base_probability * rarity_factor * ingredient_count_factor

def load_game_data(file_path: str = 'game_data.json') -> Dict[str, Any]:
    """Robust game data loading with error handling."""
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.error(f"Game data file not found: {file_path}")
        return {}
    except json.JSONDecodeError:
        logger.error(f"Invalid JSON in game data file: {file_path}")
        return {}

def create_item_from_data(item_data: Dict[str, Any]) -> Optional[Item]:
    """Robust item creation with comprehensive validation."""
    try:
        return Item(
            id=item_data['id'],
            name=item_data['name'],
            item_type=ItemType[item_data['type'].upper()],
            subtype=WeaponSubtype[item_data.get('subtype', 'MELEE').upper()] if item_data['type'] == 'weapon' else None,
            dimensions=Dimensions(
                width=item_data['dimensions']['width'],
                height=item_data['dimensions']['height']
            ),
            effects={k: ItemEffect(base_value=v, type='') for k, v in item_data.get('effects', {}).items()},
            rarity=item_data.get('rarity', 'common'),
            weight=item_data.get('weight', 0.0),
            value=item_data.get('value', 0.0),
            tags=item_data.get('tags', []),
            unique_properties=item_data.get('unique_properties', {})
        )
    except (KeyError, ValueError) as e:
        logger.error(f"Error creating item from data: {e}")
        return None