import requests
from bs4 import BeautifulSoup
import json
import time
import logging
import re
from urllib.parse import urljoin
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict

# Setup logging
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class GridPosition:
    """Represents a position in the item grid"""
    row: int
    col: int
    type: str  # 'star', 'cell', 'diamond', 'empty'

@dataclass
class WeaponStats:
    """Weapon-specific statistics"""
    damage: str = ""
    stamina: str = ""
    accuracy: str = ""
    cooldown: str = ""
    sockets: str = ""

@dataclass
class BagProperties:
    """Bag-specific properties"""
    slots_added: int = 0
    special_effects: List[str] = None
    
    def __post_init__(self):
        if self.special_effects is None:
            self.special_effects = []

@dataclass
class EnhancedItem:
    """Enhanced item with positional and mechanical information"""
    name: str
    type: str
    rarity: str
    cost: str
    effect: str
    image_url: str = ""
    source_url: str = ""
    
    # Positional Information
    grid_layout: List[List[str]] = None
    grid_width: int = 0
    grid_height: int = 0
    positional_synergies: List[str] = None
    
    # Combat Stats (for weapons)
    weapon_stats: WeaponStats = None
    
    # Bag Properties
    bag_properties: BagProperties = None
    
    # Time-based mechanics
    timing_effects: List[str] = None
    
    def __post_init__(self):
        if self.grid_layout is None:
            self.grid_layout = []
        if self.positional_synergies is None:
            self.positional_synergies = []
        if self.timing_effects is None:
            self.timing_effects = []

class EnhancedDataExtractor:
    @staticmethod
    def normalize_text(text: str) -> str:
        """Normalize text by removing extra whitespaces and special characters."""
        if not text:
            return ""
        # Remove wiki markup and extra whitespace
        text = re.sub(r'\[\[.*?\]\]', '', text)  # Remove wiki links
        text = re.sub(r'\{\{.*?\}\}', '', text)  # Remove templates
        text = re.sub(r'<.*?>', '', text)        # Remove HTML tags
        text = re.sub(r'\s+', ' ', text)         # Normalize whitespace
        return text.strip()
    
    @staticmethod
    def extract_cost(text: str) -> str:
        """Extract cost from text."""
        if not text:
            return "0"
        cost_match = re.search(r'(\d+)', text)
        return cost_match.group(1) if cost_match else "0"
    
    @staticmethod
    def parse_grid_layout(soup) -> Tuple[List[List[str]], int, int]:
        """Parse the grid layout from the Grid section."""
        # Look for Grid header in various ways
        grid_section = soup.find('th', string='Grid')
        if not grid_section:
            # Try alternative search in th elements
            grid_section = soup.find('th', string=re.compile(r'Grid', re.IGNORECASE))

        if not grid_section:
            # Try looking for div with title class containing "Grid"
            grid_section = soup.find('div', {'class': 'title'}, string='Grid')

        if not grid_section:
            # Try any element containing "Grid"
            grid_section = soup.find(string=re.compile(r'^Grid$', re.IGNORECASE))
            if grid_section:
                grid_section = grid_section.parent

        if not grid_section:
            logger.debug("No Grid section found")
            return [], 0, 0

        # Find the grid container - could be in different structures
        grid_container = None

        # Method 1: Traditional table structure
        if grid_section.name == 'th':
            grid_container = grid_section.find_parent('tr')
            if grid_container:
                grid_cell = grid_container.find('td')
            else:
                grid_cell = None
        else:
            # Method 2: Look for the grid content after the Grid title
            # Find the next sibling or parent that contains grid images
            current = grid_section
            grid_cell = None

            # Search in the next few siblings/descendants for grid images
            for _ in range(10):  # Limit search depth
                if current is None:
                    break

                # Check if current element contains grid images
                grid_images = current.find_all('img', src=re.compile(r'(cell|star|diamond)', re.IGNORECASE))
                if grid_images:
                    grid_cell = current
                    break

                # Try next sibling
                current = current.next_sibling
                if current and hasattr(current, 'find_all'):
                    continue
                else:
                    # Try parent's next sibling
                    parent = grid_section.parent
                    if parent:
                        current = parent.next_sibling
                    else:
                        break

        if not grid_cell:
            # Last resort: search the entire document for grid images
            grid_images = soup.find_all('img', src=re.compile(r'(cell|star|diamond)', re.IGNORECASE))
            if grid_images:
                # Find a common parent that contains multiple grid images
                for img in grid_images:
                    parent = img.parent
                    while parent:
                        parent_images = parent.find_all('img', src=re.compile(r'(cell|star|diamond)', re.IGNORECASE))
                        if len(parent_images) >= 3:  # Likely contains the full grid
                            grid_cell = parent
                            break
                        parent = parent.parent
                    if grid_cell:
                        break

        if not grid_cell:
            logger.debug("No grid cell found")
            return [], 0, 0

        logger.debug(f"Grid cell HTML: {str(grid_cell)[:200]}...")

        # Parse the grid images
        grid_layout = []

        # Look for all img tags in the grid cell
        images = grid_cell.find_all('img')
        logger.debug(f"Found {len(images)} images in grid")

        if not images:
            return [], 0, 0

        # Extract image types
        image_types = []
        for img in images:
            src = img.get('src', '').lower()
            alt_text = img.get('alt', '').lower()

            if 'star.png' in src or 'star.png' in alt_text:
                image_types.append('star')
            elif 'cell.png' in src or 'cell.png' in alt_text:
                image_types.append('cell')
            elif 'diamond.png' in src or 'diamond.png' in alt_text:
                image_types.append('diamond')
            else:
                image_types.append('unknown')

        logger.debug(f"Image types: {image_types}")

        # Try to determine grid structure from HTML layout
        grid_html = str(grid_cell)

        # Method 1: Look for explicit line breaks
        if '<br' in grid_html.lower():
            # Split by line breaks and count images in each section
            parts = re.split(r'<br[^>]*>', grid_html, flags=re.IGNORECASE)
            current_idx = 0

            for part in parts:
                part_soup = BeautifulSoup(part, 'html.parser')
                part_images = part_soup.find_all('img')

                if part_images:
                    row = []
                    for _ in part_images:
                        if current_idx < len(image_types):
                            row.append(image_types[current_idx])
                            current_idx += 1
                    if row:
                        grid_layout.append(row)

        # Method 2: If no line breaks, try to infer from common patterns
        if not grid_layout and image_types:
            # Common patterns: 1x1, 2x1, 3x1, 2x2, 3x2, etc.
            num_images = len(image_types)

            if num_images == 1:
                grid_layout = [image_types]
            elif num_images == 2:
                grid_layout = [image_types]  # Assume horizontal
            elif num_images == 3:
                grid_layout = [image_types]  # Assume horizontal
            elif num_images == 4:
                # Could be 2x2 or 4x1, assume 2x2
                grid_layout = [image_types[:2], image_types[2:]]
            elif num_images == 6:
                # Could be 3x2 or 2x3, assume 3x2
                grid_layout = [image_types[:3], image_types[3:]]
            else:
                # Default to single row
                grid_layout = [image_types]

        height = len(grid_layout)
        width = max(len(row) for row in grid_layout) if grid_layout else 0

        logger.debug(f"Final grid layout: {grid_layout}, width: {width}, height: {height}")

        return grid_layout, width, height
    
    @staticmethod
    def extract_positional_synergies(effect_text: str) -> List[str]:
        """Extract positional synergy information from effect text."""
        synergies = []

        # Look for star (★) references - also check for "The ★" pattern
        star_patterns = [
            r'[★⭐]\s*([^.!?]*)',
            r'The\s+[★⭐]\s*([^.!?]*)',
            r'each\s+[★⭐]\s*([^.!?]*)'
        ]
        for pattern in star_patterns:
            star_matches = re.findall(pattern, effect_text, re.IGNORECASE)
            for match in star_matches:
                synergies.append(f"Star synergy: {match.strip()}")

        # Look for diamond (♦) references - also check for "The ♦" pattern
        diamond_patterns = [
            r'[♦◆]\s*([^.!?]*)',
            r'The\s+[♦◆]\s*([^.!?]*)',
            r'each\s+[♦◆]\s*([^.!?]*)'
        ]
        for pattern in diamond_patterns:
            diamond_matches = re.findall(pattern, effect_text, re.IGNORECASE)
            for match in diamond_matches:
                synergies.append(f"Diamond synergy: {match.strip()}")

        # Look for "for each" patterns (common in positional synergies)
        each_matches = re.findall(r'for each[^.!?]*', effect_text, re.IGNORECASE)
        for match in each_matches:
            synergies.append(f"For each synergy: {match.strip()}")

        # Look for adjacency effects
        if 'adjacent' in effect_text.lower():
            adjacency_matches = re.findall(r'adjacent[^.!?]*', effect_text, re.IGNORECASE)
            for match in adjacency_matches:
                synergies.append(f"Adjacency: {match.strip()}")

        # Look for "items inside" effects (for bags)
        if 'items inside' in effect_text.lower():
            inside_matches = re.findall(r'items inside[^.!?]*', effect_text, re.IGNORECASE)
            for match in inside_matches:
                synergies.append(f"Items inside: {match.strip()}")

        # Look for "different type" synergies (like food synergies)
        if 'different type' in effect_text.lower():
            type_matches = re.findall(r'[^.!?]*different type[^.!?]*', effect_text, re.IGNORECASE)
            for match in type_matches:
                synergies.append(f"Type synergy: {match.strip()}")

        return synergies
    
    @staticmethod
    def extract_weapon_stats(soup) -> Optional[WeaponStats]:
        """Extract weapon statistics from the page."""
        stats = WeaponStats()
        
        # Look for damage information
        damage_elem = soup.find('th', string='Damage')
        if damage_elem:
            damage_cell = damage_elem.find_next_sibling('td')
            if damage_cell:
                stats.damage = EnhancedDataExtractor.normalize_text(damage_cell.get_text())
        
        # Look for stamina information
        stamina_elem = soup.find('th', string='Stamina')
        if stamina_elem:
            stamina_cell = stamina_elem.find_next_sibling('td')
            if stamina_cell:
                stats.stamina = EnhancedDataExtractor.normalize_text(stamina_cell.get_text())
        
        # Look for accuracy information
        accuracy_elem = soup.find('th', string='Accuracy')
        if accuracy_elem:
            accuracy_cell = accuracy_elem.find_next_sibling('td')
            if accuracy_cell:
                stats.accuracy = EnhancedDataExtractor.normalize_text(accuracy_cell.get_text())
        
        # Look for cooldown information
        cooldown_elem = soup.find('th', string='Cooldown')
        if cooldown_elem:
            cooldown_cell = cooldown_elem.find_next_sibling('td')
            if cooldown_cell:
                stats.cooldown = EnhancedDataExtractor.normalize_text(cooldown_cell.get_text())
        
        # Look for sockets information
        sockets_elem = soup.find('th', string='Sockets')
        if sockets_elem:
            sockets_cell = sockets_elem.find_next_sibling('td')
            if sockets_cell:
                stats.sockets = EnhancedDataExtractor.normalize_text(sockets_cell.get_text())
        
        # Return stats only if we found at least one weapon stat
        if any([stats.damage, stats.stamina, stats.accuracy, stats.cooldown, stats.sockets]):
            return stats
        return None
    
    @staticmethod
    def extract_bag_properties(effect_text: str, item_type: str) -> Optional[BagProperties]:
        """Extract bag-specific properties."""
        if item_type.lower() != 'bag':
            return None
        
        props = BagProperties()
        
        # Extract slots added
        slots_match = re.search(r'add (\d+) backpack slots?', effect_text, re.IGNORECASE)
        if slots_match:
            props.slots_added = int(slots_match.group(1))
        
        # Extract special effects
        if 'trigger' in effect_text.lower() and 'faster' in effect_text.lower():
            props.special_effects.append("Increased trigger speed")
        
        if 'stamina' in effect_text.lower():
            props.special_effects.append("Stamina bonus")
        
        if 'heal' in effect_text.lower():
            props.special_effects.append("Healing bonus")
        
        if 'armor' in effect_text.lower() or 'damage' in effect_text.lower():
            props.special_effects.append("Combat bonus")
        
        return props
    
    @staticmethod
    def extract_timing_effects(effect_text: str) -> List[str]:
        """Extract time-based mechanics from effect text."""
        timing_effects = []
        
        # Look for battle timing
        battle_matches = re.findall(r'(\d+)\s*battles?', effect_text, re.IGNORECASE)
        for match in battle_matches:
            timing_effects.append(f"Battles: {match}")
        
        # Look for second timing
        second_matches = re.findall(r'(\d+(?:\.\d+)?)\s*s(?:econds?)?', effect_text, re.IGNORECASE)
        for match in second_matches:
            timing_effects.append(f"Seconds: {match}")
        
        # Look for start of battle effects
        if 'start of battle' in effect_text.lower():
            timing_effects.append("Start of battle")
        
        # Look for hatching
        if 'hatch' in effect_text.lower():
            timing_effects.append("Hatching mechanic")
        
        return timing_effects

def fetch_page(url: str) -> Optional[str]:
    """Fetch a web page with error handling."""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        return response.text
    except Exception as e:
        logger.error(f"Error fetching {url}: {e}")
        return None

def parse_enhanced_item_from_page(url: str) -> Optional[EnhancedItem]:
    """Parse a single item page to extract enhanced information."""
    html = fetch_page(url)
    if not html:
        return None
    
    soup = BeautifulSoup(html, "html.parser")
    
    # Extract basic information
    name_elem = soup.find('h1', {'class': 'page-header__title'})
    if not name_elem:
        name_elem = soup.find('h1')
    name = EnhancedDataExtractor.normalize_text(name_elem.get_text()) if name_elem else ""
    
    if not name:
        return None
    
    # Extract other basic fields
    rarity_elem = soup.find('th', string='Rarity')
    rarity = ""
    if rarity_elem:
        rarity_cell = rarity_elem.find_next_sibling('td')
        if rarity_cell:
            rarity = EnhancedDataExtractor.normalize_text(rarity_cell.get_text())
    
    type_elem = soup.find('th', string='Type')
    item_type = ""
    if type_elem:
        type_cell = type_elem.find_next_sibling('td')
        if type_cell:
            item_type = EnhancedDataExtractor.normalize_text(type_cell.get_text())
    
    cost_elem = soup.find('th', string='Cost')
    cost = "0"
    if cost_elem:
        cost_cell = cost_elem.find_next_sibling('td')
        if cost_cell:
            cost = EnhancedDataExtractor.extract_cost(cost_cell.get_text())
    
    effect_elem = soup.find('th', string='Effect')
    effect = ""
    if effect_elem:
        effect_cell = effect_elem.find_next_sibling('td')
        if effect_cell:
            effect = EnhancedDataExtractor.normalize_text(effect_cell.get_text())
    
    # Extract image URL
    image_url = ""
    img_elem = soup.find('img', {'alt': re.compile(name, re.IGNORECASE)})
    if img_elem and img_elem.get('src'):
        image_url = urljoin(url, img_elem['src'])
    
    # Extract enhanced information
    grid_layout, width, height = EnhancedDataExtractor.parse_grid_layout(soup)
    positional_synergies = EnhancedDataExtractor.extract_positional_synergies(effect)
    weapon_stats = EnhancedDataExtractor.extract_weapon_stats(soup)
    bag_properties = EnhancedDataExtractor.extract_bag_properties(effect, item_type)
    timing_effects = EnhancedDataExtractor.extract_timing_effects(effect)
    
    return EnhancedItem(
        name=name,
        type=item_type,
        rarity=rarity,
        cost=cost,
        effect=effect,
        image_url=image_url,
        source_url=url,
        grid_layout=grid_layout,
        grid_width=width,
        grid_height=height,
        positional_synergies=positional_synergies,
        weapon_stats=weapon_stats,
        bag_properties=bag_properties,
        timing_effects=timing_effects
    )

def crawl_all_items_with_positional_data():
    """Crawl all items from the comprehensive dataset and add positional data."""
    # Load the existing comprehensive dataset
    try:
        with open("comprehensive_backpack_battles_data.json", "r", encoding="utf-8") as f:
            existing_data = json.load(f)
    except FileNotFoundError:
        logger.error("comprehensive_backpack_battles_data.json not found. Please run the basic crawler first.")
        return

    enhanced_items = []
    base_url = "https://backpackbattles.wiki.gg/wiki/"

    # Process each item from the existing dataset
    for i, item in enumerate(existing_data.get("items", [])):
        item_name = item["name"]
        # Convert item name to URL format (replace spaces with underscores)
        url_name = item_name.replace(" ", "_").replace(":", "%3A")
        url = base_url + url_name

        logger.info(f"Processing {i+1}/{len(existing_data['items'])}: {item_name}")

        enhanced_item = parse_enhanced_item_from_page(url)
        if enhanced_item:
            enhanced_items.append(enhanced_item)
            logger.info(f"Successfully enhanced: {item_name}")
        else:
            # If enhanced parsing fails, create a basic enhanced item from existing data
            enhanced_item = EnhancedItem(
                name=item["name"],
                type=item["type"],
                rarity=item["rarity"],
                cost=item["cost"],
                effect=item["effect"],
                image_url=item.get("image_url", ""),
                source_url=item.get("source_url", "")
            )
            enhanced_items.append(enhanced_item)
            logger.warning(f"Used basic data for: {item_name}")

        # Be respectful to the server
        time.sleep(1)

        # Process in batches to avoid overwhelming the server
        if (i + 1) % 50 == 0:
            logger.info(f"Processed {i+1} items, taking a longer break...")
            time.sleep(5)

    # Save enhanced results
    output = {
        "enhanced_items": [asdict(item) for item in enhanced_items],
        "recipes": existing_data.get("recipes", []),
        "metadata": {
            "total_items": len(enhanced_items),
            "total_recipes": len(existing_data.get("recipes", [])),
            "crawl_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "enhancement_note": "Items enhanced with positional, weapon stats, bag properties, and timing data"
        }
    }

    with open("complete_enhanced_backpack_battles_data.json", "w", encoding="utf-8") as f:
        json.dump(output, f, indent=2, ensure_ascii=False)

    logger.info(f"Enhanced crawling completed. Enhanced {len(enhanced_items)} items total.")
    logger.info("Results saved to complete_enhanced_backpack_battles_data.json")

def main():
    """Test the enhanced crawler with a few sample items."""
    test_urls = [
        "https://backpackbattles.wiki.gg/wiki/Banana",
        "https://backpackbattles.wiki.gg/wiki/Crossblades",
        "https://backpackbattles.wiki.gg/wiki/Fanny_Pack",
        "https://backpackbattles.wiki.gg/wiki/Stamina_Sack"
    ]

    enhanced_items = []

    for url in test_urls:
        logger.info(f"Processing: {url}")
        item = parse_enhanced_item_from_page(url)
        if item:
            enhanced_items.append(item)
            logger.info(f"Successfully parsed: {item.name}")
        time.sleep(2)  # Be respectful to the server

    # Save results
    output = {
        "enhanced_items": [asdict(item) for item in enhanced_items],
        "total_items": len(enhanced_items),
        "crawl_timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
    }

    with open("enhanced_positional_data.json", "w", encoding="utf-8") as f:
        json.dump(output, f, indent=2, ensure_ascii=False)

    logger.info(f"Enhanced crawling completed. Found {len(enhanced_items)} items with positional data.")
    logger.info("Results saved to enhanced_positional_data.json")

    # Ask user if they want to crawl all items
    print("\nWould you like to crawl all items with enhanced positional data? (y/n)")
    response = input().strip().lower()
    if response == 'y':
        crawl_all_items_with_positional_data()

if __name__ == "__main__":
    main()
