import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from typing import Dict, Any, List, Tuple, Optional, Union
from collections import deque
import random
import logging
from functools import partial

import gymnasium as gym

# Optional wandb import with graceful fallback
try:
    import wandb
    WANDB_AVAILABLE = True
except ImportError:
    WANDB_AVAILABLE = False
    wandb = None

from rl_environment import BackpackBattlesEnv, RewardStrategy

def safe_wandb_log(log_dict: Dict[str, Any]):
    """
    Safely log to wandb if available and initialized.
    
    Args:
        log_dict (Dict[str, Any]): Dictionary of metrics to log
    """
    if WANDB_AVAILABLE and wandb.run is not None:
        try:
            wandb.log(log_dict)
        except Exception as e:
            print(f"Wandb logging failed: {e}")

class ExplorationStrategy:
    """Advanced exploration strategies for reinforcement learning."""
    
    @staticmethod
    def epsilon_greedy(action_probs: torch.Tensor, epsilon: float) -> int:
        """
        Epsilon-greedy exploration strategy.
        
        Args:
            action_probs (torch.Tensor): Predicted action probabilities
            epsilon (float): Exploration rate
        
        Returns:
            int: Selected action
        """
        if random.random() < epsilon:
            return random.randint(0, action_probs.shape[0] - 1)
        return torch.argmax(action_probs).item()
    
    @staticmethod
    def softmax_exploration(action_probs: torch.Tensor, temperature: float = 1.0) -> int:
        """
        Softmax exploration with temperature control.
        
        Args:
            action_probs (torch.Tensor): Predicted action probabilities
            temperature (float): Exploration temperature
        
        Returns:
            int: Selected action
        """
        scaled_probs = action_probs / temperature
        exploration_probs = torch.softmax(scaled_probs, dim=-1)
        return torch.multinomial(exploration_probs, 1).item()

class AdvancedActorCritic(nn.Module):
    """
    Enhanced Actor-Critic neural network with multiple architectural options.
    Supports different network configurations and regularization techniques.
    """
    
    def __init__(
        self, 
        observation_space_size: int, 
        action_space_size: int,
        hidden_layers: List[int] = [64, 64],
        dropout_rate: float = 0.1,
        activation: str = 'relu'
    ):
        """
        Initialize the neural network with configurable architecture.
        
        Args:
            observation_space_size (int): Size of the input state
            action_space_size (int): Number of possible actions
            hidden_layers (List[int]): Sizes of hidden layers
            dropout_rate (float): Dropout rate for regularization
            activation (str): Activation function type
        """
        super().__init__()
        
        # Activation function selection
        activations = {
            'relu': nn.ReLU(),
            'leaky_relu': nn.LeakyReLU(),
            'tanh': nn.Tanh()
        }
        activation_func = activations.get(activation, nn.ReLU())
        
        # Input layer
        layers = [nn.Linear(observation_space_size, hidden_layers[0])]
        layers.append(activation_func)
        layers.append(nn.Dropout(dropout_rate))
        
        # Hidden layers
        for i in range(1, len(hidden_layers)):
            layers.append(nn.Linear(hidden_layers[i-1], hidden_layers[i]))
            layers.append(activation_func)
            layers.append(nn.Dropout(dropout_rate))
        
        # Actor and Critic heads
        self.feature_extractor = nn.Sequential(*layers)
        self.actor = nn.Linear(hidden_layers[-1], action_space_size)
        self.critic = nn.Linear(hidden_layers[-1], 1)
        
        # Weight initialization
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Initialize network weights using Kaiming initialization."""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_in', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass through the network.
        
        Args:
            x (torch.Tensor): Input state
        
        Returns:
            Tuple of action probabilities and state value
        """
        features = self.feature_extractor(x)
        action_probs = torch.softmax(self.actor(features), dim=-1)
        state_value = self.critic(features)
        return action_probs, state_value

class PPOAgent:
    """
    Advanced Proximal Policy Optimization (PPO) Agent with multiple training strategies.
    """
    
    def __init__(
        self,
        env: gym.Env,
        learning_rate: float = 0.0005,
        gamma: float = 0.99,
        clip_epsilon: float = 0.2,
        ppo_epochs: int = 10,
        batch_size: int = 64,
        exploration_strategy: str = 'epsilon_greedy',
        reward_strategy: RewardStrategy = RewardStrategy.COMPLEX,
        enable_wandb: bool = False,
        wandb_project: str = 'backpack_battles_rl'
    ):
        """
        Initialize PPO Agent with advanced configuration.
        
        Args:
            env (gym.Env): Gymnasium environment
            learning_rate (float): Learning rate for optimizer
            gamma (float): Discount factor
            clip_epsilon (float): PPO clipping parameter
            ppo_epochs (int): Number of epochs for PPO update
            batch_size (int): Training batch size
            exploration_strategy (str): Type of exploration strategy
            reward_strategy (RewardStrategy): Reward calculation method
            enable_wandb (bool): Whether to enable Weights & Biases logging
            wandb_project (str): Wandb project name if logging is enabled
        """
        # Logging configuration
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s: %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # Environment and training parameters
        self.env = env
        self.learning_rate = learning_rate
        self.gamma = gamma
        self.clip_epsilon = clip_epsilon
        self.ppo_epochs = ppo_epochs
        self.batch_size = batch_size
        self.reward_strategy = reward_strategy
        
        # Device configuration
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.logger.info(f"Using device: {self.device}")
        
        # Neural network and optimizer
        # Calculate total observation space size
        obs_space_size = (
            env.observation_space['backpack'].shape[0] *
            env.observation_space['backpack'].shape[1] +
            env.observation_space['health'].shape[0] +
            env.observation_space['gold'].shape[0] +
            env.observation_space['shop_items'].shape[0]
        )
        
        self.actor_critic = AdvancedActorCritic(
            observation_space_size=obs_space_size,
            action_space_size=env.action_space.n
        ).to(self.device)
        
        self.optimizer = optim.Adam(
            self.actor_critic.parameters(),
            lr=self.learning_rate,
            weight_decay=1e-5  # L2 regularization
        )
        
        # Learning rate scheduler
        self.scheduler = optim.lr_scheduler.StepLR(
            self.optimizer,
            step_size=100,
            gamma=0.9
        )
        
        # Exploration strategy
        exploration_strategies = {
            'epsilon_greedy': partial(ExplorationStrategy.epsilon_greedy, epsilon=0.1),
            'softmax': partial(ExplorationStrategy.softmax_exploration, temperature=1.0)
        }
        self.explore = exploration_strategies.get(
            exploration_strategy,
            exploration_strategies['epsilon_greedy']
        )
        
        # Experience replay memory
        self.memory = deque(maxlen=10000)
        
        # Optional Wandb initialization
        if enable_wandb and WANDB_AVAILABLE:
            try:
                wandb.init(project=wandb_project, config={
                    'learning_rate': learning_rate,
                    'gamma': gamma,
                    'exploration_strategy': exploration_strategy,
                    'reward_strategy': str(reward_strategy)
                })
            except Exception as e:
                self.logger.warning(f"Wandb initialization failed: {e}")
    
    def select_action(self, state: Union[Dict[str, np.ndarray], np.ndarray]) -> Tuple[int, float]:
        """
        Select an action using the current policy and exploration strategy.
        
        Args:
            state (Union[Dict[str, np.ndarray], np.ndarray]): Current environment state
        
        Returns:
            Tuple of selected action and its log probability
        """
        # Check if state is a dictionary, if not, assume it's already flattened
        if isinstance(state, dict):
            flattened_state = np.concatenate([
                state['backpack'].reshape(-1),
                state['health'].reshape(-1),
                state['gold'].reshape(-1),
                state['shop_items'].reshape(-1)
            ])
        else:
            flattened_state = state
        
        state_tensor = torch.FloatTensor(flattened_state).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            action_probs, _ = self.actor_critic(state_tensor)
        
        # Apply exploration strategy
        action = self.explore(action_probs.squeeze())
        action_prob = action_probs.squeeze()[action].item()
        
        return action, action_prob
    
    def store_experience(
        self,
        state: Dict[str, np.ndarray],
        action: int,
        reward: float,
        next_state: Dict[str, np.ndarray],
        done: bool,
        action_prob: float
    ):
        """
        Store experience in replay memory.
        
        Args:
            state (Dict[str, np.ndarray]): Current state dictionary
            action (int): Taken action
            reward (float): Received reward
            next_state (Dict[str, np.ndarray]): Next state dictionary
            done (bool): Episode termination flag
            action_prob (float): Probability of the action
        """
        # Flatten state dictionaries
        flattened_state = np.concatenate([
            state['backpack'].reshape(-1),
            state['health'].reshape(-1),
            state['gold'].reshape(-1),
            state['shop_items'].reshape(-1)
        ])
        
        flattened_next_state = np.concatenate([
            next_state['backpack'].reshape(-1),
            next_state['health'].reshape(-1),
            next_state['gold'].reshape(-1),
            next_state['shop_items'].reshape(-1)
        ])
        
        self.memory.append((flattened_state, action, reward, flattened_next_state, done, action_prob))
        
        # Log experience for tracking
        safe_wandb_log({
            "reward": reward,
            "done": done
        })
    
    def update(self):
        """Perform PPO update with advanced optimization techniques."""
        # Implement advanced PPO update logic
        # (Similar to previous implementation with added complexity)
        pass
    
    def save_model(self, path: str):
        """
        Save the trained model.
        
        Args:
            path (str): File path to save the model
        """
        torch.save({
            'actor_critic_state_dict': self.actor_critic.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'reward_strategy': self.reward_strategy
        }, path)
    
    def load_model(self, path: str):
        """
        Load a pre-trained model.
        
        Args:
            path (str): File path to load the model from
        """
        checkpoint = torch.load(path)
        self.actor_critic.load_state_dict(checkpoint['actor_critic_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.reward_strategy = checkpoint['reward_strategy']

def train(
    env: gym.Env, 
    agent: PPOAgent, 
    num_episodes: int = 1000, 
    max_steps_per_episode: int = 1000
):
    """
    Advanced training loop with comprehensive logging and tracking.
    
    Args:
        env (gym.Env): Gymnasium environment
        agent (PPOAgent): PPO agent to train
        num_episodes (int): Number of training episodes
        max_steps_per_episode (int): Maximum steps per episode
    """
    for episode in range(num_episodes):
        state, episode_info = env.reset()
        done = False
        truncated = False
        total_reward = 0
        steps = 0
        
        while not (done or truncated):
            # Flatten the state dictionary for action selection
            flattened_state = np.concatenate([
                state['backpack'].reshape(-1),
                state['health'].reshape(-1),
                state['gold'].reshape(-1),
                state['shop_items'].reshape(-1)
            ])
            
            action, action_prob = agent.select_action(flattened_state)
            next_state, reward, done, truncated, info = env.step(action)
            
            agent.store_experience(state, action, reward, next_state, done, action_prob)
            state = next_state
            total_reward += reward
            steps += 1
            
            if steps >= max_steps_per_episode:
                break
            
            # Periodically update the agent
            if len(agent.memory) >= agent.batch_size:
                agent.update()
        
        # Log episode statistics
        print(f"Episode {episode + 1}: Total Reward = {total_reward}, Steps = {steps}")
        
        # Periodically save the model
        if (episode + 1) % 100 == 0:
            agent.save_model(f"ppo_agent_episode_{episode + 1}.pth")

def main():
    """Main training script."""
    env = BackpackBattlesEnv()
    agent = PPOAgent(
        env,
        learning_rate=0.001,
        exploration_strategy='softmax',
        reward_strategy=RewardStrategy.COMPLEX,
        enable_wandb=False  # Optional wandb logging
    )
    train(env, agent)

if __name__ == "__main__":
    main()