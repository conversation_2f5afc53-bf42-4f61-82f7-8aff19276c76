import requests
from bs4 import BeautifulSoup
import json
import time
import logging
import random
from urllib.parse import urljoin, urlparse
from typing import Dict, List, Optional, Any, Set, Tuple
import re
import hashlib
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
import functools
import yaml
from dataclasses import dataclass, asdict
from datetime import datetime

# Data Models
@dataclass
class Item:
    name: str
    type: str
    rarity: str
    cost: str
    effect: str
    image_url: str = ""
    class_restriction: str = ""
    tags: List[str] = None
    source_url: str = ""

    def __post_init__(self):
        if self.tags is None:
            self.tags = []

@dataclass
class Recipe:
    result: str
    ingredients: List[str]
    catalyst: str = ""
    class_restriction: str = ""
    source_url: str = ""

@dataclass
class Character:
    name: str
    description: str
    starting_items: List[str]
    class_items: List[str]
    source_url: str = ""

# Enhanced Configuration Management
class CrawlerConfig:
    def __init__(self, config_path: str = 'crawler_config.yaml'):
        self.config = self.load_config(config_path)

    def load_config(self, config_path: str) -> Dict[str, Any]:
        try:
            with open(config_path, 'r') as f:
                return yaml.safe_load(f) or {}
        except FileNotFoundError:
            return self._default_config()

    def _default_config(self) -> Dict[str, Any]:
        return {
            'base_urls': {
                'items': "https://backpackbattles.wiki.gg/wiki/Items",
                'neutral_items': "https://backpackbattles.wiki.gg/wiki/Neutral_items",
                'ranger_items': "https://backpackbattles.wiki.gg/wiki/Ranger_items",
                'reaper_items': "https://backpackbattles.wiki.gg/wiki/Reaper_items",
                'berserker_items': "https://backpackbattles.wiki.gg/wiki/Berserker_items",
                'pyromancer_items': "https://backpackbattles.wiki.gg/wiki/Pyromancer_items",
                'recipes': "https://backpackbattles.wiki.gg/wiki/Recipe",
                'characters': "https://backpackbattles.wiki.gg/wiki/Characters",
                'game_mechanics': "https://backpackbattles.wiki.gg/wiki/Game_Mechanics"
            },
            'item_types': [
                'Accessory', 'Armor', 'Bag', 'Food', 'Gemstone', 'Gloves',
                'Helmet', 'Pet', 'Playing_Card', 'Potion', 'Shield', 'Shoes',
                'Weapon', 'Skill', 'Spell_Scroll'
            ],
            'rate_limit': {
                'min_delay': 1,
                'max_delay': 3
            },
            'retry_config': {
                'max_retries': 3,
                'backoff_factor': 2
            },
            'cache_dir': './crawler_cache'
        }

    def get(self, key: str, default: Any = None) -> Any:
        return self.config.get(key, default)

# Enhanced Logging Setup
def setup_logging(log_file: str = 'backpack_battles_crawler.log') -> logging.Logger:
    logger = logging.getLogger('BackpackBattlesCrawler')
    logger.setLevel(logging.INFO)

    # Clear existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # File Handler
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.INFO)
    file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_formatter)

    # Console Handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter('%(levelname)s: %(message)s')
    console_handler.setFormatter(console_formatter)

    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

# Caching Mechanism
class WebCache:
    def __init__(self, cache_dir: str):
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
    
    def _get_cache_path(self, url: str) -> str:
        url_hash = hashlib.md5(url.encode()).hexdigest()
        return os.path.join(self.cache_dir, f"{url_hash}.html")
    
    def get(self, url: str) -> Optional[str]:
        cache_path = self._get_cache_path(url)
        if os.path.exists(cache_path):
            with open(cache_path, 'r', encoding='utf-8') as f:
                return f.read()
        return None
    
    def set(self, url: str, content: str):
        cache_path = self._get_cache_path(url)
        with open(cache_path, 'w', encoding='utf-8') as f:
            f.write(content)

# Robust Request Handler with Retry and Rate Limiting
class RobustRequestHandler:
    def __init__(self, config: CrawlerConfig, logger: logging.Logger):
        self.config = config
        self.logger = logger
        self.cache = WebCache(config.get('cache_dir', './crawler_cache'))
    
    def _exponential_backoff(self, attempt: int) -> float:
        return min(
            self.config.get('retry_config', {}).get('backoff_factor', 2) ** attempt,
            30  # Maximum wait of 30 seconds
        )
    
    @functools.lru_cache(maxsize=128)
    def fetch_page(self, url: str) -> Optional[str]:
        cached_content = self.cache.get(url)
        if cached_content:
            self.logger.info(f"Cache hit for {url}")
            return cached_content
        
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8"
        }
        
        max_retries = self.config.get('retry_config', {}).get('max_retries', 3)
        
        for attempt in range(max_retries):
            try:
                # Random delay between requests
                time.sleep(random.uniform(
                    self.config.get('rate_limit', {}).get('min_delay', 1),
                    self.config.get('rate_limit', {}).get('max_delay', 3)
                ))
                
                with requests.Session() as session:
                    session.headers.update(headers)
                    response = session.get(url, timeout=10)
                    response.raise_for_status()
                    
                    self.cache.set(url, response.text)
                    return response.text
            
            except requests.RequestException as e:
                wait_time = self._exponential_backoff(attempt)
                self.logger.warning(f"Request failed for {url}: {e}. Retry in {wait_time} seconds.")
                time.sleep(wait_time)
        
        self.logger.error(f"Failed to fetch {url} after {max_retries} attempts")
        return None

# Enhanced Data Extraction
class DataExtractor:
    @staticmethod
    def normalize_text(text: str) -> str:
        """Normalize text by removing extra whitespaces and special characters."""
        if not text:
            return ""
        # Remove wiki markup and extra whitespace
        text = re.sub(r'\[\[.*?\]\]', '', text)  # Remove wiki links
        text = re.sub(r'\{\{.*?\}\}', '', text)  # Remove templates
        text = re.sub(r'<.*?>', '', text)        # Remove HTML tags
        text = re.sub(r'\s+', ' ', text)         # Normalize whitespace
        return text.strip()

    @staticmethod
    def extract_cost(text: str) -> str:
        """Extract cost from text."""
        cost_match = re.search(r'(\d+)\s*(?:Gold|![Gg]old)', text)
        return cost_match.group(1) if cost_match else "0"

    @staticmethod
    def extract_tags_from_icons(row) -> List[str]:
        """Extract tags from icon images in a table row."""
        tags = []
        icons = row.find_all('img')
        for icon in icons:
            alt_text = icon.get('alt', '')
            if alt_text and alt_text not in ['Gold', 'Star']:
                tags.append(alt_text)
        return tags

    @staticmethod
    def parse_detailed_item_table(html: str, base_url: str) -> List[Item]:
        """Parse the detailed item table from wiki pages."""
        soup = BeautifulSoup(html, "html.parser")
        items = []

        # First, try to find the structured item table with headers
        tables = soup.find_all("table")

        for table in tables:
            headers = table.find_all("th")
            if not headers:
                continue

            header_texts = [th.get_text().strip() for th in headers]

            # Check if this looks like an item table with proper headers
            if any(header in header_texts for header in ['Name', 'Effect', 'Item Type', 'Rarity', 'Cost']):
                rows = table.find_all("tr")[1:]  # Skip header row

                for row in rows:
                    cols = row.find_all(["td", "th"])
                    if len(cols) >= 4:  # Minimum columns for item data
                        try:
                            # Extract item data
                            name_cell = cols[0] if cols else None
                            effect_cell = cols[1] if len(cols) > 1 else None
                            type_cell = cols[2] if len(cols) > 2 else None
                            rarity_cell = cols[3] if len(cols) > 3 else None
                            cost_cell = cols[4] if len(cols) > 4 else None

                            # Get name and image
                            name = ""
                            image_url = ""
                            if name_cell:
                                name_link = name_cell.find("a")
                                if name_link:
                                    name = DataExtractor.normalize_text(name_link.get_text())
                                else:
                                    name = DataExtractor.normalize_text(name_cell.get_text())

                                # Get image URL
                                img = name_cell.find("img")
                                if img and img.get('src'):
                                    image_url = urljoin(base_url, img['src'])

                            if not name:
                                continue

                            # Extract other fields
                            effect = DataExtractor.normalize_text(effect_cell.get_text()) if effect_cell else ""
                            item_type = DataExtractor.normalize_text(type_cell.get_text()) if type_cell else ""
                            rarity = DataExtractor.normalize_text(rarity_cell.get_text()) if rarity_cell else ""
                            cost = DataExtractor.extract_cost(cost_cell.get_text()) if cost_cell else "0"

                            # Extract tags from icons
                            tags = DataExtractor.extract_tags_from_icons(row)

                            item = Item(
                                name=name,
                                type=item_type,
                                rarity=rarity,
                                cost=cost,
                                effect=effect,
                                image_url=image_url,
                                tags=tags,
                                source_url=base_url
                            )
                            items.append(item)

                        except Exception as e:
                            logging.warning(f"Error parsing item row: {e}")
                            continue

        # If no structured table found, try to parse the custom wiki layout
        if not items:
            items = DataExtractor.parse_wiki_item_layout(html, base_url)

        return items

    @staticmethod
    def parse_wiki_item_layout(html: str, base_url: str) -> List[Item]:
        """Parse the custom wiki item layout used on item pages."""
        soup = BeautifulSoup(html, "html.parser")
        items = []

        # Debug: Check what we're working with
        logging.info(f"Parsing wiki layout for {base_url}")

        # Look for the pattern: header row with "Name", "Effect", "Item Type", "Rarity", "Cost"
        # followed by item rows

        # Find all table rows
        all_rows = soup.find_all("tr")
        logging.info(f"Found {len(all_rows)} table rows")

        # Look for header indicators
        header_found = False
        for i, row in enumerate(all_rows):
            row_text = row.get_text().strip()
            if "Name" in row_text and "Effect" in row_text and "Item Type" in row_text and "Rarity" in row_text and "Cost" in row_text:
                header_found = True
                logging.info(f"Found header row at index {i}")
                # Process subsequent rows as item data
                processed_count = 0
                for item_row in all_rows[i+1:]:
                    try:
                        item = DataExtractor.parse_single_item_row(item_row, base_url)
                        if item:
                            items.append(item)
                            processed_count += 1
                    except Exception as e:
                        logging.warning(f"Error parsing item row: {e}")
                        continue
                logging.info(f"Processed {processed_count} item rows")
                break

        if not header_found:
            logging.warning(f"No header row found in {base_url}")
            # Try alternative parsing - look for any rows with item-like structure
            for row in all_rows:
                cols = row.find_all(["td", "th"])
                if len(cols) >= 5:
                    # Check if this looks like an item row (has image, name link, etc.)
                    first_col = cols[0]
                    if first_col.find("img") and first_col.find("a"):
                        try:
                            item = DataExtractor.parse_single_item_row(row, base_url)
                            if item:
                                items.append(item)
                        except Exception as e:
                            continue

        logging.info(f"Total items extracted from {base_url}: {len(items)}")
        return items

    @staticmethod
    def parse_single_item_row(row, base_url: str) -> Optional[Item]:
        """Parse a single item row from the wiki layout."""
        cols = row.find_all(["td", "th"])

        # Skip rows that don't have enough columns or are navigation/footer rows
        if len(cols) < 5:
            return None

        # Skip rows that contain navigation elements
        row_text = row.get_text().strip().lower()
        if any(skip_text in row_text for skip_text in ['category:', 'retrieved from', 'cookies help', 'terms of service']):
            return None

        try:
            # Column structure: [Image+Name], [Effect], [Item Type], [Rarity], [Cost]
            name_cell = cols[0]
            effect_cell = cols[1]
            type_cell = cols[2]
            rarity_cell = cols[3]
            cost_cell = cols[4]

            # Extract name
            name = ""
            image_url = ""

            # Get name from link
            name_link = name_cell.find("a")
            if name_link:
                name = DataExtractor.normalize_text(name_link.get_text())

            # Get image URL
            img = name_cell.find("img")
            if img and img.get('src'):
                image_url = urljoin(base_url, img['src'])

            if not name:
                return None

            # Extract other fields
            effect = DataExtractor.normalize_text(effect_cell.get_text())
            item_type = DataExtractor.normalize_text(type_cell.get_text())
            rarity = DataExtractor.normalize_text(rarity_cell.get_text())
            cost = DataExtractor.extract_cost(cost_cell.get_text())

            # Extract tags from icons in the type cell (where element icons are shown)
            tags = DataExtractor.extract_tags_from_icons(type_cell)

            return Item(
                name=name,
                type=item_type,
                rarity=rarity,
                cost=cost,
                effect=effect,
                image_url=image_url,
                tags=tags,
                source_url=base_url
            )

        except Exception as e:
            logging.warning(f"Error parsing single item row: {e}")
            return None

    @staticmethod
    def parse_recipes(html: str, base_url: str) -> List[Recipe]:
        """Parse recipes from the recipe page."""
        soup = BeautifulSoup(html, "html.parser")
        recipes = []

        # Find recipe tables
        tables = soup.find_all("table")

        for table in tables:
            headers = table.find_all("th")
            if not headers:
                continue

            header_texts = [th.get_text().strip() for th in headers]

            # Check if this is a recipe table
            if any(header in header_texts for header in ['Class', 'Name', 'Type', 'Ingredients', 'Catalyst']):
                rows = table.find_all("tr")[1:]  # Skip header row

                for row in rows:
                    cols = row.find_all(["td", "th"])
                    if len(cols) >= 3:  # Minimum columns for recipe data
                        try:
                            class_restriction = ""
                            result_name = ""
                            ingredients = []
                            catalyst = ""

                            # Parse based on column structure
                            if len(cols) >= 5:  # Full recipe table
                                class_restriction = DataExtractor.normalize_text(cols[0].get_text())
                                result_name = DataExtractor.normalize_text(cols[1].get_text())
                                # Skip type column (cols[2])
                                ingredients_text = cols[3].get_text()
                                catalyst = DataExtractor.normalize_text(cols[4].get_text())
                            elif len(cols) >= 3:  # Simplified recipe table
                                result_name = DataExtractor.normalize_text(cols[0].get_text())
                                ingredients_text = cols[1].get_text()
                                if len(cols) > 2:
                                    catalyst = DataExtractor.normalize_text(cols[2].get_text())

                            if not result_name:
                                continue

                            # Parse ingredients
                            if ingredients_text:
                                # Split by common separators and clean up
                                ingredient_parts = re.split(r'[+,&]|\s+\+\s+', ingredients_text)
                                ingredients = [DataExtractor.normalize_text(ing) for ing in ingredient_parts if ing.strip()]

                            recipe = Recipe(
                                result=result_name,
                                ingredients=ingredients,
                                catalyst=catalyst,
                                class_restriction=class_restriction,
                                source_url=base_url
                            )
                            recipes.append(recipe)

                        except Exception as e:
                            logging.warning(f"Error parsing recipe row: {e}")
                            continue

        return recipes

# Enhanced Main Crawler
class BackpackBattlesCrawler:
    def __init__(self, config_path: str = 'crawler_config.yaml'):
        self.config = CrawlerConfig(config_path)
        self.logger = setup_logging()
        self.request_handler = RobustRequestHandler(self.config, self.logger)
        self.output = {
            "items": [],
            "recipes": [],
            "characters": [],
            "metadata": {
                "crawl_timestamp": datetime.now().isoformat(),
                "total_items": 0,
                "total_recipes": 0,
                "sources": []
            }
        }
        self.processed_urls: Set[str] = set()

    def crawl(self) -> Dict[str, Any]:
        """Main crawling method that processes all configured URLs."""
        base_urls = self.config.get('base_urls', {})

        # Process different types of pages
        self._crawl_items(base_urls)
        self._crawl_recipes(base_urls)
        self._crawl_characters(base_urls)

        # Update metadata
        self.output["metadata"]["total_items"] = len(self.output["items"])
        self.output["metadata"]["total_recipes"] = len(self.output["recipes"])
        self.output["metadata"]["sources"] = list(self.processed_urls)

        self.logger.info(f"Crawling completed. Found {len(self.output['items'])} items, "
                        f"{len(self.output['recipes'])} recipes")

        return self.output

    def _crawl_items(self, base_urls):
        """Crawl all item-related pages."""
        # Handle both dict and list formats for backward compatibility
        if isinstance(base_urls, dict):
            item_urls = [
                base_urls.get('neutral_items'),
                base_urls.get('ranger_items'),
                base_urls.get('reaper_items'),
                base_urls.get('berserker_items'),
                base_urls.get('pyromancer_items')
            ]
        else:
            # Fallback to predefined URLs if base_urls is a list
            item_urls = [
                "https://backpackbattles.wiki.gg/wiki/Neutral_items",
                "https://backpackbattles.wiki.gg/wiki/Ranger_items",
                "https://backpackbattles.wiki.gg/wiki/Reaper_items",
                "https://backpackbattles.wiki.gg/wiki/Berserker_items",
                "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"
            ]

        # Also crawl individual item type pages
        item_types = self.config.get('item_types', [])
        for item_type in item_types:
            item_urls.append(f"https://backpackbattles.wiki.gg/wiki/{item_type}")

        with ThreadPoolExecutor(max_workers=3) as executor:
            future_to_url = {
                executor.submit(self._process_item_page, url): url
                for url in item_urls if url
            }

            for future in as_completed(future_to_url):
                url = future_to_url[future]
                try:
                    items = future.result()
                    if items:
                        self.output["items"].extend(items)
                        self.processed_urls.add(url)
                        self.logger.info(f"Extracted {len(items)} items from {url}")
                except Exception as e:
                    self.logger.error(f"Error processing item page {url}: {e}")

    def _crawl_recipes(self, base_urls):
        """Crawl recipe pages."""
        if isinstance(base_urls, dict):
            recipe_url = base_urls.get('recipes')
        else:
            recipe_url = "https://backpackbattles.wiki.gg/wiki/Recipe"

        if recipe_url:
            try:
                recipes = self._process_recipe_page(recipe_url)
                if recipes:
                    self.output["recipes"].extend(recipes)
                    self.processed_urls.add(recipe_url)
                    self.logger.info(f"Extracted {len(recipes)} recipes from {recipe_url}")
            except Exception as e:
                self.logger.error(f"Error processing recipe page {recipe_url}: {e}")

    def _crawl_characters(self, base_urls):
        """Crawl character pages."""
        if isinstance(base_urls, dict):
            character_url = base_urls.get('characters')
        else:
            character_url = "https://backpackbattles.wiki.gg/wiki/Characters"

        if character_url:
            try:
                characters = self._process_character_page(character_url)
                if characters:
                    self.output["characters"].extend(characters)
                    self.processed_urls.add(character_url)
                    self.logger.info(f"Extracted {len(characters)} characters from {character_url}")
            except Exception as e:
                self.logger.error(f"Error processing character page {character_url}: {e}")

    def _process_item_page(self, url: str) -> List[Item]:
        """Process a single item page."""
        if not url or url in self.processed_urls:
            self.logger.info(f"Skipping already processed URL: {url}")
            return []

        self.logger.info(f"Processing item page: {url}")
        html = self.request_handler.fetch_page(url)

        if not html:
            self.logger.warning(f"Failed to fetch {url}")
            return []

        self.logger.info(f"Fetched HTML for {url}, length: {len(html)}")
        items = DataExtractor.parse_detailed_item_table(html, url)
        self.logger.info(f"Extracted {len(items)} items from {url}")
        return items

    def _process_recipe_page(self, url: str) -> List[Recipe]:
        """Process the recipe page."""
        if not url or url in self.processed_urls:
            return []

        self.logger.info(f"Processing recipe page: {url}")
        html = self.request_handler.fetch_page(url)

        if not html:
            self.logger.warning(f"Failed to fetch {url}")
            return []

        return DataExtractor.parse_recipes(html, url)

    def _process_character_page(self, url: str) -> List[Character]:
        """Process the character page."""
        if not url or url in self.processed_urls:
            return []

        self.logger.info(f"Processing character page: {url}")
        html = self.request_handler.fetch_page(url)

        if not html:
            self.logger.warning(f"Failed to fetch {url}")
            return []

        # Parse character data (simplified for now)
        soup = BeautifulSoup(html, "html.parser")
        characters = []

        # Look for character sections
        character_sections = soup.find_all(['h2', 'h3'], string=re.compile(r'(Ranger|Reaper|Berserker|Pyromancer)', re.I))

        for section in character_sections:
            try:
                name = DataExtractor.normalize_text(section.get_text())
                description = ""

                # Get description from following paragraph
                next_elem = section.find_next_sibling(['p', 'div'])
                if next_elem:
                    description = DataExtractor.normalize_text(next_elem.get_text())

                character = Character(
                    name=name,
                    description=description,
                    starting_items=[],
                    class_items=[],
                    source_url=url
                )
                characters.append(character)

            except Exception as e:
                self.logger.warning(f"Error parsing character section: {e}")
                continue

        return characters

    def save_data(self, filename: str = 'backpack_battles_data.json'):
        """Save crawled data to JSON file."""
        # Convert dataclasses to dictionaries
        output_dict = {
            "items": [asdict(item) for item in self.output["items"]],
            "recipes": [asdict(recipe) for recipe in self.output["recipes"]],
            "characters": [asdict(character) for character in self.output["characters"]],
            "metadata": self.output["metadata"]
        }

        with open(filename, "w", encoding="utf-8") as f:
            json.dump(output_dict, f, indent=2, ensure_ascii=False)
        self.logger.info(f"Data saved to {filename}")

        # Also save a summary
        summary_filename = filename.replace('.json', '_summary.txt')
        with open(summary_filename, 'w', encoding='utf-8') as f:
            f.write(f"Backpack Battles Data Summary\n")
            f.write(f"Crawled on: {self.output['metadata']['crawl_timestamp']}\n\n")
            f.write(f"Total Items: {len(self.output['items'])}\n")
            f.write(f"Total Recipes: {len(self.output['recipes'])}\n")
            f.write(f"Total Characters: {len(self.output['characters'])}\n\n")
            f.write(f"Sources processed:\n")
            for source in self.output['metadata']['sources']:
                f.write(f"  - {source}\n")

        self.logger.info(f"Summary saved to {summary_filename}")

def main():
    """Main entry point."""
    crawler = BackpackBattlesCrawler()
    crawler.crawl()
    crawler.save_data()

if __name__ == "__main__":
    main()
