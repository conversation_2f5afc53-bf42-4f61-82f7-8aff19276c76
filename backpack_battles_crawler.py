import requests
from bs4 import BeautifulSoup
import json
import time
import logging
import random
from urllib.parse import urljoin
from typing import Dict, List, Optional, Any
import re
import hashlib
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
import functools
import yaml

# Configuration Management
class CrawlerConfig:
    def __init__(self, config_path: str = 'crawler_config.yaml'):
        self.config = self.load_config(config_path)
    
    def load_config(self, config_path: str) -> Dict[str, Any]:
        try:
            with open(config_path, 'r') as f:
                return yaml.safe_load(f) or {}
        except FileNotFoundError:
            return self._default_config()
    
    def _default_config(self) -> Dict[str, Any]:
        return {
            'base_urls': [
                "https://backpackbattles.wiki.gg/wiki/Items",
                "https://backpackbattles.wiki.gg/wiki/Classes",
                "https://backpack-battles.fandom.com/wiki/Backpack_Battles_Wiki",
                "https://steamcommunity.com/app/2321400/guides/",
                "https://gamefaqs.gamespot.com/pc/429449-backpack-battles/faqs",
                "https://www.reddit.com/r/BackpackBattles/"
            ],
            'rate_limit': {
                'min_delay': 1,
                'max_delay': 3
            },
            'retry_config': {
                'max_retries': 3,
                'backoff_factor': 2
            },
            'cache_dir': './crawler_cache'
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        return self.config.get(key, default)

# Advanced Logging Setup
def setup_logging(log_file: str = 'backpack_battles_crawler.log') -> logging.Logger:
    logger = logging.getLogger('BackpackBattlesCrawler')
    logger.setLevel(logging.INFO)
    
    # File Handler
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.INFO)
    file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_formatter)
    
    # Console Handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter('%(levelname)s: %(message)s')
    console_handler.setFormatter(console_formatter)
    
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

# Caching Mechanism
class WebCache:
    def __init__(self, cache_dir: str):
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
    
    def _get_cache_path(self, url: str) -> str:
        url_hash = hashlib.md5(url.encode()).hexdigest()
        return os.path.join(self.cache_dir, f"{url_hash}.html")
    
    def get(self, url: str) -> Optional[str]:
        cache_path = self._get_cache_path(url)
        if os.path.exists(cache_path):
            with open(cache_path, 'r', encoding='utf-8') as f:
                return f.read()
        return None
    
    def set(self, url: str, content: str):
        cache_path = self._get_cache_path(url)
        with open(cache_path, 'w', encoding='utf-8') as f:
            f.write(content)

# Robust Request Handler with Retry and Rate Limiting
class RobustRequestHandler:
    def __init__(self, config: CrawlerConfig, logger: logging.Logger):
        self.config = config
        self.logger = logger
        self.cache = WebCache(config.get('cache_dir', './crawler_cache'))
    
    def _exponential_backoff(self, attempt: int) -> float:
        return min(
            self.config.get('retry_config', {}).get('backoff_factor', 2) ** attempt,
            30  # Maximum wait of 30 seconds
        )
    
    @functools.lru_cache(maxsize=128)
    def fetch_page(self, url: str) -> Optional[str]:
        cached_content = self.cache.get(url)
        if cached_content:
            self.logger.info(f"Cache hit for {url}")
            return cached_content
        
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8"
        }
        
        max_retries = self.config.get('retry_config', {}).get('max_retries', 3)
        
        for attempt in range(max_retries):
            try:
                # Random delay between requests
                time.sleep(random.uniform(
                    self.config.get('rate_limit', {}).get('min_delay', 1),
                    self.config.get('rate_limit', {}).get('max_delay', 3)
                ))
                
                with requests.Session() as session:
                    session.headers.update(headers)
                    response = session.get(url, timeout=10)
                    response.raise_for_status()
                    
                    self.cache.set(url, response.text)
                    return response.text
            
            except requests.RequestException as e:
                wait_time = self._exponential_backoff(attempt)
                self.logger.warning(f"Request failed for {url}: {e}. Retry in {wait_time} seconds.")
                time.sleep(wait_time)
        
        self.logger.error(f"Failed to fetch {url} after {max_retries} attempts")
        return None

# Advanced Parsing Strategies
class DataExtractor:
    @staticmethod
    def normalize_text(text: str) -> str:
        """Normalize text by removing extra whitespaces and converting to title case."""
        return re.sub(r'\s+', ' ', text).strip().title()
    
    @staticmethod
    def parse_items_from_wiki(html: str, base_url: str) -> List[Dict[str, str]]:
        soup = BeautifulSoup(html, "html.parser")
        items = []
        
        # More flexible table parsing
        tables = soup.find_all("table", class_=re.compile(r"wikitable|item-table"))
        
        for table in tables:
            rows = table.find_all("tr")[1:]  # Skip header row
            for row in rows:
                cols = row.find_all(["td", "th"])
                if len(cols) >= 2:
                    name_tag = cols[0].find("a")
                    name = DataExtractor.normalize_text(cols[0].get_text())
                    type_ = DataExtractor.normalize_text(cols[1].get_text())
                    
                    link = (urljoin(base_url, name_tag["href"]) 
                            if name_tag and name_tag.has_attr("href") 
                            else base_url)
                    
                    items.append({
                        "name": name,
                        "type": type_,
                        "link": link,
                        "source": base_url
                    })
        
        return items

# Plugin System for Extensibility
class DataSourcePlugin:
    def extract_data(self, html: str, url: str) -> List[Dict[str, str]]:
        raise NotImplementedError("Subclasses must implement extract_data method")

# Main Crawler
class BackpackBattlesCrawler:
    def __init__(self, config_path: str = 'crawler_config.yaml'):
        self.config = CrawlerConfig(config_path)
        self.logger = setup_logging()
        self.request_handler = RobustRequestHandler(self.config, self.logger)
        self.output = {"items": [], "characters": []}
    
    def crawl(self) -> Dict[str, List[Dict[str, str]]]:
        base_urls = self.config.get('base_urls', [])
        
        with ThreadPoolExecutor(max_workers=5) as executor:
            future_to_url = {
                executor.submit(self._process_url, url): url 
                for url in base_urls
            }
            
            for future in as_completed(future_to_url):
                url = future_to_url[future]
                try:
                    result = future.result()
                    if result:
                        self.output["items"].extend(result)
                except Exception as e:
                    self.logger.error(f"Error processing {url}: {e}")
        
        return self.output
    
    def _process_url(self, url: str) -> List[Dict[str, str]]:
        self.logger.info(f"Processing URL: {url}")
        html = self.request_handler.fetch_page(url)
        
        if not html:
            self.logger.warning(f"Failed to fetch {url}")
            return []
        
        # Extensible parsing strategy
        if "wiki.gg" in url:
            return DataExtractor.parse_items_from_wiki(html, url)
        
        return []
    
    def save_data(self, filename: str = 'backpack_battles_data.json'):
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(self.output, f, indent=2, ensure_ascii=False)
        self.logger.info(f"Data saved to {filename}")

def main():
    crawler = BackpackBattlesCrawler()
    crawler.crawl()
    crawler.save_data()

if __name__ == "__main__":
    main()
