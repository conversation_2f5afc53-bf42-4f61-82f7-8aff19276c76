# simulation.py

import json
from data_manager import GameDataManager

class Backpack:
    def __init__(self, grid_size=(6, 6)):
        self.grid_size = grid_size
        self.grid = [[None for _ in range(grid_size[0])] for _ in range(grid_size[1])]
        self.items = []

    def place_item(self, item, row, col):
        # Basic placement logic, needs more sophisticated checks for item size and overlap
        if 0 <= row < self.grid_size[1] and 0 <= col < self.grid_size[0] and self.grid[row][col] is None:
            self.grid[row][col] = item
            self.items.append(item)
            return True
        return False

    def get_item_at(self, row, col):
        if 0 <= row < self.grid_size[1] and 0 <= col < self.grid_size[0]:
            return self.grid[row][col]
        return None

class Combatant:
    def __init__(self, name, backpack, initial_health=100):
        self.name = name
        self.backpack = backpack
        self.health = initial_health
        self.status_effects = {} # Example: {'poison': 3, 'stun': 1}

    def take_damage(self, damage):
        self.health -= damage
        if self.health < 0:
            self.health = 0

    def is_alive(self):
        return self.health > 0

class GameSimulation:
    def __init__(self, game_data=None, game_data_path='game_data.json'):
        if game_data is not None:
            # If game_data is provided directly, use it
            self.data_manager = GameDataManager(game_data_path)
            self.data_manager.game_data = game_data
            self.game_data = game_data
        else:
            # Otherwise, load from file
            self.data_manager = GameDataManager(game_data_path)
            self.game_data = self.data_manager.game_data
        
        # Initialize default agent state
        self.agent_health = 100.0
        self.agent_gold = 50.0
        
        # Initialize shop items
        self.shop_items = self._generate_shop_items()
        
        # Add more initialization based on game data if needed
        self._initialize_agent_state()
    
    def _initialize_agent_state(self):
        """
        Initialize agent state based on game data.
        Uses the first character class as default if available.
        """
        character_classes = self.game_data.get('character_classes', [])
        if character_classes:
            default_class = character_classes[0]
            base_stats = default_class.get('base_stats', {})
            self.agent_health = base_stats.get('health', 100.0)
            
            # Adjust gold based on character class or game data
            self.agent_gold = default_class.get('starting_gold', 50.0)
    
    def _generate_shop_items(self, num_items=5):
        """
        Generate a list of shop items based on available items in game data.
        
        Args:
            num_items (int): Number of items to generate in the shop
        
        Returns:
            List of shop items
        """
        items = self.game_data.get('items', [])
        if not items:
            return []
        
        # Randomly select items for the shop
        import random
        return random.sample(items, min(num_items, len(items)))
    
    def buy_item(self, item_index: int) -> bool:
        """
        Simulate buying an item from the shop.
        
        Args:
            item_index (int): Index of the item in the shop
        
        Returns:
            bool: True if item was successfully purchased, False otherwise
        """
        if 0 <= item_index < len(self.shop_items):
            item = self.shop_items[item_index]
            item_cost = item.get('value', 10)  # Default cost if not specified
            
            if self.agent_gold >= item_cost:
                self.agent_gold -= item_cost
                # Remove the item from shop after purchase
                del self.shop_items[item_index]
                return True
        
        return False
    
    def run_combat(self, combatant1=None, combatant2=None):
        """
        Simulate combat outcome.
        
        Args:
            combatant1 (Optional[Combatant]): First combatant (not used in this simplified version)
            combatant2 (Optional[Combatant]): Second combatant (not used in this simplified version)
        
        Returns:
            str: Combat outcome ('win', 'loss', or 'draw')
        """
        # Simplified combat logic
        import random
        
        # Adjust outcome based on agent health
        if self.agent_health <= 0:
            return 'loss'
        elif self.agent_health >= 100:
            return 'win'
        
        outcomes = ['win', 'loss', 'draw']
        return random.choice(outcomes)

    def _process_turn(self, attacker, defender):
        # This is a placeholder. Actual logic needs to consider item types, speed, triggers, etc.
        print(f"{attacker.name}'s turn")
        for item in attacker.backpack.items:
            # Example: Simple attack if item has 'damage' property
            if 'damage' in self.game_data['items'].get(item['name'], {}):
                damage = self.game_data['items'][item['name']]['damage']
                print(f"{item['name']} deals {damage} damage to {defender.name}")
                defender.take_damage(damage)
                if not defender.is_alive():
                    print(f"{defender.name} defeated!")
                    break

    def _determine_outcome(self, combatant1, combatant2):
        if combatant1.is_alive() and not combatant2.is_alive():
            print(f"{combatant1.name} wins!")
        elif not combatant1.is_alive() and combatant2.is_alive():
            print(f"{combatant2.name} wins!")
        elif not combatant1.is_alive() and not combatant2.is_alive():
            print("Draw!")
        else:
            print("Combat ended without a clear winner (e.g., turn limit reached).")

# Example Usage (for testing)
if __name__ == "__main__":
    # Create dummy items (these should ideally come from DataManager)
    dummy_item1 = {"name": "Sword", "type": "weapon", "damage": 10}
    dummy_item2 = {"name": "Shield", "type": "armor", "defense": 5}
    dummy_item3 = {"name": "Potion", "type": "consumable", "effect": "heal"}

    # Create backpacks and place items
    backpack1 = Backpack()
    backpack1.place_item(dummy_item1, 0, 0)
    backpack1.place_item(dummy_item2, 1, 0)

    backpack2 = Backpack()
    backpack2.place_item(dummy_item1, 0, 0)
    backpack2.place_item(dummy_item3, 1, 1)


    # Create combatants
    player1 = Combatant("Player 1", backpack1)
    player2 = Combatant("Player 2", backpack2)

    # Run simulation
    sim = GameSimulation()
    sim.run_combat(player1, player2)