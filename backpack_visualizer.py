import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import ListedColormap
import re
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import seaborn as sns

# Set up the plotting style
plt.style.use('default')
sns.set_palette("husl")

@dataclass
class ItemPlacement:
    """Represents an item placed in the backpack"""
    item_name: str
    x: int
    y: int
    width: int
    height: int
    grid_layout: List[List[str]]
    item_type: str
    rarity: str

@dataclass
class CombatStats:
    """Combat analysis results"""
    total_dps: float
    stamina_efficiency: float
    buff_sources: List[str]
    debuff_sources: List[str]
    healing_per_second: float
    damage_mitigation: float

class BackpackVisualizer:
    def __init__(self, data_file: str = "complete_enhanced_backpack_battles_data.json"):
        """Initialize the visualizer with game data."""
        with open(data_file, 'r', encoding='utf-8') as f:
            self.game_data = json.load(f)
        
        self.items = {item['name']: item for item in self.game_data['enhanced_items']}
        self.recipes = self.game_data.get('recipes', [])
        
        # Emoji mapping for missing emojis in the data
        self.emoji_map = {
            'strength': '💪',
            'mana': '🔮', 
            'armor': '🛡️',
            'health': '❤️',
            'fire': '🔥',
            'ice': '❄️',
            'poison': '☠️',
            'holy': '✨',
            'luck': '🍀',
            'stamina': '⚡',
            'rage': '😡',
            'shield': '🛡️',
            'heal': '💚',
            'damage': '⚔️'
        }
        
        # Color mapping for different item types and rarities
        self.type_colors = {
            'Weapon': '#FF6B6B',
            'Armor': '#4ECDC4', 
            'Accessory': '#45B7D1',
            'Food': '#96CEB4',
            'Bag': '#FFEAA7',
            'Potion': '#DDA0DD',
            'Pet': '#FFB347',
            'Skill': '#98D8C8',
            'Gemstone': '#F7DC6F'
        }
        
        self.rarity_colors = {
            'Common': '#FFFFFF',
            'Rare': '#0099FF', 
            'Epic': '#9933FF',
            'Legendary': '#FF9900',
            'Unique': '#FF0066',
            'Godly': '#FFD700',
            'Varies': '#CCCCCC'
        }

    def create_backpack_grid(self, width: int = 10, height: int = 8) -> np.ndarray:
        """Create an empty backpack grid."""
        return np.zeros((height, width), dtype=int)

    def place_item_in_grid(self, grid: np.ndarray, item_name: str, x: int, y: int) -> bool:
        """
        Attempt to place an item in the grid at position (x, y).
        Returns True if successful, False if there's a collision.
        """
        if item_name not in self.items:
            return False
        
        item = self.items[item_name]
        grid_layout = item.get('grid_layout', [])
        
        if not grid_layout:
            # Default to 1x1 if no grid layout
            grid_layout = [['cell']]
        
        height, width = len(grid_layout), len(grid_layout[0]) if grid_layout else 1
        
        # Check if item fits in grid
        if y + height > grid.shape[0] or x + width > grid.shape[1]:
            return False
        
        # Check for collisions
        for dy in range(height):
            for dx in range(width):
                if grid_layout[dy][dx] != 'empty' and grid[y + dy, x + dx] != 0:
                    return False
        
        # Place item (use hash of item name as ID)
        item_id = hash(item_name) % 1000 + 1
        for dy in range(height):
            for dx in range(width):
                if grid_layout[dy][dx] != 'empty':
                    grid[y + dy, x + dx] = item_id
        
        return True

    def visualize_backpack(self, items_to_place: List[Tuple[str, int, int]], 
                          grid_size: Tuple[int, int] = (10, 8),
                          title: str = "Backpack Layout") -> plt.Figure:
        """
        Visualize a backpack with placed items.
        
        Args:
            items_to_place: List of (item_name, x, y) tuples
            grid_size: (width, height) of the backpack
            title: Title for the visualization
        """
        width, height = grid_size
        grid = self.create_backpack_grid(width, height)
        placed_items = []
        
        # Place items in grid
        for item_name, x, y in items_to_place:
            if self.place_item_in_grid(grid, item_name, x, y):
                item = self.items[item_name]
                grid_layout = item.get('grid_layout', [['cell']])
                item_height, item_width = len(grid_layout), len(grid_layout[0]) if grid_layout else 1
                
                placed_items.append(ItemPlacement(
                    item_name=item_name,
                    x=x, y=y,
                    width=item_width,
                    height=item_height,
                    grid_layout=grid_layout,
                    item_type=item.get('type', 'Unknown'),
                    rarity=item.get('rarity', 'Common')
                ))
        
        # Create visualization
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
        
        # Main backpack visualization
        ax1.set_xlim(0, width)
        ax1.set_ylim(0, height)
        ax1.set_aspect('equal')
        ax1.invert_yaxis()  # Invert Y axis so (0,0) is top-left
        ax1.set_title(title, fontsize=16, fontweight='bold')
        
        # Draw grid lines
        for i in range(width + 1):
            ax1.axvline(i, color='lightgray', linewidth=0.5)
        for i in range(height + 1):
            ax1.axhline(i, color='lightgray', linewidth=0.5)
        
        # Draw placed items
        for item in placed_items:
            # Get colors
            type_color = self.type_colors.get(item.item_type, '#CCCCCC')
            rarity_color = self.rarity_colors.get(item.rarity, '#FFFFFF')
            
            # Draw item rectangle
            rect = patches.Rectangle(
                (item.x, item.y), item.width, item.height,
                linewidth=2, edgecolor=rarity_color, facecolor=type_color, alpha=0.7
            )
            ax1.add_patch(rect)
            
            # Draw grid pattern within item
            for dy, row in enumerate(item.grid_layout):
                for dx, cell_type in enumerate(row):
                    cell_x, cell_y = item.x + dx, item.y + dy
                    
                    if cell_type == 'star':
                        ax1.plot(cell_x + 0.5, cell_y + 0.5, '*', 
                                color='gold', markersize=15, markeredgecolor='black')
                    elif cell_type == 'diamond':
                        ax1.plot(cell_x + 0.5, cell_y + 0.5, 'D', 
                                color='cyan', markersize=10, markeredgecolor='black')
                    elif cell_type == 'cell':
                        ax1.plot(cell_x + 0.5, cell_y + 0.5, 's', 
                                color='white', markersize=8, markeredgecolor='gray', alpha=0.5)
            
            # Add item name
            text_x, text_y = item.x + item.width/2, item.y + item.height/2
            ax1.text(text_x, text_y, item.item_name, 
                    ha='center', va='center', fontsize=8, fontweight='bold',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
        
        # Legend for symbols
        legend_elements = [
            plt.Line2D([0], [0], marker='*', color='w', markerfacecolor='gold', 
                      markersize=15, label='Star Position (★)'),
            plt.Line2D([0], [0], marker='D', color='w', markerfacecolor='cyan', 
                      markersize=10, label='Diamond Position (♦)'),
            plt.Line2D([0], [0], marker='s', color='w', markerfacecolor='white', 
                      markersize=8, label='Cell Position')
        ]
        ax1.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1, 1))
        
        # Item summary panel
        ax2.axis('off')
        ax2.set_title('Item Summary', fontsize=14, fontweight='bold')
        
        summary_text = []
        total_cost = 0
        type_counts = {}
        rarity_counts = {}
        
        for item in placed_items:
            item_data = self.items[item.item_name]
            cost = int(item_data.get('cost', '0'))
            total_cost += cost
            
            item_type = item.item_type
            type_counts[item_type] = type_counts.get(item_type, 0) + 1
            
            rarity = item.rarity
            rarity_counts[rarity] = rarity_counts.get(rarity, 0) + 1
            
            # Add item details
            summary_text.append(f"• {item.item_name} ({item_type}, {rarity})")
            effect = item_data.get('effect', '')[:60] + '...' if len(item_data.get('effect', '')) > 60 else item_data.get('effect', '')
            summary_text.append(f"  {effect}")
            summary_text.append("")
        
        # Add statistics
        summary_text.extend([
            f"Total Items: {len(placed_items)}",
            f"Total Cost: {total_cost} gold",
            "",
            "By Type:",
            *[f"  {t}: {c}" for t, c in type_counts.items()],
            "",
            "By Rarity:",
            *[f"  {r}: {c}" for r, c in rarity_counts.items()]
        ])
        
        ax2.text(0.05, 0.95, '\n'.join(summary_text), 
                transform=ax2.transAxes, fontsize=10,
                verticalalignment='top', fontfamily='monospace')
        
        plt.tight_layout()
        return fig

    def analyze_combat_stats(self, items_to_analyze: List[str]) -> CombatStats:
        """
        Analyze combat statistics for a list of items.
        """
        total_dps = 0.0
        stamina_efficiency = 0.0
        buff_sources = []
        debuff_sources = []
        healing_per_second = 0.0
        damage_mitigation = 0.0
        
        for item_name in items_to_analyze:
            if item_name not in self.items:
                continue
                
            item = self.items[item_name]
            effect = item.get('effect', '')
            weapon_stats = item.get('weapon_stats')
            
            # Analyze weapon DPS
            if weapon_stats and weapon_stats.get('damage'):
                damage_str = weapon_stats['damage']
                # Parse damage like "16-19 (12.5/s)"
                dps_match = re.search(r'\(([0-9.]+)/s\)', damage_str)
                if dps_match:
                    total_dps += float(dps_match.group(1))
            
            # Analyze buffs and debuffs
            if 'gain' in effect.lower() and any(buff in effect.lower() for buff in ['strength', 'armor', 'health', 'mana']):
                buff_sources.append(item_name)
            
            if 'inflict' in effect.lower() or 'debuff' in effect.lower():
                debuff_sources.append(item_name)
            
            # Analyze healing
            heal_matches = re.findall(r'heal.*?(\d+)', effect.lower())
            for heal_amount in heal_matches:
                # Estimate healing per second (rough calculation)
                timing_matches = re.findall(r'(\d+(?:\.\d+)?)s', effect.lower())
                if timing_matches:
                    interval = float(timing_matches[0])
                    healing_per_second += int(heal_amount) / interval
                else:
                    healing_per_second += int(heal_amount) / 10  # Default 10s interval
            
            # Analyze damage mitigation
            if 'resist' in effect.lower() or 'reduce.*damage' in effect.lower():
                damage_mitigation += 10  # Rough estimate
        
        return CombatStats(
            total_dps=total_dps,
            stamina_efficiency=stamina_efficiency,
            buff_sources=buff_sources,
            debuff_sources=debuff_sources,
            healing_per_second=healing_per_second,
            damage_mitigation=damage_mitigation
        )

    def create_combat_analysis_chart(self, items_to_analyze: List[str]) -> plt.Figure:
        """Create a comprehensive combat analysis chart."""
        stats = self.analyze_combat_stats(items_to_analyze)
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Combat Analysis Dashboard', fontsize=16, fontweight='bold')
        
        # DPS Analysis
        ax1.bar(['Total DPS'], [stats.total_dps], color='#FF6B6B')
        ax1.set_title('Damage Per Second')
        ax1.set_ylabel('DPS')
        
        # Buff/Debuff Sources
        buff_count = len(stats.buff_sources)
        debuff_count = len(stats.debuff_sources)
        ax2.bar(['Buff Sources', 'Debuff Sources'], [buff_count, debuff_count], 
                color=['#4ECDC4', '#FF9999'])
        ax2.set_title('Buff/Debuff Sources')
        ax2.set_ylabel('Number of Items')
        
        # Healing Analysis
        ax3.bar(['Healing/sec'], [stats.healing_per_second], color='#96CEB4')
        ax3.set_title('Healing Per Second')
        ax3.set_ylabel('HP/sec')
        
        # Damage Mitigation
        ax4.bar(['Mitigation'], [stats.damage_mitigation], color='#FFEAA7')
        ax4.set_title('Damage Mitigation')
        ax4.set_ylabel('Estimated %')
        
        plt.tight_layout()
        return fig

    def fix_emoji_text(self, text: str) -> str:
        """Fix missing emojis in effect text."""
        # Common patterns where emojis are missing
        patterns = [
            (r'\bgain (\d+) \b', r'gain \1 💪 '),  # strength
            (r'\bgain (\d+) maximum health\b', r'gain \1 ❤️ maximum health'),
            (r'\binflict (\d+) \b', r'inflict \1 ☠️ '),  # poison/debuff
            (r'\bheal for (\d+)\b', r'heal for \1 💚'),
            (r'\bdeal (\d+) .*damage\b', r'deal \1 ⚔️ damage'),
            (r'\bgain (\d+) armor\b', r'gain \1 🛡️'),
            (r'\buse (\d+) \b', r'use \1 🔮 '),  # mana
        ]
        
        fixed_text = text
        for pattern, replacement in patterns:
            fixed_text = re.sub(pattern, replacement, fixed_text, flags=re.IGNORECASE)
        
        return fixed_text

def main():
    """Example usage of the backpack visualizer."""
    # Use non-interactive backend for matplotlib
    import matplotlib
    matplotlib.use('Agg')

    visualizer = BackpackVisualizer()

    # Example backpack layout
    example_items = [
        ("Crossblades", 0, 0),
        ("Fanny Pack", 0, 2),
        ("Banana", 2, 0),
        ("Vampiric Gloves", 3, 2),
        ("Ruby", 5, 0),
        ("Emerald", 6, 0),
        ("Stamina Sack", 0, 4)
    ]

    print("🎒 Creating backpack visualization...")

    # Create backpack visualization
    fig1 = visualizer.visualize_backpack(
        example_items,
        grid_size=(10, 8),
        title="Example Backpack Build"
    )
    fig1.savefig('backpack_visualization.png', dpi=300, bbox_inches='tight')
    print("✅ Saved backpack visualization to: backpack_visualization.png")
    plt.close(fig1)

    # Create combat analysis
    item_names = [item[0] for item in example_items]
    fig2 = visualizer.create_combat_analysis_chart(item_names)
    fig2.savefig('combat_analysis.png', dpi=300, bbox_inches='tight')
    print("✅ Saved combat analysis to: combat_analysis.png")
    plt.close(fig2)

    # Analyze combat stats
    stats = visualizer.analyze_combat_stats(item_names)
    print("\n=== 🗡️ COMBAT ANALYSIS ===")
    print(f"⚔️ Total DPS: {stats.total_dps:.2f}")
    print(f"💚 Healing/sec: {stats.healing_per_second:.2f}")
    print(f"🛡️ Damage Mitigation: {stats.damage_mitigation:.1f}%")
    print(f"💪 Buff Sources: {', '.join(stats.buff_sources) if stats.buff_sources else 'None'}")
    print(f"☠️ Debuff Sources: {', '.join(stats.debuff_sources) if stats.debuff_sources else 'None'}")

    # Test with emoji-fixed data
    try:
        visualizer_emoji = BackpackVisualizer("complete_enhanced_backpack_battles_data_with_emojis.json")
        print("\n=== 😀 TESTING WITH EMOJI-FIXED DATA ===")

        # Show some sample fixed effects
        sample_items = ["Banana", "Crossblades", "Fanny Pack"]
        for item_name in sample_items:
            if item_name in visualizer_emoji.items:
                item = visualizer_emoji.items[item_name]
                print(f"\n🔧 {item_name}:")
                print(f"   Effect: {item.get('effect', 'No effect')}")

    except FileNotFoundError:
        print("⚠️ Emoji-fixed data file not found. Run emoji_fixer.py first.")

    print("\n✅ Visualization system test complete!")

if __name__ == "__main__":
    main()
