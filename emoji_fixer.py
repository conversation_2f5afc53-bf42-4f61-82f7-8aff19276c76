import json
import re
import requests
from bs4 import BeautifulSoup
from typing import Dict, List, Tuple
import time

class EmojiRestorer:
    """Restore missing emojis in Backpack Battles game data."""
    
    def __init__(self):
        # Comprehensive emoji mapping based on Backpack Battles game mechanics
        self.emoji_patterns = {
            # Core stats
            r'\bgain (\d+) \b(?!maximum|health|damage|stamina|armor)': r'gain \1 💪 ',  # Strength
            r'\buse (\d+) \b(?!stamina|health)': r'use \1 🔮 ',  # Mana
            r'\bgain (\d+) maximum health\b': r'gain \1 ❤️ maximum health',
            r'\bheal for (\d+)\b': r'heal for \1 💚',
            r'\bgain (\d+) armor\b': r'gain \1 🛡️',
            r'\binflict (\d+) \b(?!damage)': r'inflict \1 ☠️ ',  # Poison/debuff
            r'\bgain (\d+) stamina\b': r'gain \1 ⚡',
            
            # Damage types
            r'\bdeal (\d+) -damage\b': r'deal \1 🔥 damage',  # Fire damage
            r'\bdeal (\d+) -damage\b': r'deal \1 ❄️ damage',  # Ice damage (context dependent)
            r'\binflict (\d+) \b': r'inflict \1 ☠️ ',  # Generic debuff
            
            # Special effects
            r'\bgain (\d+) \b(?=.*luck|fortune|critical)': r'gain \1 🍀 ',  # Luck
            r'\bgain (\d+) \b(?=.*rage|anger)': r'gain \1 😡 ',  # Rage
            r'\bgain (\d+) \b(?=.*holy|divine|light)': r'gain \1 ✨ ',  # Holy
            
            # Item-specific patterns
            r'\b(\d+) reached\b': r'\1 💪 reached',  # Strength thresholds
            r'\b(\d+) gained\b': r'\1 💪 gained',  # Strength gained
            r'\bremove (\d+) \b': r'remove \1 💪 ',  # Remove strength
            r'\bcleanse (\d+) \b': r'cleanse \1 ☠️ ',  # Cleanse debuffs
            
            # Weapon and combat
            r'\bWeapons? gain (\d+) damage\b': r'Weapons gain \1 ⚔️ damage',
            r'\bdeal \+(\d+) damage\b': r'deal +\1 ⚔️ damage',
            r'\bresist (\d+) \b': r'resist \1 🛡️ ',  # Resist effects
            
            # Time and triggers
            r'\bEvery (\d+(?:\.\d+)?)s\b': r'Every \1s ⏰',
            r'\bAfter (\d+(?:\.\d+)?)s\b': r'After \1s ⏰',
            
            # Food and consumables
            r'\bFood\b(?=.*trigger)': r'🍎 Food',
            r'\bPotion\b(?=.*consumed)': r'🧪 Potion',
            
            # Pets and creatures
            r'\bPet\b(?=.*trigger|activate)': r'🐾 Pet',
            r'\bDragon\b(?=.*attack|trigger)': r'🐉 Dragon',
            
            # Bags and containers
            r'\bAdd (\d+) backpack slots?\b': r'Add \1 🎒 backpack slots',
            r'\bitems? inside\b': r'items inside 🎒',
        }
        
        # Context-specific emoji mapping
        self.context_emojis = {
            'fire': '🔥',
            'ice': '❄️', 
            'poison': '☠️',
            'holy': '✨',
            'strength': '💪',
            'mana': '🔮',
            'armor': '🛡️',
            'health': '❤️',
            'stamina': '⚡',
            'luck': '🍀',
            'rage': '😡',
            'damage': '⚔️',
            'heal': '💚',
            'shield': '🛡️'
        }

    def detect_missing_emojis(self, text: str) -> List[str]:
        """Detect where emojis are likely missing based on patterns."""
        missing_patterns = []
        
        # Look for number followed by space at end of sentence/phrase
        number_space_pattern = r'\b(\d+) \b(?=[A-Z]|$|\.|\,|\:)'
        matches = re.findall(number_space_pattern, text)
        
        if matches:
            missing_patterns.append(f"Found {len(matches)} potential missing emojis after numbers")
        
        # Look for specific game terms that usually have emojis
        game_terms = ['gain', 'inflict', 'deal', 'use', 'remove', 'cleanse']
        for term in game_terms:
            if re.search(rf'\b{term} \d+ \b', text):
                missing_patterns.append(f"Missing emoji after '{term} [number]'")
        
        return missing_patterns

    def restore_emojis_in_text(self, text: str, context: str = "") -> str:
        """Restore emojis in a text string using pattern matching."""
        if not text:
            return text
        
        restored_text = text
        
        # Apply emoji patterns
        for pattern, replacement in self.emoji_patterns.items():
            restored_text = re.sub(pattern, replacement, restored_text, flags=re.IGNORECASE)
        
        # Context-specific replacements
        if context:
            context_lower = context.lower()
            for keyword, emoji in self.context_emojis.items():
                if keyword in context_lower:
                    # Apply context-specific emoji patterns
                    if keyword == 'fire':
                        restored_text = re.sub(r'\bdeal (\d+) -damage\b', rf'deal \1 {emoji} damage', restored_text)
                    elif keyword == 'ice':
                        restored_text = re.sub(r'\binflict (\d+) \b', rf'inflict \1 {emoji} ', restored_text)
        
        return restored_text

    def fix_game_data_emojis(self, data_file: str, output_file: str = None) -> Dict:
        """Fix emojis in the complete game data file."""
        print(f"Loading game data from {data_file}...")
        
        with open(data_file, 'r', encoding='utf-8') as f:
            game_data = json.load(f)
        
        fixed_items = []
        total_fixes = 0
        
        print("Restoring emojis in item effects...")
        
        for i, item in enumerate(game_data.get('enhanced_items', [])):
            if (i + 1) % 50 == 0:
                print(f"Processed {i + 1} items...")
            
            original_effect = item.get('effect', '')
            item_name = item.get('name', '')
            item_type = item.get('type', '')
            
            # Create context from item name and type
            context = f"{item_name} {item_type}"
            
            # Restore emojis
            fixed_effect = self.restore_emojis_in_text(original_effect, context)
            
            # Count fixes
            if fixed_effect != original_effect:
                total_fixes += 1
            
            # Update item
            item['effect'] = fixed_effect
            fixed_items.append(item)
        
        # Update the data
        game_data['enhanced_items'] = fixed_items
        
        # Add metadata about emoji restoration
        if 'metadata' not in game_data:
            game_data['metadata'] = {}
        
        game_data['metadata']['emoji_restoration'] = {
            'total_items_processed': len(fixed_items),
            'items_with_emoji_fixes': total_fixes,
            'restoration_timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # Save fixed data
        if output_file is None:
            output_file = data_file.replace('.json', '_emoji_fixed.json')
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(game_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ Emoji restoration complete!")
        print(f"📊 Total items processed: {len(fixed_items)}")
        print(f"🔧 Items with emoji fixes: {total_fixes}")
        print(f"💾 Saved to: {output_file}")
        
        return game_data

    def sample_emoji_fixes(self, data_file: str, num_samples: int = 10) -> None:
        """Show sample emoji fixes for verification."""
        with open(data_file, 'r', encoding='utf-8') as f:
            game_data = json.load(f)
        
        print(f"\n=== SAMPLE EMOJI FIXES (showing {num_samples} examples) ===\n")
        
        fixes_shown = 0
        for item in game_data.get('enhanced_items', []):
            if fixes_shown >= num_samples:
                break
                
            original_effect = item.get('effect', '')
            item_name = item.get('name', '')
            item_type = item.get('type', '')
            
            context = f"{item_name} {item_type}"
            fixed_effect = self.restore_emojis_in_text(original_effect, context)
            
            if fixed_effect != original_effect:
                print(f"🔧 {item_name} ({item_type})")
                print(f"   Before: {original_effect}")
                print(f"   After:  {fixed_effect}")
                print()
                fixes_shown += 1

def main():
    """Test the emoji restoration system."""
    restorer = EmojiRestorer()
    
    # Test individual text restoration
    test_texts = [
        "Start of battle: Gain 3 random buffs.",
        "Every 5s: Heal for 4 and regenerate 1 stamina.",
        "On hit: Use 1 to gain 1 and 1 .",
        "Deal 20% of your healing as -damage.",
        "Weapons gain 4 damage.",
        "Add 2 backpack slots. Items inside trigger 10% faster."
    ]
    
    print("=== TESTING EMOJI RESTORATION ===\n")
    
    for text in test_texts:
        fixed = restorer.restore_emojis_in_text(text)
        print(f"Original: {text}")
        print(f"Fixed:    {fixed}")
        print()
    
    # Fix the complete game data
    print("\n=== FIXING COMPLETE GAME DATA ===")
    
    try:
        fixed_data = restorer.fix_game_data_emojis(
            'complete_enhanced_backpack_battles_data.json',
            'complete_enhanced_backpack_battles_data_with_emojis.json'
        )
        
        # Show sample fixes
        restorer.sample_emoji_fixes('complete_enhanced_backpack_battles_data.json')
        
    except FileNotFoundError:
        print("❌ Game data file not found. Please run the enhanced crawler first.")

if __name__ == "__main__":
    main()
