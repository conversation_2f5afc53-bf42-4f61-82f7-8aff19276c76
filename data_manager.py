import json
import logging
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
import jsonschema
from functools import lru_cache
import hashlib
from datetime import datetime, timezone

from game_interface import (
    Item, create_item_from_data, Inventory, 
    InventoryManager, ItemType, load_game_data
)

class DataValidationError(Exception):
    """Custom exception for data validation failures."""
    pass

class GameDataManager:
    """
    Advanced data management system for Backpack Battles game mechanics.
    
    Enhanced Responsibilities:
    - Comprehensive data loading and validation
    - Robust item management
    - Performance-optimized data retrieval
    - Advanced logging and error tracking
    - Data integrity and versioning support
    """
    
    def __init__(
        self, 
        data_file: Union[str, Path] = 'game_data.json', 
        log_level: int = logging.INFO
    ):
        """
        Initialize GameDataManager with advanced configuration.
        
        Args:
            data_file (Union[str, Path]): Path to game data JSON
            log_level (int): Logging verbosity level
        """
        self.data_file = Path(data_file)
        self.game_data: Dict[str, Any] = {}
        self.items: Dict[str, Item] = {}
        
        # Advanced logging configuration
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('game_data_manager.log', encoding='utf-8')
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # Performance and integrity tracking
        self._data_hash: Optional[str] = None
        self._last_loaded: Optional[datetime] = None
        
        self.load_game_data()
        self.validate_game_data()
        self.initialize_items()
    
    def load_game_data(self) -> None:
        """
        Enhanced game data loading with integrity checks.
        
        Raises:
            DataValidationError: If data loading fails
        """
        try:
            with self.data_file.open('r', encoding='utf-8') as f:
                raw_data = f.read()
                
                # Compute data hash for change tracking
                self._data_hash = hashlib.sha256(raw_data.encode()).hexdigest()
                self.game_data = json.loads(raw_data)
                self._last_loaded = datetime.now(timezone.utc)
            
            self.logger.info(f"Successfully loaded game data from {self.data_file}")
            self.logger.debug(f"Data hash: {self._data_hash}")
        
        except (FileNotFoundError, json.JSONDecodeError) as e:
            error_msg = f"Game data loading failed: {e}"
            self.logger.error(error_msg)
            raise DataValidationError(error_msg) from e
    
    def validate_game_data(self) -> None:
        """
        Comprehensive game data validation with advanced schema.
        
        Raises:
            DataValidationError: If validation fails
        """
        schema = {
            "type": "object",
            "properties": {
                "metadata": {
                    "type": "object",
                    "properties": {
                        "version": {"type": "string"},
                        "last_updated": {"type": "string"},
                        "compatibility": {"type": "array", "items": {"type": "string"}}
                    },
                    "required": ["version"]
                },
                "items": {
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "type": "object",
                        "required": ["id", "name", "type"],
                        "properties": {
                            "id": {"type": "string", "minLength": 1},
                            "name": {"type": "string", "minLength": 1},
                            "type": {"enum": [t.name.lower() for t in ItemType]},
                            "dimensions": {
                                "type": "object",
                                "properties": {
                                    "width": {"type": "number", "minimum": 1},
                                    "height": {"type": "number", "minimum": 1}
                                },
                                "required": ["width", "height"]
                            }
                        }
                    }
                }
            },
            "required": ["metadata", "items"]
        }
        
        try:
            jsonschema.validate(instance=self.game_data, schema=schema)
            self.logger.info("Game data successfully validated against comprehensive schema")
        except jsonschema.ValidationError as e:
            error_msg = f"Game data validation failed: {e}"
            self.logger.error(error_msg)
            raise DataValidationError(error_msg) from e
    
    def initialize_items(self) -> None:
        """
        Robust item initialization with comprehensive error handling.
        """
        successful_items = 0
        failed_items = 0
        
        for item_data in self.game_data.get('items', []):
            try:
                item = create_item_from_data(item_data)
                if item:
                    self.items[item.id] = item
                    successful_items += 1
                else:
                    failed_items += 1
                    self.logger.warning(f"Skipping invalid item: {item_data.get('id', 'Unknown')}")
            except Exception as e:
                failed_items += 1
                self.logger.error(f"Item creation error for {item_data.get('id', 'Unknown')}: {e}")
        
        self.logger.info(
            f"Item Initialization Summary: "
            f"Total: {successful_items + failed_items}, "
            f"Successful: {successful_items}, "
            f"Failed: {failed_items}"
        )
    
    @lru_cache(maxsize=128)
    def get_item(self, item_id: str) -> Optional[Item]:
        """
        Cached item retrieval with performance optimization.
        
        Args:
            item_id (str): Unique identifier for the item
        
        Returns:
            Optional[Item]: The requested item or None if not found
        """
        return self.items.get(item_id)
    
    def find_items_by_type(self, item_type: str) -> List[Item]:
        """
        Efficient item type filtering.
        
        Args:
            item_type (str): Type of items to find
        
        Returns:
            List[Item]: List of items matching the type
        """
        try:
            type_enum = ItemType[item_type.upper()]
            return [item for item in self.items.values() if item.item_type == type_enum]
        except KeyError:
            self.logger.warning(f"Invalid item type: {item_type}")
            return []
    
    def calculate_crafting_probability(self, recipe_id: str, ingredients: List[Item]) -> float:
        """
        Advanced crafting probability calculation.
        
        Args:
            recipe_id (str): ID of the crafting recipe
            ingredients (List[Item]): Items used in crafting
        
        Returns:
            float: Probability of successful crafting
        """
        recipe = next(
            (r for r in self.game_data.get('crafting_recipes', []) if r['id'] == recipe_id), 
            None
        )
        
        if not recipe:
            self.logger.warning(f"No recipe found for ID: {recipe_id}")
            return 0.0
        
        return InventoryManager.calculate_crafting_probability(ingredients, recipe)
    
    def export_game_data(self, output_file: Optional[str] = None) -> Path:
        """
        Enhanced game data export with versioning.
        
        Args:
            output_file (Optional[str]): Export file path
        
        Returns:
            Path: Path to exported file
        """
        export_path = Path(output_file or self.data_file)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        versioned_path = export_path.with_stem(f"{export_path.stem}_{timestamp}")
        
        with versioned_path.open('w', encoding='utf-8') as f:
            json.dump(self.game_data, f, indent=4, ensure_ascii=False)
        
        self.logger.info(f"Game data exported to {versioned_path}")
        return versioned_path

def main():
    """
    Comprehensive example of GameDataManager usage.
    """
    try:
        data_manager = GameDataManager(log_level=logging.DEBUG)
        
        # Demonstrate various data management capabilities
        sword = data_manager.get_item('basic_sword')
        print(f"Sword details: {sword}")
        
        weapons = data_manager.find_items_by_type('weapon')
        print(f"Total weapons: {len(weapons)}")
        
        # Export current game data
        export_file = data_manager.export_game_data()
        print(f"Exported game data to: {export_file}")
        
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    main()