import requests
from bs4 import BeautifulSoup
import re

def fetch_page(url: str):
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    response = requests.get(url, headers=headers, timeout=30)
    response.raise_for_status()
    return response.text

def debug_grid_structure(url: str):
    print(f"\n=== Debugging {url} ===")
    html = fetch_page(url)
    soup = BeautifulSoup(html, "html.parser")
    
    # Look for any mention of "Grid" in the HTML
    print("\n--- Searching for 'Grid' text ---")
    grid_elements = soup.find_all(string=re.compile(r'Grid', re.IGNORECASE))
    for i, elem in enumerate(grid_elements[:5]):  # Show first 5 matches
        parent = elem.parent if elem.parent else None
        print(f"{i+1}. Text: '{elem.strip()}'")
        print(f"   Parent tag: {parent.name if parent else 'None'}")
        print(f"   Parent attrs: {parent.attrs if parent else 'None'}")
        print(f"   HTML context: {str(parent)[:200] if parent else 'None'}...")
        print()
    
    # Look for all th elements
    print("\n--- All <th> elements ---")
    th_elements = soup.find_all('th')
    for i, th in enumerate(th_elements[:10]):  # Show first 10
        print(f"{i+1}. <th>: '{th.get_text().strip()}'")
    
    # Look for images that might be grid-related
    print("\n--- Images with 'cell', 'star', or 'diamond' ---")
    grid_images = soup.find_all('img', src=re.compile(r'(cell|star|diamond)', re.IGNORECASE))
    for i, img in enumerate(grid_images[:10]):
        print(f"{i+1}. Image: src='{img.get('src', '')}', alt='{img.get('alt', '')}'")
        parent = img.parent
        print(f"   Parent: {parent.name if parent else 'None'} {parent.attrs if parent else ''}")
        print(f"   Context: {str(parent)[:150] if parent else 'None'}...")
        print()

    # Look for the grid container structure
    print("\n--- Grid container structure ---")
    grid_title = soup.find('div', {'class': 'title'}, string='Grid')
    if grid_title:
        print(f"Found Grid title: {grid_title}")
        # Look at the next few siblings
        current = grid_title.parent
        for i in range(5):
            if current:
                print(f"Level {i}: {current.name} {current.attrs if hasattr(current, 'attrs') else ''}")
                if hasattr(current, 'find_all'):
                    grid_imgs = current.find_all('img', src=re.compile(r'(cell|star|diamond)', re.IGNORECASE))
                    if grid_imgs:
                        print(f"  Contains {len(grid_imgs)} grid images")
                        print(f"  HTML: {str(current)[:300]}...")
                        break
                current = current.parent
            else:
                break

# Test with Crossblades which should have a complex grid
debug_grid_structure("https://backpackbattles.wiki.gg/wiki/Crossblades")
