# Custom Item Development Template

from game_interface import BaseItem
from typing import Dict, Any, Optional

class CustomItemTemplate(BaseItem):
    """
    Template for creating custom items in Backpack Battles.
    
    This template provides a structured approach to defining new item types
    with custom behaviors and interaction rules.
    """

    def __init__(
        self, 
        name: str, 
        description: str, 
        properties: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize a custom item with specific properties.
        
        Args:
            name (str): The name of the item
            description (str): A detailed description of the item
            properties (dict, optional): Additional item-specific properties
        """
        super().__init__(name, description)
        self.properties = properties or {}
        self.validate_properties()

    def validate_properties(self):
        """
        Validate the properties of the custom item.
        
        Implement custom validation logic to ensure item properties
        meet the required constraints.
        """
        # Example validation
        required_keys = ['rarity', 'type']
        for key in required_keys:
            if key not in self.properties:
                raise ValueError(f"Missing required property: {key}")

    def on_use(self, game_state):
        """
        Define the behavior when the item is used in the game.
        
        Args:
            game_state (GameState): Current state of the game
        
        Returns:
            dict: Modifications to the game state after item use
        """
        # Implement custom item usage logic
        # Example: Apply a special effect or transformation
        effect = {}
        
        # Modify game_state based on item's unique properties
        if self.properties.get('effect_type') == 'heal':
            effect['player_health'] = game_state['player_health'] + self.properties.get('heal_amount', 0)
        
        return effect

    def get_interaction_rules(self):
        """
        Define special interaction rules for this item.
        
        Returns:
            list: Custom interaction rules specific to this item
        """
        # Example: Define interaction rules based on item properties
        rules = []
        
        if self.properties.get('has_special_interaction'):
            rules.append({
                'trigger': 'on_combat_start',
                'effect': self.special_combat_interaction
            })
        
        return rules

    def special_combat_interaction(self, combat_state):
        """
        Example of a special interaction method.
        
        Args:
            combat_state (dict): Current state of combat
        
        Returns:
            dict: Modified combat state
        """
        # Implement unique combat interaction logic
        return combat_state

    def __repr__(self):
        """
        Provide a string representation of the custom item.
        
        Returns:
            str: A descriptive string of the item
        """
        return f"CustomItem(name={self.name}, properties={self.properties})"

# Example usage and item creation
def create_example_custom_item():
    """
    Demonstrate how to create a custom item with specific properties.
    
    Returns:
        CustomItemTemplate: A configured custom item instance
    """
    healing_potion = CustomItemTemplate(
        name="Experimental Healing Elixir",
        description="A mysterious potion with potent healing properties",
        properties={
            'rarity': 'rare',
            'type': 'consumable',
            'effect_type': 'heal',
            'heal_amount': 25,
            'has_special_interaction': True
        }
    )
    
    return healing_potion

# Optional: Add type hints for better IDE support and type checking
def validate_custom_item(item: CustomItemTemplate) -> bool:
    """
    Validate a custom item's configuration.
    
    Args:
        item (CustomItemTemplate): The item to validate
    
    Returns:
        bool: Whether the item passes validation
    """
    try:
        item.validate_properties()
        return True
    except ValueError:
        return False