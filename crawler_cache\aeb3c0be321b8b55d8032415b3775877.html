<!DOCTYPE html>
<html class="client-nojs view-unknown theme-none skin-theme-clientpref-day" lang="en" dir="ltr">
<head>
<meta charset="UTF-8">
<title>Characters - The Backpack Battles Wiki</title>
<script>(function(){var className="client-js view-unknown theme-none skin-theme-clientpref-day";var cookie=document.cookie.match(/(?:^|; )commons_enmwclientpreferences=([^;]+)/);if(cookie){cookie[1].split('%2C').forEach(function(pref){className=className.replace(new RegExp('(^| )'+pref.replace(/-clientpref-\w+$|[^\w-]+/g,'')+'-clientpref-\\w+( |$)'),'$1'+pref+'$2');});}document.documentElement.className=className;}());RLCONF={"wgBreakFrames":false,"wgSeparatorTransformTable":["",""],"wgDigitTransformTable":["",""],"wgDefaultDateFormat":"dmy","wgMonthNames":["","January","February","March","April","May","June","July","August","September","October","November","December"],"wgRequestId":"a27aed6a1732ed77db965422","wgCanonicalNamespace":"","wgCanonicalSpecialPageName":false,"wgNamespaceNumber":0,"wgPageName":"Characters","wgTitle":"Characters","wgCurRevisionId":9157,"wgRevisionId":9157,"wgArticleId":425,"wgIsArticle":true,"wgIsRedirect":false,"wgAction":"view","wgUserName":null,"wgUserGroups":["*"],"wgCategories":["Characters"],"wgPageViewLanguage":"en","wgPageContentLanguage":"en","wgPageContentModel":"wikitext","wgRelevantPageName":"Characters","wgRelevantArticleId":425,"wgIsProbablyEditable":false,"wgRelevantPageIsProbablyEditable":false,"wgRestrictionEdit":[],"wgRestrictionMove":[],"wgRedirectedFrom":"Classes","wgPageFormsTargetName":null,"wgPageFormsAutocompleteValues":[],"wgPageFormsAutocompleteOnAllChars":false,"wgPageFormsFieldProperties":[],"wgPageFormsCargoFields":[],"wgPageFormsDependentFields":[],"wgPageFormsCalendarValues":[],"wgPageFormsCalendarParams":[],"wgPageFormsCalendarHTML":null,"wgPageFormsGridValues":[],"wgPageFormsGridParams":[],"wgPageFormsContLangYes":null,"wgPageFormsContLangNo":null,"wgPageFormsContLangMonths":[],"wgPageFormsHeightForMinimizingInstances":800,"wgPageFormsDelayReload":false,"wgPageFormsShowOnSelect":[],"wgPageFormsScriptPath":"/mw-1.43/extensions/PageForms","edgValues":[],"wgPageFormsEDSettings":null,"wgAmericanDates":false,"wgCargoDefaultQueryLimit":2000,"wgCargoMonthNamesShort":["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],"wgMediaViewerOnClick":true,"wgMediaViewerEnabledByDefault":true,"wgCiteReferencePreviewsActive":true,"wgInternalRedirectTargetUrl":"/wiki/Characters#Playable_Classes","wgCheckUserClientHintsHeadersJsApi":["architecture","bitness","brands","fullVersionList","mobile","model","platform","platformVersion"]};
RLSTATE={"ext.globalCssJs.user.styles":"ready","site.styles":"ready","user.styles":"ready","ext.globalCssJs.user":"ready","user":"ready","user.options":"loading","mediawiki.page.gallery.styles":"ready","skins.vector.styles.legacy":"ready","wgg.skins.vector.styles.search":"ready","ext.CookieWarning.styles":"ready","oojs-ui-core.styles":"ready","oojs-ui.styles.indicators":"ready","mediawiki.widgets.styles":"ready","oojs-ui-core.icons":"ready","ext.removeredlinks.styles":"ready","ext.usergroupbadges.styles":"ready","ext.globalui.styles":"ready"};RLPAGEMODULES=["mediawiki.action.view.redirect","site","mediawiki.page.ready","mediawiki.toc","skins.vector.legacy.js","ext.CookieWarning","ext.removeredlinks","ext.themes.switcher","ext.gadget.interwikiDropdownButton","mmv.bootstrap","ext.checkUser.clientHints","ext.cargo.purge","ext.globalui"];</script>
<script>(RLQ=window.RLQ||[]).push(function(){mw.loader.impl(function(){return["user.options@12s5i",function($,jQuery,require,module){mw.user.tokens.set({"patrolToken":"+\\","watchToken":"+\\","csrfToken":"+\\"});
}];});});</script>
<link rel="stylesheet" href="/load.php?lang=en&amp;modules=ext.CookieWarning.styles%7Cext.globalui.styles%7Cext.removeredlinks.styles%7Cext.usergroupbadges.styles%7Cmediawiki.page.gallery.styles%7Cmediawiki.widgets.styles%7Coojs-ui-core.icons%2Cstyles%7Coojs-ui.styles.indicators%7Cskins.vector.styles.legacy%7Cwgg.skins.vector.styles.search&amp;only=styles&amp;skin=vector">
<script async="" src="/load.php?lang=en&amp;modules=startup&amp;only=scripts&amp;raw=1&amp;skin=vector"></script>
<meta name="ResourceLoaderDynamicStyles" content="">
<link rel="stylesheet" href="/load.php?lang=en&amp;modules=site.styles&amp;only=styles&amp;skin=vector">
<script async src="/load.php?lang=en&skin=vector&modules=ext.themes.apply&only=scripts&skin=vector&raw=1"></script>
<meta name="generator" content="MediaWiki 1.43.1">
<meta name="robots" content="max-image-preview:standard">
<meta name="format-detection" content="telephone=no">
<meta name="description" content="This is a list of all of the characters in Backpack Battles:&#10;All classes have the same starting health, gold, as well as gold gained per round.&#10;Main article: Shopkeeper&#10;Shopkeeper Furcifer will give various tips if clicked.&#10;Main article: Chestnut&#10;Chestnut is a chest sitting right next to the Shopkeeper...">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, minimum-scale=0.25, maximum-scale=5.0">
<style type="text/css">@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/cyrillic-ext/wght/normal.woff2);unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/latin/wght/normal.woff2);unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/cyrillic/wght/normal.woff2);unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/latin-ext/wght/normal.woff2);unicode-range:U+0100-02AF,U+0304,U+0308,U+0329,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/vietnamese/wght/normal.woff2);unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;font-display:swap;}</style>
<link rel="apple-touch-icon" href="/images/4/4a/Site-favicon.ico">
<link rel="icon" href="/images/4/4a/Site-favicon.ico">
<link rel="search" type="application/opensearchdescription+xml" href="/rest.php/v1/search" title="Backpack Battles Wiki (en)">
<link rel="EditURI" type="application/rsd+xml" href="https://backpackbattles.wiki.gg/api.php?action=rsd">
<link rel="canonical" href="https://backpackbattles.wiki.gg/wiki/Characters#Playable_Classes">
<link rel="license" href="https://creativecommons.org/licenses/by-sa/4.0">
<link rel="alternate" type="application/atom+xml" title="Backpack Battles Wiki Atom feed" href="/wiki/Special:RecentChanges?feed=atom">
	<meta property="og:type" content="article">

	<meta property="og:site_name" content="Backpack Battles Wiki">

	<meta property="og:title" content="Characters">

	<meta property="og:description" content="This is a list of all of the characters in Backpack Battles:&#10;All classes have the same starting health, gold, as well as gold gained per round.&#10;Main article: Shopkeeper&#10;Shopkeeper Furcifer will give various tips if clicked.&#10;Main article: Chestnut&#10;Chestnut is a chest sitting right next to the Shopkeeper...">

	<meta property="og:url" content="https://backpackbattles.wiki.gg/wiki/Characters#Playable_Classes">

<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-P68LVFKN');</script>
<!-- End Google Tag Manager -->
</head>
<body class="wgg-dom-version-1_43 skin-vector-legacy mediawiki ltr sitedir-ltr mw-hide-empty-elt ns-0 ns-subject page-Characters rootpage-Characters skin-vector action-view skin--responsive"><header id="wgg-netbar">
	<div class="wgg-netbar__left">
		<div class="wgg-netbar__logo">
			<a href="/" aria-label="wiki.gg">
				<img height=25 alt="wiki.gg logo" src="https://commons.wiki.gg/images/2/27/Network_header_logo.svg?feeba7">
			</a>
		</div>
	</div>

	<nav class="wgg-netbar__user-links" aria-label="Personal tools">
		<div id="p-personal" class="mw-portlet mw-portlet-personal"  >
		    
		    <ul>
		            <li id="pt-createaccount" class="mw-list-item wgg-netbar__desktop-only"><a data-mw="interface" class=""  href="/wiki/Special:CreateAccount?returnto=Characters" title="You are encouraged to create an account and log in; however, it is not mandatory"><span>Create account</span></a></li>
		            <li id="pt-login" class="mw-list-item wgg-netbar__desktop-only"><a data-mw="interface" class=""  href="/wiki/Special:UserLogin?returnto=Characters" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o"><span>Log in</span></a></li>
		    </ul>
		    
		</div>

		<div id="wgg-user-menu-overflow" class="wgg-netbar__dropdown wgg-netbar__mobile-only">
			<input type="checkbox"
				id="wgg-user-menu-overflow-checkbox"
				role="button"
				aria-haspopup="true"
				aria-labelledby="wgg-user-menu-overflow-label"
			>
			<label
				id="wgg-user-menu-overflow-label"
		        for="wgg-user-menu-overflow-checkbox"
				class="wgg-netbar__icon-button"
			><span class="wgg-netbar-icon wgg-netbar-icon--overflow"></span><span>Toggle personal tools menu</span></label>

			<div class="wgg-netbar__dropdown-content">
				<div id="p-user-menu" class="mw-portlet mw-portlet-user-menu"  >
				    
				    <ul>
				            <li id="pt-createaccount-overflow" class="mw-list-item wgg-netbar__mobile-only"><a data-mw="interface" class=""  href="/wiki/Special:CreateAccount?returnto=Characters" title="You are encouraged to create an account and log in; however, it is not mandatory"><span>Create account</span></a></li>
				            <li id="pt-login-overflow" class="mw-list-item wgg-netbar__mobile-only"><a data-mw="interface" class=""  href="/wiki/Special:UserLogin?returnto=Characters" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o"><span>Log in</span></a></li>
				    </ul>
				    
				</div>
			</div>
		</div>
</nav>

</header>

<div id="mw-page-base" class="noprint"></div>
<div id="mw-head-base" class="noprint"></div>
<div class="content-wrapper">
	<div id="mw-navigation">
		<h2>Navigation menu</h2>
		<div id="mw-head">
			<div id="left-navigation">
				
<div id="p-namespaces" class="mw-portlet mw-portlet-namespaces vector-menu-tabs vector-menu-tabs-legacy vectorTabs vector-menu" aria-labelledby="p-namespaces-label"  >
	<h3
		id="p-namespaces-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">Namespaces</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="ca-nstab-main" class="selected mw-list-item"><a href="/wiki/Characters" title="View the content page [c]" accesskey="c"><span>Page</span></a></li><li id="ca-talk" class="new mw-list-item"><a href="/wiki/Talk:Characters?action=edit&amp;redlink=1" rel="discussion" class="new" title="Discussion about the content page (page does not exist) [t]" accesskey="t"><span>Discussion</span></a></li>
		</ul>
		
	</div>
</div>

				
<div id="p-variants" class="mw-portlet mw-portlet-variants emptyPortlet vector-menu-dropdown vector-menu" aria-labelledby="p-variants-label"  >
	<input type="checkbox"
		id="p-variants-checkbox"
		role="button"
		aria-haspopup="true"
		data-event-name="ui.dropdown-p-variants"
		class="vector-menu-checkbox"
		aria-labelledby="p-variants-label"
	>
	<label
		id="p-variants-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">English</span>
	</label>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			
		</ul>
		
	</div>
</div>

			</div>
			<div id="right-navigation">
				
<div id="p-views" class="mw-portlet mw-portlet-views vector-menu-tabs vector-menu-tabs-legacy vectorTabs vector-menu" aria-labelledby="p-views-label"  >
	<h3
		id="p-views-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">Views</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="ca-view" class="selected mw-list-item"><a href="/wiki/Characters"><span>Read</span></a></li><li id="ca-edit" class="mw-list-item"><a href="/wiki/Special:CreateAccount?warning=accountrequiredtoedit&amp;returnto=Characters&amp;returntoquery=action%3Dedit" title="Edit this page [e]" accesskey="e"><span>Sign up to edit</span></a></li><li id="ca-viewsource" class="mw-list-item"><a href="/wiki/Characters?action=edit" title="This page is protected.&#10;You can view its source [e]" accesskey="e"><span>View source</span></a></li><li id="ca-history" class="mw-list-item"><a href="/wiki/Characters?action=history" title="Past revisions of this page [h]" accesskey="h"><span>History</span></a></li>
		</ul>
		
	</div>
</div>

				
<div id="p-cactions" class="mw-portlet mw-portlet-cactions vectorMenu vector-menu-dropdown vector-menu" aria-labelledby="p-cactions-label"  title="More options" >
	<input type="checkbox"
		id="p-cactions-checkbox"
		role="button"
		aria-haspopup="true"
		data-event-name="ui.dropdown-p-cactions"
		class="vector-menu-checkbox"
		aria-labelledby="p-cactions-label"
	>
	<label
		id="p-cactions-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">More</span>
	</label>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="ca-cargo-purge" class="mw-list-item"><a href="/wiki/Characters?action=purge"><span>Purge cache</span></a></li>
		</ul>
		
	</div>
</div>

				
<div id="p-search" role="search" class="vector-search-box-vue  vector-search-box-show-thumbnail vector-search-box-auto-expand-width vector-search-box">
	<h3 >Search</h3>
	<form action="/wiki/Special:Search" id="searchform" class="vector-search-box-form">
		<div id="simpleSearch"
			class="vector-search-box-inner"
			 data-search-loc="header-navigation">
			<input class="vector-search-box-input"
				 type="search" name="search" placeholder="Search Backpack Battles Wiki" aria-label="Search Backpack Battles Wiki" autocapitalize="sentences" spellcheck="false" title="Search Backpack Battles Wiki [f]" accesskey="f" id="searchInput"
			>
			<input id="mw-searchButton"
				 class="searchButton mw-fallbackSearchButton" type="submit" name="fulltext" title="Search the pages for this text" value="Search">
			<input id="searchButton"
				 class="searchButton" type="submit" name="go" title="Go to a page with this exact name if it exists" value="Go">
		</div>
	</form>
</div>

			</div>
		</div>
		
<div id="mw-panel">
	<div id="p-logo" role="banner">
		<a class="mw-wiki-logo" href="/"
			title="Visit the main page"></a>
	</div>
	
<div id="p-Content" class="mw-portlet mw-portlet-Content vector-menu-portal portal vector-menu" aria-labelledby="p-Content-label"  >
	<h3
		id="p-Content-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">Content</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="n-mainpage-description" class="mw-list-item"><a href="/" title="Visit the main page [z]" accesskey="z"><span>Main page</span></a></li><li id="n-Backpack-Battles" class="mw-list-item"><a href="/wiki/Backpack_Battles"><span>Backpack Battles</span></a></li><li id="n-Items" class="mw-list-item"><a href="/wiki/Items"><span>Items</span></a></li><li id="n-Game-Mechanics" class="mw-list-item"><a href="/wiki/Game_Mechanics"><span>Game Mechanics</span></a></li><li id="n-Characters" class="mw-list-item"><a href="/wiki/Characters"><span>Characters</span></a></li><li id="n-Version-History" class="mw-list-item"><a href="/wiki/Version_History"><span>Version History</span></a></li>
		</ul>
		
	</div>
</div>

	
<div id="p-navigation" class="mw-portlet mw-portlet-navigation vector-menu-portal portal vector-menu" aria-labelledby="p-navigation-label"  >
	<h3
		id="p-navigation-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">Navigation</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="n-portal" class="mw-list-item"><a href="/wiki/Backpack_Battles_Wiki:Community_portal" title="About the project, what you can do, where to find things"><span>Community portal</span></a></li><li id="n-recentchanges" class="mw-list-item"><a href="/wiki/Special:RecentChanges" title="A list of recent changes in the wiki [r]" accesskey="r"><span>Recent changes</span></a></li><li id="n-randompage" class="mw-list-item"><a href="/wiki/Special:Random" title="Load a random page [x]" accesskey="x"><span>Random page</span></a></li><li id="n-help-mediawiki" class="mw-list-item"><a href="https://www.mediawiki.org/wiki/Special:MyLanguage/Help:Contents"><span>Help about MediaWiki</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-tb" class="mw-portlet mw-portlet-tb vector-menu-portal portal vector-menu" aria-labelledby="p-tb-label"  >
	<h3
		id="p-tb-label"
		
		class="vector-menu-heading "
	>
		<span class="vector-menu-heading-label">Tools</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="t-whatlinkshere" class="mw-list-item"><a href="/wiki/Special:WhatLinksHere/Characters" title="A list of all wiki pages that link here [j]" accesskey="j"><span>What links here</span></a></li><li id="t-recentchangeslinked" class="mw-list-item"><a href="/wiki/Special:RecentChangesLinked/Characters" rel="nofollow" title="Recent changes in pages linked from this page"><span>Related changes</span></a></li><li id="t-newpage" class="mw-list-item"><a href="/wiki/Special:NewPage" accesskey="]"><span>New page</span></a></li><li id="t-specialpages" class="mw-list-item"><a href="/wiki/Special:SpecialPages" title="A list of all special pages [q]" accesskey="q"><span>Special pages</span></a></li><li id="t-print" class="mw-list-item"><a href="javascript:print();" rel="alternate" title="Printable version of this page [p]" accesskey="p"><span>Printable version</span></a></li><li id="t-info" class="mw-list-item"><a href="/wiki/Characters?action=info" title="More information about this page"><span>Page information</span></a></li><li id="t-cargopagevalueslink" class="mw-list-item"><a href="/wiki/Characters?action=pagevalues" rel="cargo-pagevalues"><span>Cargo data</span></a></li>
		</ul>
		
	</div>
</div>

	
</div>

	</div>
	<div id="content" class="mw-body" role="main">
		<aside id="wikigg-sl-header" data-mw="WggShowcaseLayout">
        <div
            class="wikigg-showcase__unit wikigg-showcase__reserved--lb"
            data-wgg-unit-type="internal"
        ></div>
</aside>

		<div class="content-body">
			<main>
				<a id="top"></a>
				<div id="siteNotice"></div>
				<div class="mw-indicators">
				</div>
				<h1 id="firstHeading" class="firstHeading mw-first-heading"><span class="mw-page-title-main">Characters</span></h1>
				<div id="bodyContent" class="vector-body">
					<div id="siteSub" class="noprint">From Backpack Battles Wiki</div>
					<div id="contentSub"><span class="mw-redirectedfrom">(Redirected from <a href="/wiki/Classes?redirect=no" class="mw-redirect" title="Classes">Classes</a>)</span></div>
					<div id="contentSub2"></div>
					
					<div id="jump-to-nav"></div>
					<a class="mw-jump-link" href="#mw-head">Jump to navigation</a>
					<a class="mw-jump-link" href="#searchInput">Jump to search</a>
					<div id="mw-content-text" class="mw-body-content"><div class="mw-content-ltr mw-parser-output" lang="en" dir="ltr"><div style="float:right; clear:right; margin-bottom:.5em; padding:.5em 0 .8em 1.4em; background:transparent; max-width:20em;"><div id="toc" class="toc" role="navigation" aria-labelledby="mw-toc-heading"><input type="checkbox" role="button" id="toctogglecheckbox" class="toctogglecheckbox" style="display:none" /><div class="toctitle" lang="en" dir="ltr"><h2 id="mw-toc-heading">Contents</h2><span class="toctogglespan"><label class="toctogglelabel" for="toctogglecheckbox"></label></span></div>
<ul>
<li class="toclevel-1 tocsection-1"><a href="#Playable_Classes"><span class="tocnumber">1</span> <span class="toctext">Playable Classes</span></a></li>
<li class="toclevel-1 tocsection-2"><a href="#Other_Characters"><span class="tocnumber">2</span> <span class="toctext">Other Characters</span></a>
<ul>
<li class="toclevel-2 tocsection-3"><a href="#Shopkeeper"><span class="tocnumber">2.1</span> <span class="toctext">Shopkeeper</span></a></li>
<li class="toclevel-2 tocsection-4"><a href="#Chestnut"><span class="tocnumber">2.2</span> <span class="toctext">Chestnut</span></a></li>
</ul>
</li>
</ul>
</div>
</div>
<p>This is a list of all of the <b>characters</b> in <i><a href="/wiki/Backpack_Battles" title="Backpack Battles">Backpack Battles</a></i>:
</p>
<h2><span class="mw-headline" id="Playable_Classes">Playable Classes</span></h2>
<p>All classes have the same starting health, gold, as well as gold gained per round.
</p>
<ul class="gallery mw-gallery-nolines">
		<li class="gallerybox" style="width: 185px"><div style="width: 185px">
			<div class="thumb" style="width: 180px;"><div style="margin:0px auto;"><a href="/wiki/Ranger" title="Ranger"><img alt="Ranger" src="/images/thumb/e/e9/Ranger.png/130px-Ranger.png?a7e08c" decoding="async" loading="lazy" width="130" height="180" data-file-width="722" data-file-height="1000" /></a></div></div>
			<div class="gallerytext"><a href="/wiki/Ranger" title="Ranger">Ranger</a></div>
		</div></li>
		<li class="gallerybox" style="width: 185px"><div style="width: 185px">
			<div class="thumb" style="width: 180px;"><div style="margin:0px auto;"><a href="/wiki/Reaper" title="Reaper"><img alt="Reaper" src="/images/thumb/6/6a/Reaper.png/177px-Reaper.png?9671b4" decoding="async" loading="lazy" width="177" height="180" data-file-width="1000" data-file-height="1015" /></a></div></div>
			<div class="gallerytext"><a href="/wiki/Reaper" title="Reaper">Reaper</a></div>
		</div></li>
		<li class="gallerybox" style="width: 185px"><div style="width: 185px">
			<div class="thumb" style="width: 180px;"><div style="margin:0px auto;"><a href="/wiki/Pyromancer" title="Pyromancer"><img alt="Pyromancer" src="/images/thumb/7/78/Pyromancer.png/123px-Pyromancer.png?b2f788" decoding="async" loading="lazy" width="123" height="180" data-file-width="889" data-file-height="1297" /></a></div></div>
			<div class="gallerytext"><a href="/wiki/Pyromancer" title="Pyromancer">Pyromancer</a></div>
		</div></li>
		<li class="gallerybox" style="width: 185px"><div style="width: 185px">
			<div class="thumb" style="width: 180px;"><div style="margin:0px auto;"><a href="/wiki/Berserker" title="Berserker"><img alt="Berserker" src="/images/thumb/a/a8/Berserker.png/99px-Berserker.png?24e32e" decoding="async" loading="lazy" width="99" height="180" data-file-width="580" data-file-height="1052" /></a></div></div>
			<div class="gallerytext"><a href="/wiki/Berserker" title="Berserker">Berserker</a></div>
		</div></li>
</ul>
<h2><span class="mw-headline" id="Other_Characters">Other Characters</span></h2>
<h4><span class="mw-headline" id="Shopkeeper">Shopkeeper</span></h4>
<div class="thumb tright"><div class="thumbinner" style="width:202px;"><a href="/wiki/File:Shopkeeper.png" class="image"><img src="/images/thumb/5/5b/Shopkeeper.png/200px-Shopkeeper.png?973694" decoding="async" loading="lazy" width="200" height="220" class="thumbimage" data-file-width="605" data-file-height="665" /></a>  <div class="thumbcaption"><div class="magnify"><a href="/wiki/File:Shopkeeper.png" class="internal" title="Enlarge"></a></div>Shopkeeper Furcifer</div></div></div>
<dl><dd><i>Main article: <a href="/wiki/Shopkeeper" class="mw-redirect" title="Shopkeeper">Shopkeeper</a></i></dd></dl>
<p>Shopkeeper Furcifer will give various tips if clicked.
</p>
<div style="clear:right"></div>
<h4><span class="mw-headline" id="Chestnut">Chestnut</span></h4>
<div class="thumb tright"><div class="thumbinner" style="width:102px;"><a href="/wiki/File:Chestnut.png" class="image"><img src="/images/thumb/4/47/Chestnut.png/100px-Chestnut.png?c5f9b9" decoding="async" loading="lazy" width="100" height="133" class="thumbimage" data-file-width="379" data-file-height="504" /></a>  <div class="thumbcaption"><div class="magnify"><a href="/wiki/File:Chestnut.png" class="internal" title="Enlarge"></a></div>Chestnut</div></div></div>
<dl><dd><i>Main article: <a href="/wiki/Chestnut" title="Chestnut">Chestnut</a></i></dd></dl>
<p>Chestnut is a chest sitting right next to the Shopkeeper. Chestnut devours items dragged onto it and gives half of theirs price (rounded up) back as gold coins.
</p>
<div style="clear:right"></div>
<!-- 
NewPP limit report
Cached time: 20250613072200
Cache expiry: 2592000
Reduced expiry: false
Complications: [show‐toc]
CPU time usage: 0.032 seconds
Real time usage: 0.193 seconds
Preprocessor visited node count: 41/1000000
Revision size: 916/4194304 bytes
Post‐expand include size: 201/4194304 bytes
Template argument size: 10/4194304 bytes
Highest expansion depth: 3/100
Expensive parser function count: 0/500
Unstrip recursion depth: 0/20
Unstrip post‐expand size: 1883/10000000 bytes
-->
<!--
Transclusion expansion time report (%,ms,calls,template)
100.00%  116.176      1 -total
 15.73%   18.278      1 Template:Tocright
 15.48%   17.984      2 Template:Clear
-->

<!-- Saved in parser cache with key backpackbattles_en:pcache:idhash:425-0!canonical and timestamp 20250613072200 and revision id 9157. Rendering was triggered because: page-view
 -->
</div>
<div class="printfooter" data-nosnippet="">Retrieved from "<a dir="ltr" href="https://backpackbattles.wiki.gg/wiki/Characters?oldid=9157#Playable_Classes">https://backpackbattles.wiki.gg/wiki/Characters?oldid=9157#Playable_Classes</a>"</div></div>
					<div id="catlinks" class="catlinks" data-mw="interface"><div id="mw-normal-catlinks" class="mw-normal-catlinks"><a href="/wiki/Special:Categories" title="Special:Categories">Category</a>: <ul><li><a href="/wiki/Category:Characters" title="Category:Characters">Characters</a></li></ul></div></div>
				</div>
			</main>
			<aside id="wikigg-sl-rail" data-mw="WggShowcaseLayout">
        <div
            class="wikigg-showcase__unit wikigg-showcase__reserved--mrec"
            data-wgg-unit-type="pubco"
        ><div id="nn_mpu1"></div></div>
        <div
            class="wikigg-showcase__unit wikigg-showcase__reserved--mrec"
            data-wgg-unit-type="internal"
        ></div>
        <div
            class="wikigg-showcase__unit wikigg-showcase__reserved--mrec"
            data-wgg-unit-type="pubco"
        ><div id="nn_mpu2"></div></div>
</aside>

		</div>
		<aside id="wikigg-sl-footer" data-mw="WggShowcaseLayout">
        <div
            class="wikigg-showcase__unit wikigg-showcase__reserved--lb"
            data-wgg-unit-type="internal"
        ></div>
</aside>

	</div>
</div>
<div id='mw-data-after-content'>
	<div class="mw-cookiewarning-container"><div class="mw-cookiewarning-text"><span>Cookies help us deliver our services. By using our services, you agree to our use of cookies.</span></div><form method="POST"><div class='oo-ui-layout oo-ui-horizontalLayout'><span class='oo-ui-widget oo-ui-widget-enabled oo-ui-buttonElement oo-ui-buttonElement-framed oo-ui-labelElement oo-ui-flaggedElement-progressive oo-ui-buttonWidget'><a role='button' tabindex='0' href='https://www.indie.io/privacy-policy' rel='nofollow' class='oo-ui-buttonElement-button'><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon oo-ui-image-progressive'></span><span class='oo-ui-labelElement-label'>More information</span><span class='oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator oo-ui-image-progressive'></span></a></span><span class='oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-buttonElement oo-ui-buttonElement-framed oo-ui-labelElement oo-ui-flaggedElement-primary oo-ui-flaggedElement-progressive oo-ui-buttonInputWidget'><button type='submit' tabindex='0' name='disablecookiewarning' value='OK' class='oo-ui-inputWidget-input oo-ui-buttonElement-button'><span class='oo-ui-iconElement-icon oo-ui-iconElement-noIcon oo-ui-image-invert'></span><span class='oo-ui-labelElement-label'>OK</span><span class='oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator oo-ui-image-invert'></span></button></span></div></form></div>
</div>


<footer id="footer" class="mw-footer" >
	<ul id="footer-info">
	<li id="footer-info-lastmod"> This page was last edited on 5 May 2024, at 09:01.</li>
	<li id="footer-info-copyright"><div class="wikigg-copyright-box">Pages that were created prior to November 2023 are adapted from the Backpack Battles Fandom wiki.<br />Page content is under <a class="external" rel="nofollow" href="https://creativecommons.org/licenses/by-sa/4.0">Creative Commons Attribution-ShareAlike 4.0 License</a> unless otherwise noted.</div></li>
</ul>

	<ul id="footer-places">
	<li id="footer-places-wikigg-tos"><a href="https://www.indie.io/terms-of-service">Terms of Service</a></li>
	<li id="footer-places-wikigg-privacy"><a href="https://www.indie.io/privacy-policy">Privacy policy</a></li>
	<li id="footer-places-wikigg-support"><a href="https://support.wiki.gg">Support Wiki</a></li>
	<li id="footer-places-wikigg-servicedesk"><a href="https://wiki.gg/go/servicedesk">Send a ticket to wiki.gg</a></li>
	<li id="footer-places-wikigg-statuspage"><a href="https://wikiggstatus.com">Status page</a></li>
	<li id="footer-places-wikigg-pcmp"><a href="#" class="nn-cmp-show" style="display: none">Manage cookie settings</a></li>
</ul>

	<ul id="footer-icons" class="noprint">
	<li id="footer-copyrightico"><a href="https://creativecommons.org/licenses/by-sa/4.0" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><img src="https://commons.wiki.gg/images/1/14/CC-BY-SA_footer_badge.svg?d931d3" alt="Creative Commons Attribution-ShareAlike 4.0 License" height="31" width="88" loading="lazy" data-theme="light"><img src="https://commons.wiki.gg/images/7/71/CC-BY-SA_footer_badge_dark.svg?55845c" alt="Creative Commons Attribution-ShareAlike 4.0 License" height="31" width="88" loading="lazy" data-theme="dark"></a></li>
	<li id="footer-poweredbyico"><a href="https://www.mediawiki.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><img src="/mw-1.43/resources/assets/poweredby_mediawiki.svg" alt="Powered by MediaWiki" width="88" height="31" loading="lazy" data-theme="light"><img src="https://commons.wiki.gg/images/1/1c/MediaWiki_footer_badge_dark.svg?12ec0a" alt="Powered by MediaWiki" width="88" height="31" loading="lazy" data-theme="dark"></a></li>
	<li id="footer-partofico"><a href="https://wiki.gg" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><img src="https://commons.wiki.gg/images/d/d1/Network_footer_badge.svg?9d5a96" alt="Part of wiki.gg" height="31" width="88" loading="lazy" data-theme="light"><img src="https://commons.wiki.gg/images/2/23/Network_footer_badge_dark.svg?9cf3e8" alt="Part of wiki.gg" height="31" width="88" loading="lazy" data-theme="dark"></a></li>
</ul>

</footer>


<script>(RLQ=window.RLQ||[]).push(function(){mw.config.set({"wgBackendResponseTime":130,"wgPageParseReport":{"limitreport":{"cputime":"0.032","walltime":"0.193","ppvisitednodes":{"value":41,"limit":1000000},"revisionsize":{"value":916,"limit":4194304},"postexpandincludesize":{"value":201,"limit":4194304},"templateargumentsize":{"value":10,"limit":4194304},"expansiondepth":{"value":3,"limit":100},"expensivefunctioncount":{"value":0,"limit":500},"unstrip-depth":{"value":0,"limit":20},"unstrip-size":{"value":1883,"limit":10000000},"timingprofile":["100.00%  116.176      1 -total"," 15.73%   18.278      1 Template:Tocright"," 15.48%   17.984      2 Template:Clear"]},"cachereport":{"timestamp":"20250613072200","ttl":2592000,"transientcontent":false}}});});</script>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'950255760900d8ab',t:'MTc0OTk5MzMwMS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script><script defer src="https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon='{"rayId":"950255760900d8ab","serverTiming":{"name":{"cfExtPri":true,"cfEdge":true,"cfOrigin":true,"cfL4":true,"cfSpeedBrain":true,"cfCacheStatus":true}},"version":"2025.6.2","token":"26b2c6e7c852417d8f9d11b0c7f02309"}' crossorigin="anonymous"></script>
</body>
</html>