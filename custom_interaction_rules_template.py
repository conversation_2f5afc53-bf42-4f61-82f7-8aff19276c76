# Custom Interaction Rules Template

from typing import Dict, Any, Callable
from game_interface import GameState

class CustomInteractionRule:
    """
    Template for creating custom interaction rules in Backpack Battles.
    
    This template provides a structured approach to defining complex game
    interaction rules with flexible trigger conditions and effects.
    """

    def __init__(
        self, 
        name: str, 
        trigger_conditions: Callable[[GameState], bool],
        effect: Callable[[GameState], GameState]
    ):
        """
        Initialize a custom interaction rule.
        
        Args:
            name (str): Unique identifier for the interaction rule
            trigger_conditions (callable): Function to check if rule can be applied
            effect (callable): Function to modify game state when rule is triggered
        """
        self.name = name
        self.trigger_conditions = trigger_conditions
        self.effect = effect
        self.metadata: Dict[str, Any] = {}

    def can_trigger(self, game_state: GameState) -> bool:
        """
        Check if the interaction rule can be applied to the current game state.
        
        Args:
            game_state (GameState): Current state of the game
        
        Returns:
            bool: Whether the rule can be triggered
        """
        try:
            return self.trigger_conditions(game_state)
        except Exception as e:
            # Log and handle any errors in condition checking
            print(f"Error in trigger conditions for {self.name}: {e}")
            return False

    def apply_effect(self, game_state: GameState) -> GameState:
        """
        Apply the interaction rule's effect to the game state.
        
        Args:
            game_state (GameState): Current state of the game
        
        Returns:
            GameState: Modified game state after applying the effect
        """
        try:
            return self.effect(game_state)
        except Exception as e:
            # Log and handle any errors in effect application
            print(f"Error applying effect for {self.name}: {e}")
            return game_state

    def add_metadata(self, key: str, value: Any):
        """
        Add additional metadata to the interaction rule.
        
        Args:
            key (str): Metadata key
            value (Any): Metadata value
        """
        self.metadata[key] = value

    def __repr__(self):
        """
        Provide a string representation of the interaction rule.
        
        Returns:
            str: A descriptive string of the interaction rule
        """
        return f"CustomInteractionRule(name={self.name}, metadata={self.metadata})"

# Example interaction rule creation functions
def create_example_interaction_rules():
    """
    Demonstrate how to create custom interaction rules with various conditions.
    
    Returns:
        list: A collection of example CustomInteractionRule instances
    """
    def low_health_trigger(game_state: GameState) -> bool:
        """
        Example trigger condition: Check if player health is below a threshold.
        
        Args:
            game_state (GameState): Current game state
        
        Returns:
            bool: Whether player health is low
        """
        return game_state.get('player_health', 100) < 30

    def heal_on_low_health(game_state: GameState) -> GameState:
        """
        Example effect: Automatically heal player when health is low.
        
        Args:
            game_state (GameState): Current game state
        
        Returns:
            GameState: Game state with increased player health
        """
        healing_amount = 25
        game_state['player_health'] = min(
            game_state.get('player_health', 0) + healing_amount, 
            100  # Maximum health cap
        )
        return game_state

    def rare_item_bonus_trigger(game_state: GameState) -> bool:
        """
        Example trigger condition: Check for rare item presence.
        
        Args:
            game_state (GameState): Current game state
        
        Returns:
            bool: Whether a rare item is in inventory
        """
        return any(
            item.get('rarity') == 'rare' 
            for item in game_state.get('inventory', [])
        )

    def apply_rare_item_bonus(game_state: GameState) -> GameState:
        """
        Example effect: Apply bonus for having rare items.
        
        Args:
            game_state (GameState): Current game state
        
        Returns:
            GameState: Game state with applied rare item bonus
        """
        game_state['score_multiplier'] = 1.5
        return game_state

    # Create interaction rules
    low_health_rule = CustomInteractionRule(
        name="AutoHeal",
        trigger_conditions=low_health_trigger,
        effect=heal_on_low_health
    )
    low_health_rule.add_metadata('priority', 'high')

    rare_item_bonus_rule = CustomInteractionRule(
        name="RareItemBonus",
        trigger_conditions=rare_item_bonus_trigger,
        effect=apply_rare_item_bonus
    )
    rare_item_bonus_rule.add_metadata('description', 'Bonus for collecting rare items')

    return [low_health_rule, rare_item_bonus_rule]

# Utility function for validating interaction rules
def validate_interaction_rule(rule: CustomInteractionRule) -> bool:
    """
    Validate a custom interaction rule's configuration.
    
    Args:
        rule (CustomInteractionRule): The interaction rule to validate
    
    Returns:
        bool: Whether the interaction rule passes validation
    """
    # Add custom validation logic
    if not rule.name:
        return False
    
    # Optionally add more complex validation
    return True

# Example usage
if __name__ == "__main__":
    example_rules = create_example_interaction_rules()
    for rule in example_rules:
        print(f"Validating rule: {rule}")
        if validate_interaction_rule(rule):
            print("Rule is valid")
        else:
            print("Rule is invalid")