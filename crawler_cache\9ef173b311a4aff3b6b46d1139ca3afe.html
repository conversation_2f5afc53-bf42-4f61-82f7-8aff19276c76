<!doctype html>
<html lang="en" class="js no-touch" itemscope>
	<head>
		<title>Sapiens Craft Puzzle FAQs, Walkthroughs, and Guides for PC - GameFAQs</title>

				<meta http-equiv="content-type" content="text/html;charset=UTF-8" />

				<meta name="description" content="For Sapiens Craft Puzzle on the PC, GameFAQs has game information and a community message board for game discussion." />
		
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta content="origin" id="mref" name="referrer" />

		
		
		
		
				
					  		    <meta name="viewport" content="width=device-width, initial-scale=1.0">
	  
	  		<link id="core_css" href="/a/css/v13.20250306212919.css" rel="stylesheet" type="text/css">
		
		
				<link rel="canonical" href="https://gamefaqs.gamespot.com/pc/429449-sapiens-craft-puzzle/faqs" />
		
		
		
		<meta id="utag-data" name="utag-data" content="{&quot;siteType&quot;:&quot;gamefaqs-desktop&quot;,&quot;siteEdition&quot;:&quot;ww&quot;,&quot;deviceType&quot;:&quot;desktop&quot;,&quot;siteSection&quot;:&quot;Games&quot;,&quot;pageType&quot;:&quot;game_faq_list&quot;,&quot;siteHier&quot;:&quot;Games|Game|FAQs|FAQ List&quot;,&quot;userState&quot;:&quot;non authenticated&quot;,&quot;userType&quot;:&quot;anon&quot;,&quot;userId&quot;:null,&quot;regionCode&quot;:&quot;US&quot;,&quot;pageViewGuid&quot;:&quot;276749a5-10c3-48b4-1a0b-a4fe1f7c2306&quot;,&quot;articleType&quot;:null,&quot;articleTitle&quot;:null,&quot;productId&quot;:&quot;429449&quot;,&quot;productSeriesId&quot;:&quot;605453&quot;,&quot;productName&quot;:&quot;sapiens-craft-puzzle|&quot;,&quot;productGenre&quot;:&quot;Puzzle|General&quot;,&quot;productPlatform&quot;:&quot;PC&quot;,&quot;page_event&quot;:null}"/>
<meta id="ad-settings" content="" data-settings="{&quot;script&quot;:&quot;\/\/www.googletagservices.com\/tag\/js\/gpt.js&quot;,&quot;target_params&quot;:{&quot;game&quot;:&quot;sapiens-craft-puzzle&quot;,&quot;ptype&quot;:&quot;game_faq_list&quot;,&quot;genre&quot;:&quot;Puzzle,General&quot;,&quot;publisher&quot;:&quot;Abra24&quot;,&quot;user&quot;:&quot;anon&quot;,&quot;rdate&quot;:&quot;bm52&quot;},&quot;unit_name&quot;:&quot;\/22309610186\/aw-gamefaqs&quot;,&quot;mapped_units&quot;:{&quot;skybox-nav&quot;:{&quot;size&quot;:[[5,5]],&quot;target&quot;:{&quot;pos&quot;:&quot;nav&quot;}},&quot;omni-skybox-nav&quot;:{&quot;size&quot;:[[5,5],[6,6]],&quot;target&quot;:{&quot;pos&quot;:&quot;nav&quot;}},&quot;leader_plus_top&quot;:{&quot;size&quot;:[[728,90],[970,66],[970,250]],&quot;target&quot;:{&quot;pos&quot;:&quot;top&quot;}},&quot;leader_top&quot;:{&quot;size&quot;:[[728,90],[970,66]],&quot;target&quot;:{&quot;pos&quot;:&quot;top&quot;}},&quot;incontent-leader-middle&quot;:{&quot;size&quot;:[[728,90],[970,66]],&quot;target&quot;:{&quot;pos&quot;:&quot;inc&quot;}},&quot;leader_bottom&quot;:{&quot;size&quot;:[[728,90],[970,66]],&quot;target&quot;:{&quot;pos&quot;:&quot;bottom&quot;}},&quot;mpu_plus_top&quot;:{&quot;size&quot;:[[300,250],[300,600]],&quot;target&quot;:{&quot;pos&quot;:&quot;top&quot;}},&quot;mpu_top&quot;:{&quot;size&quot;:[[300,250]],&quot;target&quot;:{&quot;pos&quot;:&quot;top&quot;}},&quot;mpu_bottom&quot;:{&quot;size&quot;:[[300,250]],&quot;target&quot;:{&quot;pos&quot;:&quot;bottom&quot;}},&quot;native-top&quot;:{&quot;size&quot;:[[11,11],[728,90],[970,66],&quot;fluid&quot;],&quot;target&quot;:{&quot;pos&quot;:&quot;top&quot;}},&quot;incontent-ad&quot;:{&quot;size&quot;:[[5,5],[728,90],[970,66],&quot;fluid&quot;],&quot;target&quot;:{&quot;pos&quot;:&quot;top&quot;}},&quot;incontent-ad-plus&quot;:{&quot;size&quot;:[[5,5],[728,90],[970,66],&quot;fluid&quot;],&quot;target&quot;:{&quot;pos&quot;:&quot;inc&quot;}},&quot;interstitial&quot;:{&quot;outOfPage&quot;:true}}}"/>
<meta id="view-guid-meta" name="view-guid-meta" itemprop="view-guid" content="276749a5-10c3-48b4-1a0b-a4fe1f7c2306"/>
<meta id="bsm-pub-zone-meta" name="bsm-pub-zone-meta" itemprop="bsm-pub-zone" content="gamefaqs.com/nucleus"/>
<meta id="bsm-sizes-meta" name="bsm-sizes-meta" itemprop="bsm-sizes" content="728x90 300x250"/>
<meta id="fandom-meta" name="fandom-meta" itemprop="fandom-context" content=""/>
<meta name="adtags" content="game=sapiens-craft-puzzle&amp;ptype=game_faq_list&amp;genre=Puzzle%2CGeneral&amp;publisher=Abra24&amp;user=anon&amp;rdate=bm52"/>
		
              <script type="text/plain" class="optanon-category-C0002">
			(function(w,d,s,l,i){ w[l]=w[l]||[];w[l].push({ 'gtm.start':new Date().getTime(),event:'gtm.js' });var f=d.getElementsByTagName(s)[0],j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f); })(window,document,'script','dataLayer','GTM-PWVTCD9');
  	   </script>

				<script type="text/javascript" src="/a/js/jquery_gf.20240930203849.js"></script>
		<script type="text/javascript" src="/a/js/gamefaqs.20240930203849.js"></script>
		<script type="text/javascript" src="/a/js/quill.20240930203849.js"></script>
		<script type="text/javascript">var xsrf_key = 'c5958fc7'; var cdn_path = '/a'; var css_date='20250306212919'; var gfuid = '0';</script>

				<script type="text/javascript">
		 function setUpAgknTag(tag){ tag.setBpId("cbsinteractive"); }
		var vguid = $('meta[id=view-guid-meta]').attr("content").substr(0, 24) + Math.random().toString(16).substr(2,12);
		$('meta[id=view-guid-meta]').attr("content", vguid);
		</script>

	  		  	<script src="https://cdn.cookielaw.org/scripttemplates/otSDKStub.js" type="text/javascript" charset="UTF-8" data-domain-script="73588546-b116-4dbc-ab64-9db97e11fc0d"></script>
	  	<link rel="preload" as="script" href="https://www.googletagservices.com/tag/js/gpt.js"/><link rel="preload" as="script" href="https://js-sec.indexww.com/ht/p/183728-151106130411182.js"/><link rel="preload" as="script" href="https://c.amazon-adsystem.com/aax2/apstag.js"/>
	  	<script class="optanon-category-C0004" type="text/plain" src="//js.agkn.com/prod/v0/tag.js" async></script>
	</head>
	<body data-css="" >
		<div id="fullscreen_overlay"></div>
				<div class="wrapper g_gamespace p_429449 s_pc">
						<div id="menubutton" class="menubutton" onclick="topnav_side_open();"><i class="fa fa-bars"></i> Menu</div><div class="sidenav" id="sidenav"><div class="sidenav_div"><div id="menuclose" class="menuclose" onclick="topnav_side_close();"><i class="fa fa-times fa-2x"></i></div><ul class="sidenav_menu"><li><a href="/">Home</a></li><li><a href="/boards">Boards</a></li><li><a href="/news">News</a></li><li><a href="/answers">Q&amp;A</a></li><li><a href="/community">Community</a></li><li><a href="/contribute">Contribute</a></li><li><a href="/games">Games</a></li></ul><br /><ul class="sidenav_menu"><li><a href="/3ds">3DS</a></li><li><a href="/android">Android</a></li><li><a href="/board-card">Board/Card</a></li><li><a href="/iphone">iOS</a></li><li><a href="/pc">PC</a></li><li><a href="/ps3">PlayStation&nbsp;3</a></li><li><a href="/ps4">PlayStation&nbsp;4</a></li><li><a href="/ps5">PlayStation&nbsp;5</a></li><li><a href="/switch">Switch</a></li><li><a href="/switch-2">Switch 2</a></li><li><a href="/vita">Vita</a></li><li><a href="/xbox360">Xbox 360</a></li><li><a href="/xboxone">Xbox One</a></li><li><a href="/xbox-series-x">Xbox Series</a></li><li><a href="/games/systems">More Systems</a></li></ul></div></div><div class="masthead"><div class="masthead_strip"><div class="container row"><nav class="masthead_systems"><a href="/pc">PC</a><a href="/ps4">PS4</a><a href="/ps5">PS5</a><a href="/switch">Switch</a><a href="/switch-2">Switch 2</a><a href="/xboxone">Xbox One</a><a href="/xbox-series-x">Xbox Series</a><span class="masthead_platform_drop"><a class="mast_nav_last" href="/games/systems" >More&nbsp;Systems <i id="sysdrop_down" class="fa fa-caret-down"></i><i id="sysdrop_up" class="fa fa-caret-up"></i></a><ul id="sysdrop" class="masthead_platform_subnav"><li class="masthead_platform_subnav_item"><a href="/3ds">3DS</a></li><li class="masthead_platform_subnav_item"><a href="/android">Android</a></li><li class="masthead_platform_subnav_item"><a href="/board-card">Board / Card</a></li><li class="masthead_platform_subnav_item"><a href="/ds">DS</a></li><li class="masthead_platform_subnav_item"><a href="/gba">Game Boy Advance</a></li><li class="masthead_platform_subnav_item"><a href="/gamecube">GameCube</a></li><li class="masthead_platform_subnav_item"><a href="/iphone">iOS</a></li><li class="masthead_platform_subnav_item"><a href="/n64">Nintendo 64</a></li><li class="masthead_platform_subnav_item"><a href="/ps">PlayStation</a></li><li class="masthead_platform_subnav_item"><a href="/ps2">PlayStation 2</a></li><li class="masthead_platform_subnav_item"><a href="/ps3">PlayStation 3</a></li><li class="masthead_platform_subnav_item"><a href="/psp">PSP</a></li><li class="masthead_platform_subnav_item"><a href="/snes">Super Nintendo</a></li><li class="masthead_platform_subnav_item"><a href="/vita">Vita</a></li><li class="masthead_platform_subnav_item"><a href="/wii">Wii</a></li><li class="masthead_platform_subnav_item"><a href="/wii-u">Wii U</a></li><li class="masthead_platform_subnav_item"><a href="/xbox360">Xbox 360</a></li><li class="masthead_platform_subnav_item"><a href="/games/systems">See All 143...</a></li></ul></span></nav><div class="masthead_user">
	<a href="/user/login" onclick="return show_login();"><i class="fa fa-key nouser"></i>Log In</a>
	<a href="/user/register" onclick="return show_signup('header');" class="nouser">Sign Up</a>
</div>
</div></div><div class="masthead_main container row"><div class="masthead_logo_search"><div class="masthead_logo"><a href="/">GameFAQs</a></div><div class="masthead_search"><div class="search_head"></div><form class="search" method="get" action="/search"><fieldset><button type="button" class="mh_cancel" onclick="topnav_hide_search();"><i id="mh_cancel" class="fa fa-remove"></i></button><button type="submit" class="mh_search"><i class="fa fa-search"></i></button><input type="text" id="searchtextbox" name="game" value="" class="search" placeholder="Search Game Titles" /></fieldset></form></div></div><div class="masthead_nav"><nav><a href="/boards">Boards</a><a href="/news">News</a><a href="/answers">Q&amp;A</a><a class="notab" href="/community">Community</a><a class="navtrim" href="/contribute">Contribute</a><a class="navtrim" href="/games">Games</a><a class="search_link" onclick="topnav_show_search();"><i class="fa fa-2x fa-search" title="Search"></i></a></nav></div></div></div>

<script type="text/javascript">
$(window).resize(function()
{
	dismiss_search_results();
});

$('body :not(.game_selector)').bind('click touch', function(e)
{
	if($('.game_selector').css('display')=='block')
	{
		e.stopPropagation();
				if(($(this).attr('class') || $(this).attr('id')) && $(this).attr('class')!='game_selector' && $(this).attr('id')!='game_search' && $(this).attr('id')!='searchtextbox' && $(this).attr('class')!='head')
		{
			dismiss_search_results();
		}
			}
});

$('#searchtextbox').autocomplete(
{
	minLength: 1,
	delay: 250,
	classes: { 'ui-autocomplete':'game_selector' },
	source: '/ajax/home_game_search?term=' + $('#searchtextbox').val(),
	focus: function(){ return false; },
	close: function(event, ui)
	{
		if($('#searchtextbox').val())
		{
			$('.game_selector').show();
			if($('.game_selector').has('li').length===0)
				dismiss_search_results();
		}
	},
	select: function(event, ui)
	{
		log_site_result(ui.item.row_num, 'game_faq_list');
		window.location = ui.item.url;
	}
})
.data('ui-autocomplete')._renderItem = function(ul, item)
{
	var list;
	if(item.product)
	{
		var contribs = '';
		contribs += '<a class="result_link" href="'+item.url+'/data">Data</a>&nbsp;&nbsp;';
		if(item.has_guides>0)
			contribs += '<a class="result_link" href="'+item.url+'/faqs">Guides</a>&nbsp;&nbsp;';
		if(item.has_cheats>0)
			contribs += '<a class="result_link" href="'+item.url+'/cheats">Cheats</a>&nbsp;&nbsp;';
		contribs += '<a class="result_link" href="'+item.url+'/answers">Q&amp;A</a>&nbsp;&nbsp;';
		contribs += '<a class="result_link" href="'+item.board_url+'">Board</a>';
		if(item.pcnt>5)
			item.plats = item.pcnt + ' platforms';

		list += '<li><div data-row="'+item.row_num+'" data-pid="'+item.pid+'" data-gameid="'+item.game_id+'" data-url="'+item.url+'"><span class="result_img"><img class="search_img imgboxart" src="/a/game/'+item.game_id+'.jpg"/></span><span class="result_title"><a class="bold" href="'+item.url+'">'+item.game_name+'</a><br><span class="sub_title">'+contribs+'</span></span><span class="result_type">'+item.release_date+'<br>'+item.plats+'</span></div></li>';
	}
	else if(item.footer)
	{
		list += '<li><div data-row="'+item.row_num+'" data-url="/search?game='+item.search_string+'" class="dismiss_results"><span class="result_title"><a href="/search?game='+item.search_string+'">'+item.text+'</a></span></div></li>';
	}
	else
	{
		list += '<li><div class="dismiss_results" onclick="dismiss_search_results();">No Results Found - Close</div></li>';
	}
	return $(list).appendTo(ul);
};
</script>
			
			
						
			
									
						<div id="mantle_skin">
													<div class="js-mapped-ad ad ad_leader_top" data-ad-type="leader_top"><div class="ad_wrap "></div></div>
								
								<div id="content" class="container">

															
																<div class="main_content row">
							<div class="span8">
								<header class="page-header"><div class="header_split"><div class="header_left"><div class="header_image" style="background-image:url(/a/box/7/9/5/971795_thumb.jpg)"></div></div><div class="header_right"><h1 class="page-title">Sapiens Craft Puzzle &ndash; Guides and FAQs</h1><h3 class="platform-title"><span class="header_more">PC</span></h3><div class="gs_header_buttons"><span class="gs_hbtn_span"><button class="btn gs_hbtn gs_hb_notes" id="btn_gs_note" title="Your Lists"><i class="bi bi-clipboard" id="i_gs_note"></i></button><div id="gs_note" class="gsh_subnav gsh_subnav_wide">Log in to add games to your lists</div></span><span class="gs_hbtn_span"><button class="btn gs_hbtn gs_hb_notify" id="btn_gs_noty" title="Notify me"><i class="fa fa-bell-o" id="i_gs_noty"></i></button><div id="gs_noty" class="gsh_subnav"><span class="bold underline">Notify me about new:</span><div class="gsh_track" id="ug_guides"><i class="fa fa-bell-o gs_hb_notify"></i> Guides</div><div class="gsh_track" id="ug_cheats"><i class="fa fa-bell-o gs_hb_notify"></i> Cheats</div><div class="gsh_track" id="ug_reviews"><i class="fa fa-bell-o gs_hb_notify"></i> Reviews</div><div class="gsh_track" id="ug_answers"><i class="fa fa-bell-o gs_hb_notify"></i> Questions</div><div class="gsh_track" id="ug_news"><i class="fa fa-bell-o gs_hb_notify"></i> News</div><div class="gsh_track" id="ug_forum"><i class="fa fa-bell-o gs_hb_notify"></i> Board Topics</div><div class="gsh_track" id="ug_forum_topic"><i class="fa fa-bell-o gs_hb_notify"></i> Board Messages</div></div></span><span class="gs_hbtn_span"><button type="button" id="btn_gs_fav" class="btn gs_hbtn gs_hb_favorite" title="Favorites"><i class="fa fa-heart-o" id="i_gs_fav"></i></button><div id="gs_fav" class="gsh_subnav"><span class="bold underline">Add this game to my:</span><div class="gsh_track" id="ug_game"><i class="fa fa-heart-o gs_hb_favorite"></i> Favorite Games</div><div class="gsh_track" id="ug_board"><i class="fa fa-heart-o gs_hb_favorite"></i> Favorite Boards</div></div></span></div></div></div></header><nav id="js_content_nav" class="content_nav_wrap gs_nav_track"><div class="content_nav_imp_wrap"><div class="cnav_imp cnav_item"><a href="/pc/429449-sapiens-craft-puzzle">Home</a></div><div class="cnav_imp cnav_item"><a href="/boards/429449-sapiens-craft-puzzle">Board</a></div><div id="js_content_nav_toggle" class="content_nav_label">More</div></div><ol class="content_nav content_nav_split"><li class="cnav_item cnav_has_drop"><a href="/pc/429449-sapiens-craft-puzzle">Home</a><ol class="content_subnav"><li class="csubnav_item"><a href="/pc/429449-sapiens-craft-puzzle">Summary</a></li><li class="csubnav_item"><a href="/pc/429449-sapiens-craft-puzzle/data">Release Data</a></li><li class="csubnav_item"><a href="/pc/429449-sapiens-craft-puzzle/playing">Also Playing</a></li></ol></li><li class="cnav_item cnav_has_drop"><a href="/pc/429449-sapiens-craft-puzzle/media">Media</a><ol class="content_subnav"><li class="csubnav_item"><a href="/pc/429449-sapiens-craft-puzzle/boxes">Boxes</a></li></ol></li><li class="cnav_imp cnav_item"><a href="/boards/429449-sapiens-craft-puzzle">Board</a></li></ol></nav>
<script type="text/javascript">
$().ready(function() {
	$(document).mouseup(function(e){
	    $("#gs_fav, #gs_noty, #gs_note").each( function(container) {
	    	var container = $(this);
		    if(!container.is(e.target) && container.has(e.target).length === 0 && container.is(':visible')){
		    	if(e.target.id.indexOf(container.attr('id')) < 1)
		    	{
	        		container.hide();
		    	}
		    }
	    });
	});

	$('#js_content_nav_toggle').click(function()
	{
		$('.content_nav_wrap').toggleClass('content_nav_open');
	});

	$(".gs_hbtn").click( function() {
		var id = $(this).attr('id').substr(4);
		$('#' + id).toggle();
	});

			$(".gsh_track").click(function() {
			show_login();
		});
	});
</script>

<script type="application/ld+json">
{
	"@context": "http://schema.org",
	"@type": "BreadcrumbList",
	"itemListElement":
	[
		{
			"@type": "ListItem",
			"position": 1,
			"item":
			{
				"@id": "https://gamefaqs.gamespot.com/pc",
				"name": "PC"
			}
		},				{
			"@type": "ListItem",
			"position": 2,
			"item":
			{
				"@id": "https://gamefaqs.gamespot.com/pc/category/173-puzzle",
				"name": "Puzzle"
			}
		},				{
			"@type": "ListItem",
			"position": 3,
			"item":
			{
				"@id": "https://gamefaqs.gamespot.com/pc/category/281-puzzle-general",
				"name": "General"
			}
		}			]
}
</script>

<div class="pod" id="gamespace_search_module">
	<div class="search_head">
		<span class="search_head_mobile">What do you need help on?</span>
		<span class="search_close" onclick="dismiss_search_results(0);">Cancel X</span>
	</div>
	<div class="body">
		<fieldset id="gamespace_search">
			<button disabled><i class="fa fa-search"></i></button>
			<input type="text" id="gs_search_input" size="80" maxlength="80" placeholder="Search for help" value=""/>
		</fieldset>
	</div>
</div>


<script type="text/javascript">
$('body :not(.gamespace_search)').bind('click touch', function(e)
{
	if($('.gamespace_search').css('display')=='block')
	{
		e.stopPropagation();
		if($(this).attr('class')!='gamespace_search' && $(this).attr('id')!='gamespace_search' && $(this).attr('id')!='gs_search_input' && $(this).attr('class')!='head')
		{
			dismiss_search_results();
		}
	}
});


$('#gs_search_input').autocomplete(
{
	minLength: 1,
	delay: 150,
	classes: { 'ui-autocomplete':'gamespace_search' },
	source: '/ajax/gamespace_search?id=429449&term=' + $('#gs_search_input').val() + '&group=' + 'gamespace',
	focus: function(){ return false; },
	close: function(event, ui)
	{
		if($('#gs_search_input').val())
		{
			$('.gamespace_search').show();
			if($('.gamespace_search').has('li').length===0)
				dismiss_search_results();
		}
	},
	select: function(event, ui)
	{
		event.preventDefault();
		log_gs_result(ui.item.row_num);
	}
})
.data('ui-autocomplete')._renderItem = function(ul, item)
{
	var list;
	if(item.result)
	{
		if(!item.sub_label)
			item.sub_label = '';
		list += '<li><div data-row="'+item.row_num+'" data-url="'+item.value+'" data-type="'+item.header_type+'"><span class="result_title"><a href="'+item.value+'">'+item.label+'</a>'+item.sub_label+'</span><span class="result_type">'+item.header+'</span></div></li>';
	}
	else if(item.topics)
	{
			}
	else
	{
		list += '<li><div class="dismiss_results" onclick="dismiss_search_results();">No Results Found - Close</div></li>';
	}
	return $(list).appendTo(ul);
};

function log_gs_result(row)
{
	if($('.gamespace_search div[data-row="'+row+'"]').length>0)
	{
		var query = $('#gs_search_input').val();
		var label = $('.gamespace_search div[data-row="'+row+'"] span.result_title').text();
		var type = parseInt($('.gamespace_search div[data-row="'+row+'"]').attr('data-type'));
				$.ajax(
		{
			type: 'POST',
			url: '/ajax/log_search_click',
			data: { t:'game_faq_list', p:429449, g:605453, s:query, c:label, x:type, r:row, key:'c5958fc7' },
			success: function(response)
			{
				$('#gs_search_input').val('');
				window.location = $('.gamespace_search div[data-row="'+row+'"]').attr('data-url');
			}
		});
			}
}
</script>


<div class="pod">
</div>



<div class="pod"><div class="head"><h2 class="title">Want to Write Your Own Guide?</h2></div><div class="body"><p>You can write and submit your own guide for this game using either our <a href="/contribute/guide_select">full-featured online editor</a> or our <a href="/contribute/submit_text">basic text editor</a>. We also accept <a href="/contribute/submit_map">maps and charts</a> as well.</p></div></div>
								
								<div class="clear"></div>
							</div>
							<aside class="span4">
								<div class="pod pod_gameinfo">
	<div class="head"><h2 class="title">Game Detail</h2></div>
	<div class="body">
		<ol class="list flex col1 nobg">
			<li>
				<div class="content"><b>Platform:</b> <a href="/pc">PC</a></div>
			</li>
			<li>
				<div class="content"><b>Genre:</b> <a href="/pc/category/173-puzzle">Puzzle</a> &raquo; <a href="/pc/category/281-puzzle-general">General</a></div>
			</li>
						<li>
				<div class="content"><b>Developer/Publisher: </b><a href="/games/company/262155-abra24">Abra24</a></div>
			</li>
									<li>
				<div class="content"><b>Release:</b> <a href="/pc/429449-sapiens-craft-puzzle/data">TBA</a></div>
			</li>
														</ol>
	</div>

	</div>

<div id="deals_ctr" class="hide">
	Loading deals...
</div>
<script type="text/javascript">
$().ready( function() {
	$.ajax({
		type: 'GET',
		url: '/ajax/gamespace_deals_async',
		data: { pid: 429449, product_id: 733947, game_id: 605453 },
		success: function(response)
		{
			if(response)
			{
				$('#deals_ctr').html(response);
				$('#deals_ctr').removeClass('hide');
			}
		}
	});
});
</script>


																											<div class="js-mapped-ad ad ad_mpu_plus_top" data-ad-type="mpu_plus_top"><div class="ad_wrap "></div></div>
																									<div class="pod"><div class="head"><h2 class="title">Games You May Like</h2></div><div class="body"><ol class="list flex col1"><li><div class="list_img img_med"><img class="crop imgboxart" src="/a/box/4/2/0/1030420_thumb.jpg" alt=""/></div><div class="content"><a class="bold" href="/pc/469025-jigsaw-world">Jigsaw World</a><div class="meta"></div></div></li><li><div class="list_img img_med"><img class="crop imgboxart" src="/a/box/4/8/3/586483_thumb.jpg" alt=""/></div><div class="content"><a class="bold" href="/pc/181933-human-resource-machine">Human Resource Machine</a><div class="meta">The machines are coming... for your job.



Program little office workers to solve puzzles. Be a good employee! The m...</div></div></li><li><div class="list_img img_med"><img class="crop imgboxart" src="/a/box/7/2/0/584720_thumb.jpg" alt=""/></div><div class="content"><a class="bold" href="/pc/180807-dungeonup">DungeonUp</a><div class="meta">DungeonUp is a dungeon crawler in which you have to make choice in every few steps.

Inspired by but different from T...</div></div></li><li><div class="list_img img_med"><img class="crop imgboxart" src="/a/box/6/6/6/1003666_thumb.jpg" alt=""/></div><div class="content"><a class="bold" href="/pc/450668-camper-van-make-it-home">Camper Van: Make it Home</a><div class="meta">Camper Van: Make it Home is a game about camperizing vans. Embark on a journey of personal discovery while decorating...</div></div></li><li><div class="list_img img_med"><img class="crop imgboxart" src="/a/box/0/0/6/602006_thumb.jpg" alt=""/></div><div class="content"><a class="bold" href="/pc/191278-dungeon-journey-2016">Dungeon Journey (2016)</a><div class="meta">Fight your way through thousands floors of dungeons. Get precious treasures and slay undead creatures of hell to make...</div></div></li></ol></div></div>

								<div class="js-mapped-ad ad ad_mpu_bottom" data-ad-type="mpu_bottom"><div class="ad_wrap "></div></div>
								
							</aside>
						</div>
										
																			</div>
				
								<div class="js-mapped-ad ad ad_leader_bottom" data-ad-type="leader_bottom"><div class="ad_wrap "></div></div>
				
			</div>
						<div class="js-mapped-ad ad ad_interstitial" data-ad-type="interstitial"><div class="ad_wrap "></div></div>

							<footer id="footer" class="footer"><div class="footer_site container row"><a href="/" class="footer_logo_gf">GameFAQs</a><a href="http://www.facebook.com/GFAQs"><i class="fa fa-facebook-square"></i>facebook.com/GFAQs</a><a href="http://www.twitter.com/GameFAQs"><i class="fa fa-twitter"></i>twitter.com/GameFAQs</a><span class="footer_break"></span><a href="/help">Help / Contact Us</a><span class="footer_color_drop" id="color_change">Change Colors <i class="fa fa-caret-up"></i><ul class="footer_color_subnav"><li class="footer_color_subnav_item"><a onclick="change_css('blue');">Blue (Default)</a></li>
<li class="footer_color_subnav_item"><a onclick="change_css('dark-blue');">Blue on Black</a></li>
<li class="footer_color_subnav_item"><a onclick="change_css('red');">Red</a></li>
<li class="footer_color_subnav_item"><a onclick="change_css('dark-red');">Red on Black</a></li>
<li class="footer_color_subnav_item"><a onclick="change_css('green');">Green</a></li>
<li class="footer_color_subnav_item"><a onclick="change_css('dark-green');">Green on Black</a></li>
<li class="footer_color_subnav_item"><a onclick="change_css('orange');">Orange</a></li>
<li class="footer_color_subnav_item"><a onclick="change_css('dark-orange');">Orange on Black</a></li>
<li class="footer_color_subnav_item"><a onclick="change_css('purple');">Purple</a></li>
<li class="footer_color_subnav_item"><a onclick="change_css('dark-purple');">Purple on Black</a></li>
<li class="footer_color_subnav_item"><a onclick="change_css('cloudy');">Cloudy Blue</a></li>
<li class="footer_color_subnav_item"><a onclick="change_css('grayscale');">Grayscale</a></li>
<li class="footer_color_subnav_item"><a onclick="change_css('sepia');">Sepia</a></li>
<li class="footer_color_subnav_item"><a onclick="change_css('cottoncandy');">Cotton Candy</a></li>
</ul></span></div><div class="footer_network"><div class="container row"><p><a href="https://www.gamespot.com/?utm_source=gamefaqs&utm_medium=partner&utm_content=footer&utm_campaign=footer" class="footer_logo_gs">gamespot.com</a><a href="https://www.metacritic.com/" class="footer_logo_mc">metacritic.com</a><a href="https://www.fandom.com/" class="footer_logo_fandom">fandom.com</a><a href="https://www.fanatical.com/" class="footer_logo_fanatical">fanatical.com</a></p><p><span class="footer_break"></span><a href="https://gamefaqs.gamespot.com/sitemap">Sitemap</a><a href="https://about.fandom.com/mediakit#contact" rel="nofollow">Partnerships</a><a href="https://about.fandom.com/careers" rel="nofollow">Careers</a><a href="https://www.fandom.com/terms-of-service-pp1" rel="nofollow">Terms of Use</a><a href="https://www.fandom.com/digital-services-act-gamefaqs" rel="nofollow">Digital Services Act</a></p><p><span class="footer_break"></span><a href="https://www.fandom.com/privacy-policy-pp1" rel="nofollow">Privacy Policy</a><a class="ot-sdk-show-settings">Cookie Settings</a><a style="z-index: 999999999; color: red; cursor: pointer" onclick="ad_report();">Report&nbsp;Ad</a></p><p>&copy; 2025 FANDOM, INC. ALL RIGHTS RESERVED.</p></div></div></footer>


							
		</div>
		
				<script type="application/ld+json">
		{
			"@context":"http://schema.org",
			"@type":"VideoGame",
			"name":"Sapiens Craft Puzzle",
			"url":"https://gamefaqs.gamespot.com/pc/429449-sapiens-craft-puzzle",
			"image":"/a/box/7/9/5/971795_thumb.jpg",
			"description":"Sapiens Craft Puzzle is a Puzzle game, developed and published by Abra24,  with no announced release date.",
			"datePublished":"2097-12-31",
									"publisher":"Abra24",
			"genre":["Puzzle","General"],
			"gamePlatform":["PC"],
			"operatingSystem":["PC"],
			"applicationCategory":"Game",
			"sameAs":"https://www.gamespot.com/games/sapiens-craft-puzzle/",
			"keywords":""
		}
		</script>
		
		<div id="site_dialog" class="site_dialog"></div><div id="flyover"></div><div id="report_dialog"></div>
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-PWVTCD9" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>


<script type="text/javascript">
// utag_data
(function(){ let utag_data;try{ utag_data=JSON.parse($('#utag-data').attr('content')); }catch(e){ utag_data={}; }window.utag_data=$.extend({},window.utag_data,utag_data); })();
</script>

<script src="https://services.fandom.com/icbm/api/loader?app=gamefaqs-noads"></script>

<script type="text/javascript">
function amp_event(name, vars = null){ window.dataLayer = window.dataLayer || []; var dl = { event: name, env: location.hostname.match(/dev|integ/) ? 'dev' : 'prod', pageview_id: window.vguid, registered_user_id: window.gfuid, data: window.utag_data}; if(vars) dl = { ...dl, ...vars }; window.dataLayer.push(dl); }
amp_event('Pageview');
</script>
		
<!--Page built in 114.6 ms on 2025-06-15 13:15:01 on box #137 (CSS 20250306212919 JS 20240930203849)- O 22.4 / S 72.8 / M 19.4 / U 0 / SC 58-->
			</body>
</html>
