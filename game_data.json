{"game_version": "2.0.0", "metadata": {"game_name": "Backpack Battles", "description": "Advanced inventory management roguelike with complex item interactions", "version": "2.0.0"}, "item_types": {"base_types": ["weapon", "armor", "accessory", "consumable", "crafting_material", "special"], "subtypes": {"weapon": ["melee", "ranged", "magical"], "armor": ["light", "medium", "heavy"]}}, "items": [{"id": "basic_sword", "name": "Basic Sword", "type": "weapon", "subtype": "melee", "dimensions": {"width": 2, "height": 3, "shape": "rectangular", "rotation_allowed": true}, "grid_placement": {"preferred_orientation": "horizontal", "max_rotations": 2}, "effects": {"damage": {"base": 10, "type": "physical", "scaling_factors": {"strength": 0.5, "dexterity": 0.3}}, "durability": {"current": 100, "max": 100, "degradation_rate": 0.1}}, "requirements": {"minimum_strength": 10, "skill_level": 1}, "rarity": "common", "weight": 2.5, "value": 25, "tags": ["starter", "basic_equipment"]}], "positional_bonuses": {"corner_bonus": {"description": "Strategic placement in corner slots", "bonus_multiplier": 1.2, "applicable_types": ["weapon", "accessory"]}, "adjacent_synergy": {"description": "Interaction bonuses for compatible adjacent items", "rules": [{"item_types": ["weapon", "armor"], "bonus_effect": {"type": "increased_defense", "value": 0.15}}, {"item_types": ["magical_weapon", "magical_accessory"], "bonus_effect": {"type": "mana_regeneration", "value": 0.1}}]}}, "inventory_layout": {"grid_size": {"width": 6, "height": 4, "total_slots": 24}, "special_slots": {"weapon": {"max": 2, "position": "primary"}, "armor": {"max": 1, "position": "body"}, "accessory": {"max": 3, "position": "flexible"}}, "weight_limits": {"base_capacity": 50, "overweight_penalty": {"movement_speed_reduction": 0.3, "stamina_drain": 0.2}}}, "crafting_recipes": [{"id": "advanced_sword_recipe", "result": {"item_id": "advanced_sword", "quantity": 1}, "ingredients": [{"item_id": "basic_sword", "quantity": 1}, {"item_id": "rare_metal", "quantity": 1}], "required_crafting_level": 2, "success_probability": 0.75, "crafting_time": 300}], "character_classes": [{"name": "Warrior", "base_stats": {"health": 100, "strength": 15, "defense": 10, "stamina": 80}, "inventory_bonuses": {"weapon_slot_bonus": 1.1, "max_weight_capacity": 60, "special_abilities": ["dual_wielding", "quick_swap"]}, "progression": {"max_level": 50, "experience_curve": "linear"}}], "interaction_matrix": {"damage_types": {"physical": {"weakness": ["magical"], "strength": ["armor"]}, "magical": {"weakness": ["physical"], "strength": ["magical_resistance"]}}}}