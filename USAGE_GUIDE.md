# Backpack Battles Web Crawler - Usage Guide

## Installation

### Prerequisites
- Python 3.8+
- pip package manager

### Setup
1. Clone the repository
```powershell
git clone https://github.com/yourusername/backpack-battles-crawler.git
cd backpack-battles-crawler
```

2. Install dependencies
```powershell
pip install -r requirements.txt
```

## Basic Usage

### Simple Crawling
```python
from backpack_battles_crawler import BackpackBattlesCrawler

# Initialize crawler with default configuration
crawler = BackpackBattlesCrawler()

# Perform web crawling
data = crawler.crawl()

# Save extracted data to JSON
crawler.save_data()
```

### Custom Configuration
```python
# Use custom configuration file
crawler = BackpackBattlesCrawler('custom_config.yaml')
crawler.crawl()
crawler.save_data('custom_output.json')
```

## Configuration Options

### crawler_config.yaml
```yaml
# Base URLs to crawl
base_urls:
  - "https://backpackbattles.wiki.gg/wiki/Items"
  - "https://backpackbattles.wiki.gg/wiki/Classes"

# Rate limiting configuration
rate_limit:
  min_delay: 1  # Minimum delay between requests
  max_delay: 3  # Maximum delay between requests

# Retry mechanism configuration
retry_config:
  max_retries: 3
  backoff_factor: 2

# Caching configuration
cache_dir: './crawler_cache'
```

## Advanced Usage

### Creating Custom Data Source Plugins
```python
from backpack_battles_crawler import DataSourcePlugin

class CustomSourcePlugin(DataSourcePlugin):
    def extract_data(self, html: str, url: str) -> List[Dict[str, str]]:
        # Implement custom parsing logic
        # Return normalized data
        pass
```

### Logging Configuration
```python
import logging

# Configure logging level
logging.getLogger('BackpackBattlesCrawler').setLevel(logging.DEBUG)
```

## Command-Line Usage
```powershell
# Run crawler directly
python backpack_battles_crawler.py
```

## Error Handling

### Common Issues
- Network connectivity problems
- Website structure changes
- Rate limiting

### Troubleshooting
1. Check `backpack_battles_crawler.log` for detailed errors
2. Verify network connection
3. Ensure correct Python version
4. Validate configuration file

## Performance Tips
- Use fewer concurrent workers for stable connections
- Adjust rate limiting to prevent IP blocking
- Implement caching to reduce network requests

## Data Output

### JSON Structure
```json
{
  "items": [
    {
      "name": "Item Name",
      "type": "Item Type",
      "link": "Source URL",
      "source": "Website Origin"
    }
  ],
  "characters": [
    {
      "name": "Character Name",
      "type": "Character Class",
      "link": "Source URL",
      "source": "Website Origin"
    }
  ]
}
```

## Best Practices
- Respect website terms of service
- Use crawler responsibly
- Verify extracted data
- Keep configuration flexible

## Extending Functionality
1. Create custom parsing plugins
2. Implement more advanced data normalization
3. Add machine learning-based extraction

## Monitoring and Maintenance
- Regularly update parsing strategies
- Monitor log files
- Keep dependencies updated

## Security Considerations
- Do not expose sensitive configuration
- Use environment variables for credentials
- Implement proper error handling

## Contributing
1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Submit a pull request

## License
[Specify your project's license]