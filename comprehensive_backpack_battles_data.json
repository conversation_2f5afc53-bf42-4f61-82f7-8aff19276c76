{"items": [{"name": "Amulet of Alchemy", "type": "Accessory", "rarity": "Rare", "cost": "6", "effect": "Start of battle: Gain 3 random buffs. Potion consumed: 70% chance to repeat its effect after 2.5s.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/b/b1/AmuletofAlchemy.png/96px-AmuletofAlchemy.png?656cbc", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Amulet of Darkness", "type": "Accessory", "rarity": "Rare", "cost": "6", "effect": "10 -damage dealt: Inflict 1 random debuff. item activates: 30% chance to deal 5 -damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/d/d9/AmuletofDarkness.png/97px-AmuletofDarkness.png?db0f02", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Amulet of Energy", "type": "Accessory", "rarity": "Rare", "cost": "6", "effect": "Start of battle: The item triggers 100% faster for 1s. Buff used: Refund 25% of the used buffs.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/7/76/AmuletofEnergy.png/94px-AmuletofEnergy.png?6e2ba1", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Amulet of Feasting", "type": "Accessory", "rarity": "Rare", "cost": "6", "effect": "Food triggers 40% faster. Food bought: Restock with a random Food.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/6/6f/AmuletofFeasting.png/94px-AmuletofFeasting.png?2a06b8", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Amulet of Life", "type": "Accessory", "rarity": "Rare", "cost": "6", "effect": "Start of battle: <PERSON><PERSON> 20 maximum health. Your healing is increased by 20%.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/3/3c/AmuletofLife.png/94px-AmuletofLife.png?44dec4", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Amulet of Steel", "type": "Accessory", "rarity": "Rare", "cost": "6", "effect": "Start of battle: <PERSON><PERSON> 25 . items gained 35 : Gain 1 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/6/62/AmuletofSteel.png/94px-AmuletofSteel.png?411a34", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Amulet of the Wild", "type": "Accessory", "rarity": "Rare", "cost": "6", "effect": "After 5s: Trigger the Pet and gain 4 . Return damage limit of against and attacks +50%.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/5/55/AmuletoftheWild.png/94px-AmuletoftheWild.png?ca9820", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Artifact Stone: Cold", "type": "Weapon", "rarity": "Unique", "cost": "10", "effect": "Can only be thrown once per battle. On hit: Inflict 3 . Weapon hits: Inflict 1 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/5/59/ArtifactStoneCold.png/88px-ArtifactStoneCold.png?f7e6ec", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Artifact Stone: Death", "type": "Weapon", "rarity": "Unique", "cost": "8", "effect": "Can only be thrown once per battle. On hit: Inflict Fatigue damage. items have +7% critical hit chance per Fatigue level of your opponent.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/5/55/ArtifactStoneDeath.png/83px-ArtifactStoneDeath.png?354915", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Artifact Stone: Heat", "type": "Weapon", "rarity": "Unique", "cost": "9", "effect": "Can only be thrown once per battle. On hit: Gain 3 . 10 reached: Weapons gain 8 damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/c2/ArtifactStoneHeat.png/86px-ArtifactStoneHeat.png?96be74", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Bag of Stones", "type": "Accessory", "rarity": "Rare", "cost": "3", "effect": "Stones above can be thrown repeatedly.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/9/99/BagofStones.png/100px-BagofStones.png?6080b9", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Bagtacular", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Fanny Packs give +10% trigger speed. Stamina Sacks give 10% base stamina regeneration. Potion Belts give 2 buffs when a Potion inside is consumed. Protective Purses give +15 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/8/8a/Bagtacular.png/98px-Bagtacular.png?fcc182", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Banana", "type": "Food", "rarity": "Common", "cost": "3", "effect": "Every 5s: <PERSON><PERSON> for 4 and regenerate 1 stamina.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/6/69/Banana.png/100px-Banana.png?90a3c2", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Blood Amulet", "type": "Accessory", "rarity": "Legendary", "cost": "8", "effect": "Start of battle: <PERSON><PERSON> 2 and 20 maximum health.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/5/59/BloodAmulet.png/91px-BloodAmulet.png?4f493d", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Blood Goobert", "type": "Pet", "rarity": "Legendary", "cost": "15", "effect": "Start of battle: Gain 2 . 6 item activations: Deal 10 -damage with 100% lifesteal. Deal +1 for each .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/0/02/BloodGoobert.png/97px-BloodGoobert.png?bb918f", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Blood Harvester", "type": "Weapon", "rarity": "Unique", "cost": "7", "effect": "Items give +100% . Attacks 5% faster for every .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/0/0a/BloodHarvester.png/65px-BloodHarvester.png?180e09", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Blood Manipulation", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Deal 20% of your healing as -damage. Every 3s: Gain 1 . Triggers 15% faster for each -item.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/0/07/BloodManipulation.png/89px-BloodManipulation.png?65e728", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "Weapon", "rarity": "<PERSON><PERSON>", "cost": "15", "effect": "On hit: Use 1 to gain 1 and 1 . Deals +1 damage per and .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/0/02/Bloodthorne.png/33px-Bloodthorne.png?8da1c8", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Bloody Dagger", "type": "Weapon", "rarity": "Legendary", "cost": "12", "effect": "On hit: <PERSON>ain 1 (up to 5 per battle). Heal 4 per -item. On stun: Triggers extra attack.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/a1/BloodyDagger.png/50px-BloodyDagger.png?fa4eb6", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Blueberries", "type": "Food", "rarity": "Rare", "cost": "2", "effect": "Every 3.5s: <PERSON><PERSON> 1 . If you have at least 10 : gain 1 instead.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/af/Blueberries.png/100px-Blueberries.png?f70ba7", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Box of Prosperity", "type": "Bag", "rarity": "Epic", "cost": "5", "effect": "Add 4 backpack slots. Shop entered: If this has at least 2 Godly or Unique items inside, generate a chipped Gemstone.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/9/94/BoxofProsperity.png/99px-BoxofProsperity.png?2f8f1a", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Box of Riches", "type": "Accessory", "rarity": "Rare", "cost": "5", "effect": "Shop entered: Generate a low-quality gemstone. Gemstones are offered in the shop.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/2/2a/BoxofRiches.png/100px-BoxofRiches.png?47d246", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Amethyst", "type": "Gemstone", "rarity": "Varies", "cost": "1", "effect": "Weapon sockets: On hit: 20/30/45/65/100% chance to remove a random buff from your opponent. Armor & other sockets: Reduce opponent's healing by 15/20/25/30/40%. Backpack: Every 4/3/2.5/2/1.2s: Cleanse 1 debuff.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/f/f2/BPB_Amethyst_Line.png/100px-BPB_Amethyst_Line.png?535ff2", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Emerald", "type": "Gemstone", "rarity": "Varies", "cost": "1", "effect": "Weapon sockets: On hit: 35/50/80/80/100% chance to inflict 1/1/1/2/3 . Armor & other sockets: 10/15/20/25/35% chance to resist . Backpack: After 3/4/4/3.5/3s: <PERSON>ain 1/2/3/4/6 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/f/fb/BPB_Emerald_Line.png/100px-BPB_Emerald_Line.png?9861b7", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON>", "type": "Gemstone", "rarity": "Varies", "cost": "1", "effect": "Weapon sockets: On hit: <PERSON><PERSON> 7/10/15/20/30% lifesteal. Armor & other sockets: Your healing is increased by 10/15/20/25/35%. Backpack: After 5s: Deal 4/6/10/15/30 -damage with 100% lifesteal.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/2/2f/BPB_Ruby_Line.png/100px-BPB_Ruby_Line.png?b03e69", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Sapphire", "type": "Gemstone", "rarity": "Varies", "cost": "1", "effect": "Weapon sockets: On hit: 15/25/40/60/80% chance to ignore , gain 1 and inflict 1 . Armor & other sockets: 5 gained: <PERSON>ain 2/3/4/5/7 . Backpack: After 3/4/4/3.5/3s: Inflict 1/2/3/4/6 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/8/86/BPB_Sapphire_Line.png/100px-BPB_Sapphire_Line.png?330f8d", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Topaz", "type": "Gemstone", "rarity": "Varies", "cost": "1", "effect": "Weapon sockets: Attacks 10/15/20/25/35% faster. Armor & other sockets: 10/15/20/30/35% chance to resist stuns. 4/6/8/10/15% chance resist critical hits. Backpack: Base stamina regeneration +8/12/20/30/45%.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/4/4e/BPB_Topaz_Line.png/100px-BPB_Topaz_Line.png?488599", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Broom", "type": "Weapon", "rarity": "Common", "cost": "4", "effect": "Opponent misses attack: <PERSON><PERSON> +2 damage for the next attack. On hit: 33% chance to inflict 1 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/2/26/Broom.png/22px-Broom.png?972204", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Bunch of Coins", "type": "Accessory", "rarity": "Rare", "cost": "9", "effect": "They don't do anything. But you can sell them for profit!", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/ab/BunchofCoins.png/98px-BunchofCoins.png?2bb8bc", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Burning Coal", "type": "Gemstone", "rarity": "Rare", "cost": "2", "effect": "Weapon sockets: On hit: 12% chance to deal +6 damage and gain 1 . Armor & other sockets: Start of battle: Gain 15 . Resist 5 . Backpack: After 5s: Gain 2 , cleanse 3 debuffs.", "image_url": "https://backpackbattles.wiki.gg/images/b/bc/BurningCoal.png?3aa66a", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Burning Torch", "type": "Weapon", "rarity": "Epic", "cost": "5", "effect": "Start of battle: <PERSON><PERSON> 2 . On hit: 30% chance to gain 1 damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/9/9c/BurningTorch.png/40px-BurningTorch.png?618279", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Buy the Holy Light", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "-Items have +15% chance to be on sale. Oil Lamps and Djinn Lamps gain . -Items trigger 40% faster.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/a1/BuytheHolyLight.png/97px-BuytheHolyLight.png?c2a240", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Cap of Discomfort", "type": "<PERSON><PERSON><PERSON>", "rarity": "Legendary", "cost": "14", "effect": "Start of battle: Reduce damage taken by 25% for 5s. Opponent gains buff: 15% chance to nullify it. Your opponent's healing is reduced by 30%.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/a6/CapofDiscomfort.png/57px-CapofDiscomfort.png?14f1db", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Cap of Resilience", "type": "<PERSON><PERSON><PERSON>", "rarity": "Epic", "cost": "7", "effect": "Start of battle: Reduce damage taken by 25% for 3s. 15% chance to resist critical hits. 15% chance to resist stuns.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/1a/CapofResilience.png/56px-CapofResilience.png?4dad8c", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Ch<PERSON>l<PERSON>", "type": "Pet", "rarity": "Unique", "cost": "8", "effect": "Every 3.3s: Deal 10 -damage with 100% lifesteal and trigger a random Food. Food gains . Triggers 15% faster for each -item.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/b/b8/Chtulhu.png/100px-Chtulhu.png?65006a", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Claws of Attack", "type": "Weapon", "rarity": "Epic", "cost": "8", "effect": "After 4 hits, gain 1 . Attacks 5% faster for every .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/f/f4/ClawsofAttack.png/100px-ClawsofAttack.png?f98575", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Corrupted Armor", "type": "Armor", "rarity": "<PERSON><PERSON>", "cost": "20", "effect": "-Items gain . 10% chance for each -item to protect debuffs on your opponent from being cleansed. Start of battle: <PERSON><PERSON> 85 . Every 2.4s: Cleanse 2 debuffs and inflict them on your opponent.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/9/9c/CorruptedArmor.png/74px-CorruptedArmor.png?92b27b", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Corrupted Crystal", "type": "Gemstone", "rarity": "Epic", "cost": "7", "effect": "Weapon sockets: Opponent below 30% health: Deal +50% damage. Armor & other sockets: 7 debuffs inflicted: Gain 6 . Backpack: Every 5.5s: Inflict Fatigue damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/a9/CorruptedCrystal.png/62px-CorruptedCrystal.png?827970", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Crossblades", "type": "Weapon", "rarity": "<PERSON><PERSON>", "cost": "38", "effect": "Start of battle: The Weapon gains 10 damage. The item triggers 60% faster. On hit: Gain +1 damage and trigger 4% faster.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/8/8c/Crossblades.png/100px-Crossblades.png?62087d", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON><PERSON><PERSON>", "type": "Pet", "rarity": "Unique", "cost": "8", "effect": "activates: 35% chance to gain 1 . activates: 30% chance to use 1 to gain 1 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/6/6a/Cubert.png/99px-Cubert.png?6e5743", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Customer Card", "type": "Accessory", "rarity": "Rare", "cost": "4", "effect": "Increases the rarity of 1 item in the shop every time it refreshes.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/5/56/CustomerCard.png/100px-CustomerCard.png?78d44c", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON>gger", "type": "Weapon", "rarity": "Rare", "cost": "4", "effect": "On stun: <PERSON><PERSON><PERSON> extra attack.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/4/45/Dagger.png/50px-Dagger.png?9c578e", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Dancing Dragon", "type": "Weapon", "rarity": "Unique", "cost": "9", "effect": "You have a 2% chance to resist debuffs for each . Start of battle: Gain 2 and 2 for each -item. Deals +0.5 damage per .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/3/3d/DancingDragon.png/100px-DancingDragon.png?60afa4", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Darksaber", "type": "Weapon", "rarity": "<PERSON><PERSON>", "cost": "19", "effect": "On attack: Use 1 to inflict 1 . Deals +0.5 damage for each debuff of your opponent.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/e/e8/Darksaber.png/23px-Darksaber.png?e8af7f", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Divine Potion", "type": "Potion", "rarity": "<PERSON><PERSON>", "cost": "7", "effect": "You reached 10 debuff: Consume this and cleanse 10 debuffs.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/7/7e/DivinePotion.png/46px-DivinePotion.png?2d9d16", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON><PERSON><PERSON>", "type": "Accessory", "rarity": "<PERSON><PERSON>", "cost": "11", "effect": "Every 1.6s: Gain 1 or 1 or 1 , depending on what you have the least of. Use 7 , 7 , 7 , 7 and 27 health: Give the Weapon +27 damage (once).", "image_url": "https://backpackbattles.wiki.gg/images/thumb/8/80/DjinnLamp.png/100px-DjinnLamp.png?dfc77d", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Eggscalibur", "type": "Weapon", "rarity": "Legendary", "cost": "10", "effect": "On attack: Use 11 : <PERSON><PERSON> all Food. Deals +1 damage for each Food.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/7/76/Eggscalibur.png/100px-Eggscalibur.png?29e5d4", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Falcon Blade", "type": "Weapon", "rarity": "Legendary", "cost": "19", "effect": "Start of battle: items trigger 30% faster. Attacks twice.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/f/f2/FalconBlade.png/38px-FalconBlade.png?2b76d3", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON><PERSON>", "type": "Weapon", "rarity": "<PERSON><PERSON>", "cost": "12", "effect": "On hit: Use 3 to gain 3 damage. On miss: Gain 3 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/d/d3/FancyFencingRapier.png/23px-FancyFencingRapier.png?81456b", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Fanfare", "type": "Accessory", "rarity": "<PERSON><PERSON>", "cost": "7", "effect": "Every 3s: Randomly gain 1 or gain 3 and remove 2 from opponent or remove 1 stamina from opponent. Triggers 10% faster for each item.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/7/7f/Fanfare.png/100px-Fanfare.png?c192ef", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON>", "type": "Bag", "rarity": "Rare", "cost": "3", "effect": "Add 2 backpack slots. Items inside trigger 10% faster.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/f/f7/FannyPack.png/100px-FannyPack.png?c7f0bf", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Flame Badge", "type": "Accessory", "rarity": "Unique", "cost": "5", "effect": "Pyromancer items are offered in the shop. Shop entered: 65% chance to spend 1 and generate a Flame. Start of battle: Gain 6 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/a7/FlameBadge.png/79px-FlameBadge.png?ee5b7f", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Flute", "type": "Accessory", "rarity": "Epic", "cost": "6", "effect": "Every 4.7s: Randomly gain 14 or 2 stamina or 2 . Triggers 10% faster for each item.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/d/db/Flute.png/100px-Flute.png?2a0b71", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Frostbite", "type": "Weapon", "rarity": "Legendary", "cost": "11", "effect": "On hit: 60% chance to inflict 1 . Opponent reaches 30 : <PERSON><PERSON> 5 (once). Deals +1 damage per and +0.4 per of your opponent.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/e/ee/Frostbite.png/30px-Frostbite.png?b60bb0", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON><PERSON><PERSON>", "type": "Shield", "rarity": "Epic", "cost": "8", "effect": "On attacked (): 30% chance to prevent 7 damage, remove 0.5 stamina from opponent and inflict 1 (up to 10).", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/ad/FrozenBuckler.png/97px-FrozenBuckler.png?b6596e", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON><PERSON><PERSON>", "type": "Food", "rarity": "Common", "cost": "2", "effect": "Every 4s: <PERSON><PERSON> 3 . 30% chance to remove 1 from your opponent.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/cc/Garlic.png/45px-Garlic.png?1d9604", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Gingerbread Jerry", "type": "Food", "rarity": "Unique", "cost": "8", "effect": "Start of battle: <PERSON><PERSON> 40 maximum health. Every 3s: Use 1 , 1 and 1 : <PERSON><PERSON> 1 , 3 and 20 maximum health.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/8/8c/GingerbreadJerry.png/55px-GingerbreadJerry.png?90994b", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Girl Power", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Every 3.5s: Gain 2 or 2 , depending on which you have less of. Triggers 20% faster for each distinct Class item.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/d/d6/GirlPower.png/90px-GirlPower.png?3a35f7", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Gloves of Haste", "type": "Gloves", "rarity": "Rare", "cost": "4", "effect": "Start of battle: items trigger 20% faster.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/7/74/GlovesofHaste.png/100px-GlovesofHaste.png?a0c2ec", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Gloves of Power", "type": "Gloves", "rarity": "Legendary", "cost": "10", "effect": "Weapons deal +20% damage but attack 10% slower. Weapon hits: gain 7 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/19/GlovesofPower.png/100px-GlovesofPower.png?53c6d5", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Glowing Crown", "type": "<PERSON><PERSON><PERSON>", "rarity": "<PERSON><PERSON>", "cost": "12", "effect": "Every 2.4s: Cleanse 1 and heal for 5. Use 10 : Become invulnerable for 2s (once).", "image_url": "https://backpackbattles.wiki.gg/images/thumb/5/55/GlowingCrown.png/100px-GlowingCrown.png?45bec0", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Goobert", "type": "Pet", "rarity": "Rare", "cost": "6", "effect": "5 item activations: Heal for 9.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/c3/Goobert.png/97px-Goobert.png?c6fe15", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON><PERSON><PERSON>", "type": "Pet", "rarity": "Common", "cost": "2", "effect": "3 item activations: Heal for 4.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/8/8b/Goobling.png/100px-Goobling.png?259c4d", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Hammer", "type": "Weapon", "rarity": "Rare", "cost": "8", "effect": "On hit: 45% chance to stun your opponent for 0.5s.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/8/81/Hammer.png/100px-Hammer.png?38d36f", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Hardwood", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Common Weapons deal +150% damage. Start of battle: Gain 10 for each Common item.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/e/ed/Hardwood.png/68px-Hardwood.png?e80c98", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Healing Herbs", "type": "Accessory", "rarity": "Common", "cost": "4", "effect": "Start of battle: Gain 2 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/c4/HealingHerbs.png/100px-HealingHerbs.png?408827", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Health Potion", "type": "Potion", "rarity": "Rare", "cost": "4", "effect": "Health drops below 50%: Consume this and heal for 12 and cleanse 4 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/d/d2/HealthPotion.png/46px-HealthPotion.png?4485b0", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Heart Container", "type": "Accessory", "rarity": "<PERSON><PERSON>", "cost": "12", "effect": "Every 3s: Gain 1 . Use 7 : <PERSON><PERSON> 100 maximum health, 2 and your healing is increased by 15% (once).", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/10/HeartContainer.png/98px-HeartContainer.png?4de6cb", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Heart of Darkness", "type": "Accessory", "rarity": "<PERSON><PERSON>", "cost": "19", "effect": "Every 4s: Steal 2 buffs, prioritizing . Triggers 20% faster for each -item. Use 7 : <PERSON>ain 100 maximum health, 4 and your opponent's healing is reduced by 40% (once).", "image_url": "https://backpackbattles.wiki.gg/images/thumb/f/fd/HeartofDarkness.png/100px-HeartofDarkness.png?933cef", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Heavy Drinking", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Every 10s: Trigger the effect of a random Potion. Triggers 60% faster for each distinct Potion.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/9/96/HeavyDrinking.png/100px-HeavyDrinking.png?b9d2af", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Heroic Potion", "type": "Potion", "rarity": "Legendary", "cost": "6", "effect": "Out of stamina: Consume this and regenerate 2 stamina and gain 1 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/7/7b/HeroicPotion.png/51px-HeroicPotion.png?8acad0", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON> Longsword", "type": "Weapon", "rarity": "Legendary", "cost": "19", "effect": "Start of battle: Weapons gain 4 damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/f/f2/HeroLongsword.png/39px-HeroLongsword.png?7611b5", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Hero Sword", "type": "Weapon", "rarity": "Epic", "cost": "7", "effect": "Start of battle: Weapons gain 1 damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/9/9c/HeroSword.png/50px-HeroSword.png?d63678", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Holy Armor", "type": "Armor", "rarity": "Legendary", "cost": "13", "effect": "Start of battle: <PERSON><PERSON> 65 . <PERSON><PERSON> 2 for each -item. Every 2.6s: Cleanse 2 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/1b/HolyArmor.png/74px-HolyArmor.png?d4ea3c", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Holy Spear", "type": "Weapon", "rarity": "<PERSON><PERSON>", "cost": "18", "effect": "On hit: Destroy 10 and cleanse 1 debuff for each free slot or -item in front of it. Use 10 : Become invulnerable and attack 100% faster for 3s (once).", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/a1/HolySpear.png/20px-HolySpear.png?acefa5", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Hungry Blade", "type": "Weapon", "rarity": "Epic", "cost": "7", "effect": "Start of battle: <PERSON>ain 1 . On hit: Use 1 to gain 1 . Deals +1 maximum damage per .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/c8/HungryBlade.png/33px-HungryBlade.png?a58c63", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Ice Armor", "type": "Armor", "rarity": "Epic", "cost": "11", "effect": "Start of battle: <PERSON><PERSON> 45 and inflict 4 . Every 5s: Use 1 to inflict 2 and gain 10 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/ad/IceArmor.png/74px-IceArmor.png?2534e0", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Impractically Large Greatsword", "type": "Weapon", "rarity": "<PERSON><PERSON>", "cost": "14", "effect": "While you have at least 5 , decrease stamina usage to 2 and cooldown to 2s.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/5/5e/ImpracticallyLargeGreatsword.png/47px-ImpracticallyLargeGreatsword.png?a3cb92", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Investment Opportunity", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Shop entered: <PERSON>ain 1 . item used buff: <PERSON>ain 2 maximum health.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/a7/InvestmentOpportunity.png/100px-InvestmentOpportunity.png?edd557", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "It's Slime Time!", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Gooblings are offered in the shop. On buy: Generate a Goobling. Every 3s: Advance all Gooberts and Gooblings by 1 activation.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/c8/ItsSlimeTime.png/100px-ItsSlimeTime.png?67eb91", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Just Stats", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Start of battle: Gain 15% maximum health and 10% base stamina regeneration. Always offered in round 4.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/6/67/JustStats.png/100px-JustStats.png?87bd66", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Jynx torquilla", "type": "Pet", "rarity": "Legendary", "cost": "8", "effect": "Every 3s: items trigger 5% faster (up to 40%). Remove 1 from your opponent.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/9/9a/JynxTorquilla.png/100px-JynxTorquilla.png?e8b697", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON><PERSON>", "type": "Weapon", "rarity": "<PERSON><PERSON>", "cost": "14", "effect": "On hit: Remove 1 damage gained in battle from all opponent Weapons and gain 1 damage. If your opponent has at least 20 buffs, remove 2 of the type they have the most of.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/e/e9/Katana.png/14px-Katana.png?9183cc", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "King Crown", "type": "Accessory", "rarity": "<PERSON><PERSON>", "cost": "17", "effect": "Every 2.4s: Heal for 5 and protect 1 buff from removal. Use 10 : Become invulnerable for 2.5s (once). Effects of Gemstones socketed in this are increased by 50%.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/1a/KingCrown.png/96px-KingCrown.png?6bd401", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "King <PERSON><PERSON>", "type": "Pet", "rarity": "<PERSON><PERSON>", "cost": "23", "effect": "6 item activations: Heal for 35, protect 3 buffs from removal and use 4 to become invulnerable for 1.5s (up to 3 times). Effects of Gemstones socketed in this are increased by 50%.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/f/f0/KingGoobert.png/67px-KingGoobert.png?1300ab", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Leaf Badge", "type": "Accessory", "rarity": "Unique", "cost": "5", "effect": "Ranger items are offered in the shop. items gain 2% critical hit chance for each . Every 2.2s: Gain 1 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/d/d3/LeafBadge.png/100px-LeafBadge.png?7545d6", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON><PERSON> Armor", "type": "Armor", "rarity": "Rare", "cost": "7", "effect": "Start of battle: <PERSON><PERSON> 45 . <PERSON><PERSON> 3 debuffs.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/5/5c/LeatherArmor.png/100px-LeatherArmor.png?63e8dc", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON><PERSON>", "type": "Bag", "rarity": "Common", "cost": "4", "effect": "Add 4 backpack slots.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/c0/LeatherBag.png/100px-LeatherBag.png?e378e9", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON><PERSON>", "type": "Shoes", "rarity": "Epic", "cost": "6", "effect": "Health drops below 70%: Gain 1 , 1 and 15 (once).", "image_url": "https://backpackbattles.wiki.gg/images/thumb/5/57/LeatherBoots.png/69px-LeatherBoots.png?e51b16", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Light Goobert", "type": "Pet", "rarity": "<PERSON><PERSON>", "cost": "18", "effect": "6 item activations: Heal for 20 and inflict 6 for 3s.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/c2/LightGoobert.png/97px-LightGoobert.png?254e91", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Lightsaber", "type": "Weapon", "rarity": "<PERSON><PERSON>", "cost": "12", "effect": "Use 3 : Inflict 8 for 6s (unstackable). Deals +1 damage for each of your opponent.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/4/49/Lightsaber.png/23px-Lightsaber.png?7a3baf", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Lump of Coal", "type": "Gemstone", "rarity": "Common", "cost": "2", "effect": "Weapon sockets: On attack: 70% chance to deal +1 damage. Armor & other sockets: Start of battle: Gain 8 . Resist 1 debuff. Backpack: After 3s: <PERSON>ain a random buff, inflict a random debuff.", "image_url": "https://backpackbattles.wiki.gg/images/5/58/LumpofCoal.png?ce71b4", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Magic Staff", "type": "Weapon", "rarity": "Epic", "cost": "10", "effect": "On attack: Use 3 to deal +6 damage and gain 2 damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/7/7b/MagicStaff.png/25px-MagicStaff.png?382f05", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Magic Torch", "type": "Weapon", "rarity": "Legendary", "cost": "11", "effect": "On hit: Use 1 : This and Weapons gain 1 damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/0/01/MagicTorch.png/43px-MagicTorch.png?301fb7", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON><PERSON>y", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "<PERSON><PERSON> gain +17 random buffs. Every 6s: <PERSON><PERSON> 3 . Triggers 20% faster for each -item.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/0/00/ManaMastery.png/100px-ManaMastery.png?f2cfaf", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON><PERSON>", "type": "Accessory", "rarity": "Epic", "cost": "6", "effect": "item activates: 50% chance to gain 1 . Use 35 : <PERSON><PERSON> 17 random other buffs (once).", "image_url": "https://backpackbattles.wiki.gg/images/thumb/3/3d/ManaOrb.png/100px-ManaOrb.png?c746e7", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON><PERSON>", "type": "Potion", "rarity": "Epic", "cost": "6", "effect": "used or health drops below 50%: Consume this and gain 4 and 18 maximum health.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/c3/ManaPotion.png/48px-ManaPotion.png?a75fd1", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "Weapon", "rarity": "Legendary", "cost": "13", "effect": "On hit: <PERSON><PERSON> 2 . 30 gained: Deal 10 -damage with 100% lifesteal. Deal +1 for each .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/5/53/Manathirst.png/33px-Manathirst.png?886b1a", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "Accessory", "rarity": "Legendary", "cost": "10", "effect": "Sale chance +3%. Value of items > 20 15% chance to resist critical hits. Value of items > 40 Godly and Unique items trigger 15% faster.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/1a/Maneki-neko.png/62px-Maneki-neko.png?5c2910", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Moon Armor", "type": "Armor", "rarity": "<PERSON><PERSON>", "cost": "19", "effect": "Start of battle: <PERSON><PERSON> 50 + 20 for each -item. Every 2.6s: <PERSON><PERSON> 3 and reflect 2 debuffs.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/c4/MoonArmor.png/75px-MoonArmor.png?a05ebe", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Moon Shield", "type": "Shield", "rarity": "<PERSON><PERSON>", "cost": "18", "effect": "items give +30% . items gained 12 : Gain 1 . On attacked (/): 30% chance to prevent 12 damage and remove 0.7 stamina from your opponent.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/0/08/MoonShield.png/66px-MoonShield.png?933ba4", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "More Stats", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Start of battle: <PERSON><PERSON> 15% maximum health. Your Weapons deal +5% damage. Always offered in round 10.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/5/59/MoreStats.png/99px-MoreStats.png?4fbfd6", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Oil Lamp", "type": "Accessory", "rarity": "Epic", "cost": "7", "effect": "Start of battle: <PERSON><PERSON> 2 . Every 3.4s: The Weapon gains 1 damage and 5% accuracy.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/b/b9/OilLamp.png/43px-OilLamp.png?32a0e8", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Pan", "type": "Weapon", "rarity": "Common", "cost": "4", "effect": "Deals +1 damage for each Food.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/ca/Pan.png/100px-Pan.png?a43964", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Pandamonium", "type": "Weapon", "rarity": "Legendary", "cost": "11", "effect": "Food activates: Inflict 1 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/a6/Pandamonium.png/100px-Pandamonium.png?8b4cc3", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Pestilence Flask", "type": "Potion", "rarity": "Epic", "cost": "7", "effect": "Opponent heals: Consume this and inflict 3 and 1 to yourself.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/13/PestilenceFlask.png/55px-PestilenceFlask.png?a47944", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Piggybank", "type": "Accessory", "rarity": "Common", "cost": "3", "effect": "Shop entered: Gain 1 . Start of battle: Gain 2 maximum health for each Start of battle item.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/3/33/Piggybank.png/100px-Piggybank.png?48998a", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON><PERSON>", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Destroying a Piggybank generates items instead. Shop entered: Piggybanks have a 30% chance to explode. Items gain 5% critical hit chance.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/4/4e/PiggyPinata.png/85px-PiggyPinata.png?6de2c1", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Pineapple", "type": "Food", "rarity": "Legendary", "cost": "7", "effect": "Every 3.3s: <PERSON><PERSON> 1 and heal for 4.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/f/fb/Pineapple.png/39px-Pineapple.png?8bf553", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Platinum Customer Card", "type": "Accessory", "rarity": "Epic", "cost": "8", "effect": "Start of battle: Reflect 2 debuffs for each Legendary, Godly or Unique item. 20% chance to protect your buffs from removal. Chance to find -items +25%. You can obtain +1 -item.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/a1/PlatinumCustomerCard.png/100px-PlatinumCustomerCard.png?1f4932", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Pocket Sand", "type": "Accessory", "rarity": "Common", "cost": "2", "effect": "Start of battle: Inflict 1 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/3/3a/PocketSand.png/100px-PocketSand.png?4ca6c2", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON><PERSON>", "type": "Weapon", "rarity": "Epic", "cost": "11", "effect": "On hit: Inflict 2 . On stun: <PERSON>ggers extra attack.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/f/f4/PoisonDagger.png/50px-PoisonDagger.png?6c885a", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Pop", "type": "Weapon, Pet", "rarity": "Unique", "cost": "6", "effect": "Attacks 3% faster for each (up to 60%).", "image_url": "https://backpackbattles.wiki.gg/images/thumb/7/71/Pop.png/100px-Pop.png?d5088e", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Potion Belt", "type": "Bag", "rarity": "Legendary", "cost": "5", "effect": "Add 4 backpack slots. First Potion inside consumed: Gain a random buff. 4 Potions inside consumed: Cleanse 8 debuffs.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/9/94/PotionBelt.png/34px-PotionBelt.png?5d0ae9", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Power of the Moon", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Fatigue starts 4s earlier. Moon Armor activates: Inflict 1 . Moon Shield activates: Reflect 1 debuff. Fatigue starts: Heal for 50% of your maximum health.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/e/ea/PoweroftheMoon.png/99px-PoweroftheMoon.png?8f1f3c", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Present", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Shop entered: Instead of gold, you receive items with a higher value. Start of battle: Gain 5 random buffs.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/0/02/Present.png/93px-Present.png?5862f4", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Prismatic Orb", "type": "Accessory", "rarity": "<PERSON><PERSON>", "cost": "13", "effect": "Start of battle: For each... -item: <PERSON>ain 2 . -item: Gain 1 . -item: Increase your healing by 4%. -item: Inflict a random debuff. Every 8s: Gain 1 of every type of buff.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/2/2f/PrismaticOrb.png/100px-PrismaticOrb.png?e3c97b", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Prismatic Sword", "type": "Weapon", "rarity": "<PERSON><PERSON>", "cost": "15", "effect": "For each... -item: +8% attack speed. -item: +15% lifesteal. -item: Gain +0.3 damage on hit. -item: +12% chance to inflict 4 random debuffs on hit.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/3/3f/PrismaticSword.png/36px-PrismaticSword.png?177744", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Protective Purse", "type": "Bag", "rarity": "<PERSON><PERSON>", "cost": "2", "effect": "Add 1 backpack slot. Start of battle: <PERSON><PERSON> 15 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/8/85/ProtectivePurse.png/98px-ProtectivePurse.png?6da35e", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON><PERSON><PERSON>", "type": "Weapon, Food", "rarity": "Unique", "cost": "8", "effect": "On hit: 50% chance to stun for 0.5s. Fatigue starts: gain 10 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/6/64/Pumpkin.png/100px-Pumpkin.png?3f68d8", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON> Badge", "type": "Accessory", "rarity": "Unique", "cost": "5", "effect": "Items of all classes are offered in the shop. After 7s: Gain 1 of every type of buff.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/d/d2/RainbowBadge.png/100px-RainbowBadge.png?930831", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Ripsaw Blade", "type": "Weapon", "rarity": "Legendary", "cost": "10", "effect": "On hit: Remove 1 damage gained in battle from all opponent Weapons and gain 0.5 damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/14/RipsawBlade.png/27px-RipsawBlade.png?3ce9e7", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON>", "type": "Pet", "rarity": "Legendary", "cost": "10", "effect": "Start of battle: <PERSON><PERSON> 4 . <PERSON><PERSON>ct 3 debuffs. Hatches after 2 rounds in your backpack.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/0/08/RubyEgg.png/85px-RubyEgg.png?da83b1", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON>", "type": "Weapon, Pet", "rarity": "<PERSON><PERSON>", "cost": "10", "effect": "Start of battle: <PERSON><PERSON> 4 . <PERSON><PERSON><PERSON> 3 debuffs.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/2/2a/RubyWhelp.png/95px-RubyWhelp.png?d40007", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Sack of Surprises", "type": "Bag", "rarity": "Unique", "cost": "10", "effect": "Game started: Replace this with random starting bags and items.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/7/7f/SackofSurprises.png/100px-SackofSurprises.png?81eaa1", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Serpent Staff", "type": "Weapon", "rarity": "Legendary", "cost": "17", "effect": "On attack: Use 4 to gain 2 damage and inflict 1 for each 4 damage dealt. You have 30% chance to duplicate you inflict.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/2/25/SerpentStaff.png/30px-SerpentStaff.png?48236f", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Shell Totem", "type": "Weapon", "rarity": "Rare", "cost": "5", "effect": "Every 3.4s: If your health is above 70%, gain 1 . Otherwise, heal for 8. Uses -15% stamina for each -item.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/b/b3/ShellTotem.png/51px-ShellTotem.png?3a371c", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Shepherd's Crook", "type": "Accessory", "rarity": "Rare", "cost": "8", "effect": "Start of battle: Weapons gain 2 damage. 25% chance to protect your buffs from removal. 25% chance to resist and .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/9/9e/ShepherdsCrook.png/46px-ShepherdsCrook.png?ae7440", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Shielded", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Shields have +25% chance to block. Armors trigger 50% faster.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/cb/Shielded.png/98px-Shielded.png?910ad1", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Shield of Valor", "type": "Shield", "rarity": "Legendary", "cost": "12", "effect": "Items give 30% more . On attacked (): 30% chance to prevent 12 damage and remove 0.7 stamina from opponent.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/9/9e/ShieldofValor.png/63px-ShieldofValor.png?441118", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Shiny Shell", "type": "Accessory", "rarity": "Common", "cost": "2", "effect": "After 5s: Heal for 5 + 3 for each -item.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/d/db/ShinyShell.png/100px-ShinyShell.png?7fcb1d", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON><PERSON><PERSON>", "type": "Weapon", "rarity": "Rare", "cost": "8", "effect": "Shop entered: Dig up a random item. On hit: 40% chance to inflict 1 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/d/d5/Shovel_item.png/23px-Shovel_item.png?b58fc9", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Skull Badge", "type": "Accessory", "rarity": "Unique", "cost": "5", "effect": "Reaper items are offered in the shop. Every 1.5s: Inflict a random debuff.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/0/04/SkullBadge.png/79px-SkullBadge.png?d3d31d", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON><PERSON><PERSON>", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "<PERSON><PERSON><PERSON> gains +5 . 20 gained: Inflict 1 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/18/SmellyWall.png/88px-SmellyWall.png?89fb9d", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Smithing For Dummies", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "On buy: Generate a Whetstone. Crafted Weapons deal +1 damage and use -25% stamina.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/2/25/SmithingForDummies.png/100px-SmithingForDummies.png?700dbd", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Snowball", "type": "Accessory", "rarity": "Epic", "cost": "4", "effect": "Start of battle: Inflict 2 . Your opponent gains 15% less maximum health from items.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/0/04/Snowball.png/100px-Snowball.png?1473bb", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Snowcake", "type": "Food", "rarity": "Unique", "cost": "6", "effect": "Every 3s: Inflict 1 . If your opponent has at least 10 , increase -damage by 10% and deal 10 -damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/f/f8/Snowcake.png/96px-Snowcake.png?e44d58", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Snow Stick", "type": "Weapon", "rarity": "Legendary", "cost": "8", "effect": "On hit: Inflict 3 and 2 to yourself.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/8/8f/SnowStick.png/25px-SnowStick.png?ea2d24", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Spear", "type": "Weapon", "rarity": "Rare", "cost": "6", "effect": "On hit: Des<PERSON>y 4 for each free slot in front of this.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/4/4f/Spear.png/25px-Spear.png?887bc8", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON><PERSON><PERSON> Dagger", "type": "Weapon", "rarity": "Legendary", "cost": "10", "effect": "On attack: Use 1 to ignore and deal +7 damage. On stun: Triggers extra attack.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/12/SpectralDagger.png/37px-SpectralDagger.png?8b1fa5", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Spicy <PERSON>", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Banana activates: 30% chance to gain 1 . 1 stamina used: Heal for 3.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/11/SpicyBanana.png/100px-SpicyBanana.png?a7da4a", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Spiked Shield", "type": "Shield", "rarity": "Rare", "cost": "8", "effect": "On attacked (): 30% chance to prevent 7 damage, remove 0.3 stamina from opponent, and gain 1 (up to 4).", "image_url": "https://backpackbattles.wiki.gg/images/thumb/2/25/SpikedShield.png/100px-SpikedShield.png?e8ea95", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Stable Recombobulator", "type": "Accessory", "rarity": "Unique", "cost": "6", "effect": "Shop entered: Consume items. Create different items based on the combined value. Every 2.5s: Gain 1 random buff and cleanse 1 debuff.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/6/61/StableRecombobulator.png/100px-StableRecombobulator.png?e50b16", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON><PERSON><PERSON>", "type": "Bag", "rarity": "Epic", "cost": "5", "effect": "Add 3 backpack slots. Gain 1 maximum stamina.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/1b/StaminaSack.png/45px-StaminaSack.png?373760", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Steel Goobert", "type": "Pet", "rarity": "Legendary", "cost": "17", "effect": "5 item activations: Weapons gain +2 damage. Gain 16 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/0/08/SteelGoobert.png/97px-SteelGoobert.png?b13d4b", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Stone", "type": "Weapon", "rarity": "Common", "cost": "1", "effect": "Can only be thrown once per battle. On hit: Destroy 4 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/d/d4/Stone.png/100px-Stone.png?13e0c3", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Stone Armor", "type": "Armor", "rarity": "Legendary", "cost": "13", "effect": "Items use +20% stamina. Start of battle: <PERSON><PERSON> 90 . Every 4s: Remove 1 and 1 from opponent. Health drops below 50%: Gain equal to 40% of your missing health (once).", "image_url": "https://backpackbattles.wiki.gg/images/thumb/8/8d/StoneArmor.png/75px-StoneArmor.png?d0b5e8", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Stone Badge", "type": "Accessory", "rarity": "Unique", "cost": "5", "effect": "Your starting class items are no longer offered in the shop (even when this item is in storage). Shop entered: Generate items worth 1 . Every 3s: Gain 4 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/a3/StoneBadge.png/99px-StoneBadge.png?a5d1cd", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Stoned", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Stone or Stone Golem dealt damage: <PERSON>ain 45% of the damage as . While you have : 40% chance to resist critical hits.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/ab/Stoned.png/100px-Stoned.png?22d016", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Stone Golem", "type": "Weapon, Pet", "rarity": "<PERSON><PERSON>", "cost": "16", "effect": "On hit: <PERSON><PERSON> 1 . 30% chance to stun for 0.5s. Use 7 : Reduce cooldown to 2.6s and gain 150 (once). Deals +10 damage for each Bag of Stones.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/8/8e/StoneGolem.png/95px-StoneGolem.png?83ddb3", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON>", "type": "<PERSON><PERSON><PERSON>", "rarity": "Legendary", "cost": "13", "effect": "Start of battle: Reduce damage taken by 25% for 5s and gain 35 . 25% chance to resist critical hits. 30% chance to resist stuns.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/7/72/StoneHelm.png/58px-StoneHelm.png?451786", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Stone Shoes", "type": "Shoes", "rarity": "Legendary", "cost": "12", "effect": "Health drops below 70%: Gain 1 , 1 , and 30 . Reduce /-damage taken by 30% for 5s (once).", "image_url": "https://backpackbattles.wiki.gg/images/thumb/7/7d/StoneShoes.png/57px-StoneShoes.png?3b3e48", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Stone Skin Potion", "type": "Potion", "rarity": "Epic", "cost": "6", "effect": "45 reached: Consume this and convert 15 health to 30 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/ce/StoneSkinPotion.png/49px-StoneSkinPotion.png?d8b455", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Strong Health Potion", "type": "Potion", "rarity": "Epic", "cost": "8", "effect": "Health drops below 50%: Consume this and heal for 24, gain 3 and cleanse 4 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/cd/StrongHealthPotion.png/49px-StrongHealthPotion.png?86d443", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Strong Heroic Potion", "type": "Potion", "rarity": "Legendary", "cost": "9", "effect": "Out of stamina: Consume this and regenerate 4 stamina and gain 1 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/e/eb/StrongHeroicPotion.png/76px-StrongHeroicPotion.png?6b4913", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Strong Stone Skin Potion", "type": "Potion", "rarity": "Legendary", "cost": "9", "effect": "45 reached: Consume this and convert 15 health to 35 and gain 2 for 4s.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/1e/StrongStoneSkinPotion.png/55px-StrongStoneSkinPotion.png?7dd3d3", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Superspacious", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Bags appear more often in the shop and have +30% chance to be on sale. item trigger 9% faster for each free slot.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/e/e6/Superspacious.png/90px-Superspacious.png?be537c", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Thornbloom", "type": "Weapon", "rarity": "<PERSON><PERSON>", "cost": "14", "effect": "On hit: Gain 1 . 60% chance to gain 1 . gained: Gain 10 maximum health. Deals +1 damage per .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/6/6f/Thornbloom.png/97px-Thornbloom.png?24edc0", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Thornburst", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Every 10s: Stun for 0.5s and gain 3 (up to 3 times). Triggers 5% faster for each .", "image_url": "https://backpackbattles.wiki.gg/images/7/7e/Thornburst.png?1d3d6c", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Thorn Whip", "type": "Weapon", "rarity": "Epic", "cost": "8", "effect": "On hit: <PERSON><PERSON> 1 . Deals +1 damage per .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/7/7b/ThornWhip.png/100px-ThornWhip.png?e12059", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON>", "type": "Gemstone", "rarity": "Unique", "cost": "10", "effect": "Weapon sockets: On hit: 50% chance to steal a random buff. Armor & other sockets: 25% chance to resist debuffs or critical hits. Backpack: Opponent drops below 30%: Heal for 50 and gain 5 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/e/ea/Tim.png/76px-Tim.png?127103", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Time Dilator", "type": "Accessory", "rarity": "Unique", "cost": "8", "effect": "Your and your opponent's Weapons attack 30% slower. Every 1s: Your item with the highest cooldown triggers 6% faster.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/16/TimeDilator.png/77px-TimeDilator.png?ce5efb", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON>ch", "type": "Weapon", "rarity": "Rare", "cost": "5", "effect": "On hit: 25% chance to gain 1 damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/b/b2/Torch.png/45px-Torch.png?cbccb7", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Unidentified Amulet", "type": "Accessory", "rarity": "Rare", "cost": "6", "effect": "On buy: Gain a random effect.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/14/UnidentifiedAmulet.png/100px-UnidentifiedAmulet.png?658c73", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Uniquely Unique", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "The item triggers 20% faster +10% for each Unique item, Customer Card or Platinum Customer Card. Chance to find -items +50%. You can obtain +2 -items.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/e/ef/UniquelyUnique.png/94px-UniquelyUnique.png?a0756c", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Unsettling Presence", "type": "Pet", "rarity": "Unique", "cost": "10", "effect": "Deal +30% of your healing as -damage. Every 3s: Use a random buff to heal for 12.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/a0/UnsettlingPresence.png/54px-UnsettlingPresence.png?901958", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Unstable Recombobulator", "type": "Accessory", "rarity": "Epic", "cost": "6", "effect": "Shop entered: Consume this and items. Create different items based on the combined value. Every 4s: <PERSON>ain 1 random buff and cleanse 1 debuff.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/1e/UnstableRecombobulator.png/100px-UnstableRecombobulator.png?290fcb", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Vampiric Armor", "type": "Armor", "rarity": "Legendary", "cost": "16", "effect": "Start of battle: Convert 30 health into 65 and gain 2 . Every 2.8s: Convert 10 health into 20 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/f/fb/VampiricArmor.png/81px-VampiricArmor.png?91a173", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Vampiri<PERSON>s", "type": "Gloves", "rarity": "<PERSON><PERSON>", "cost": "12", "effect": "After 4s: <PERSON><PERSON> 5 , items trigger 35% faster.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/b/b8/VampiricGloves.png/100px-VampiricGloves.png?f2f79c", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Vampiric Potion", "type": "Potion", "rarity": "Legendary", "cost": "8", "effect": "Both characters drop below 80% health: Consume this and gain 3 and deal 15 -damage with 100% lifesteal.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/5/54/VampiricPotion.png/35px-VampiricPotion.png?8a26ce", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON>in <PERSON>", "type": "Weapon", "rarity": "Unique", "cost": "7", "effect": "-Weapons deal -2 damage. Deals +4 damage per -Weapon.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/6/6e/VillainSword.png/51px-VillainSword.png?bb320f", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON><PERSON><PERSON>", "type": "Accessory", "rarity": "Common", "cost": "4", "effect": "Start of battle: Gain 1 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/2/22/WalrusTusk.png/50px-WalrusTusk.png?525e95", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Whetstone", "type": "Accessory", "rarity": "Common", "cost": "4", "effect": "Start of battle: Weapons gain 1 damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/9/9f/Whetstone.png/100px-Whetstone.png?e5238e", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Winged Boots", "type": "Shoes", "rarity": "<PERSON><PERSON>", "cost": "13", "effect": "Health drops below 70%: Gain 1 , cleanse 15 debuffs and dodge the next 3 /-attacks (once).", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/af/WingedBoots.png/98px-WingedBoots.png?661cde", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON> Badge", "type": "Accessory", "rarity": "Unique", "cost": "5", "effect": "Berserker items are offered in the shop. Health drops below 50%: Enter Battle Rage for 5s (once). During Battle Rage: items trigger 25% faster. You take 20% reduced damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/5/57/WolfBadge.png/100px-WolfBadge.png?439d03", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "Pet", "rarity": "<PERSON><PERSON>", "cost": "12", "effect": "Increase base stamina regeneration by 0.7% for each buff you have. Every 5s: Gain 3 of the buff you have least of. Triggers 15% faster for each Pet.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/c3/Wolpertinger.png/100px-Wolpertinger.png?c48373", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Wonky Snowman", "type": "Accessory", "rarity": "Epic", "cost": "8", "effect": "On Buy: Split into 2 Snowballs.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/f/fc/WonkySnowman.png/57px-WonkySnowman.png?ea0ba3", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "<PERSON><PERSON>", "type": "Shield", "rarity": "Common", "cost": "4", "effect": "On attacked (): 30% chance to prevent 7 damage and remove 0.3 stamina from opponent.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/2/2c/WoodenBuckler.png/100px-WoodenBuckler.png?6a31a8", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Wooden Sword", "type": "Weapon", "rarity": "Common", "cost": "3", "effect": "", "image_url": "https://backpackbattles.wiki.gg/images/thumb/9/9b/WoodenSword.png/50px-WoodenSword.png?97eead", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items"}, {"name": "Acorn Ace", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Acorn Collars have more slots. Items affected by Acorn Collar use -8% stamina. Critwood Staffs use -75% stamina.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/9/96/AcornAce.png/79px-AcornAce.png?f89a68", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "Acorn Collar", "type": "Accessory", "rarity": "Epic", "cost": "6", "effect": "items gain 5% critical hit chance for each .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/f/f5/AcornCollar.png/100px-AcornCollar.png?70fbf6", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "Belladonna's Shade", "type": "Weapon", "rarity": "Legendary", "cost": "11", "effect": "On hit: 70% chance to inflict 2 and a random debuff.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/b/bf/BelladonnasShade.png/49px-BelladonnasShade.png?dcd99a", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "Belladonna's Whisper", "type": "Weapon", "rarity": "Legendary", "cost": "14", "effect": "For every 5 damage Weapon deals: Inflict +1 on the next attack. Deals +0.5 damage per of your opponent.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/ac/BelladonnasWhisper.png/97px-BelladonnasWhisper.png?85cfad", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "Big Bowl of Treats", "type": "Food", "rarity": "Unique", "cost": "10", "effect": "Every 3.7s: <PERSON><PERSON> 2 random buffs and make Food trigger 25% faster (up to 100%). All your Pets have a 20% chance to activate twice. Friends of the forest are offered in the shop.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/c3/BigBowlofTreats.png/100px-BigBowlofTreats.png?e9203e", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "Bow and Arrow", "type": "Weapon", "rarity": "Epic", "cost": "7", "effect": "Weapon hits: <PERSON><PERSON> gain +1 damage (up to 7).", "image_url": "https://backpackbattles.wiki.gg/images/thumb/7/7d/BowandArrow.png/100px-BowandArrow.png?fbd333", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "Carrot", "type": "Food", "rarity": "Rare", "cost": "3", "effect": "Every 3.2s: Cleanse 1 debuff. If you have at least 4 : 55% chance to gain 1 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/c3/Carrot.png/100px-Carrot.png?39787a", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "Carrot Goobert", "type": "Pet", "rarity": "Epic", "cost": "12", "effect": "6 item activations: Cleanse 4 random debuffs and gain 2 for 6s.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/8/88/CarrotGoobert.png/91px-CarrotGoobert.png?15c47d", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "Critwood Staff", "type": "Weapon", "rarity": "Legendary", "cost": "16", "effect": "On attack: Use 3 to deal +7 damage and for the next 1.2s, all your attacks are critical.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/3/33/CritwoodStaff.png/26px-CritwoodStaff.png?6cdd54", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "Fortuna's Grace", "type": "Weapon", "rarity": "Legendary", "cost": "11", "effect": "Start of battle: G<PERSON> 3 . Weapon crits: Attack twice on the next attack.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/d/d5/FortunasGrace.png/99px-FortunasGrace.png?a8c7d4", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "Fortuna's Hope", "type": "Weapon", "rarity": "Epic", "cost": "8", "effect": "On hit: 70% chance to gain 1 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/8/81/FortunasHope.png/46px-FortunasHope.png?3232da", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "Hedgehog", "type": "Pet", "rarity": "Epic", "cost": "7", "effect": "Every 5s: Deal 10 -damage + 0.5 for each . Health drops below 70%: Gain 2 and 15 (once). Triggers 15% faster for each Pet or Food.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/c4/Hedgehog.png/69px-Hedgehog.png?533403", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "<PERSON> Clover", "type": "Accessory", "rarity": "Rare", "cost": "2", "effect": "Start of battle: Gain 1 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/15/LuckyClover.png/100px-LuckyClover.png?eacc9e", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "<PERSON> Piggy", "type": "Accessory", "rarity": "Epic", "cost": "7", "effect": "Shop entered: Gain 1 . Start of battle: Gain 2 . Chance-based effects of the items are 15% more likely to trigger.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/6/64/LuckyPiggy.png/100px-LuckyPiggy.png?71ab13", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "Markswoman", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "-Weapons deal +20% damage, attack +30% faster and have +15% accuracy.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/e/ea/Markswoman.png/100px-Markswoman.png?922064", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "Mega Clover", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Sale chance +5% Chance to find -items +20%. Shop entered: Generate two Lucky Clovers 15 reached: G<PERSON> 25 random other buffs.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/2/2a/MegaClover.png/99px-MegaClover.png?18a7b3", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "Piercing Arrow", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Weapons deal +50% critical damage. They remove 15 on crit. Item activates: 65% chance to gain 1 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/1e/PiercingArrow.png/100px-PiercingArrow.png?87e6ba", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "Poison Ivy", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "You have a 5% chance to resist debuffs for each -item. gained: Inflict 2 . Opponent reaches 18 : They take +25% damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/0/02/PoisonIvy.png/100px-PoisonIvy.png?827379", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "Rainbow Goobert Megasludge Alphapuddle", "type": "Pet", "rarity": "<PERSON><PERSON>", "cost": "68", "effect": "6 item activations: Heal for 20, gain 20 , 2 and 2 , inflict 4 , and Weapons gain 4 damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/d/d7/RainbowGoobertMegasludgeAlphapuddle.png/97px-RainbowGoobertMegasludgeAlphapuddle.png?ccf123", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "<PERSON>", "type": "Bag", "rarity": "Unique", "cost": "16", "effect": "Add 6 backpack slots. Items inside gain 10% critical hit chance +3% for each .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/0/0a/RangerBag.png/89px-RangerBag.png?50230b", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "Rat", "type": "Pet", "rarity": "Common", "cost": "4", "effect": "Every 3.3s: Deal 5 -damage. 75% to inflict 1 . 10% to inflict 1 . Triggers 15% faster for each Pet or Food.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/8/8b/Rat.png/100px-Rat.png?eb7de9", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "Rat Chef", "type": "Pet", "rarity": "Rare", "cost": "8", "effect": "Start of battle: Gain 1 for each Food. Every 7s: Regenerate 2 stamina and gain 1 . Triggers 15% faster for each Pet or Food.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/7/75/RatChef.png/50px-RatChef.png?72a300", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "Shortbow", "type": "Weapon", "rarity": "Common", "cost": "4", "effect": "", "image_url": "https://backpackbattles.wiki.gg/images/thumb/f/f9/Shortbow.png/43px-Shortbow.png?b72e3e", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "Squirrel", "type": "Pet", "rarity": "Rare", "cost": "5", "effect": "Every 4s: Steal a random buff. Triggers 15% faster for each Pet or Food.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/4/41/Squirrel.png/93px-Squirrel.png?6782f8", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "Squirrel Archer", "type": "Weapon, Pet", "rarity": "Epic", "cost": "9", "effect": "On hit: Steal a random buff. Triggers 15% faster for each Pet or Food.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/aa/SquirrelArcher.png/100px-SquirrelArcher.png?73463e", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "Tusk Piercer", "type": "Weapon", "rarity": "Legendary", "cost": "11", "effect": "Start of battle: <PERSON><PERSON> 4 . Weapon hits: Use 1 to deal +9 damage on the next attack.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/8/87/TuskPiercer.png/100px-TuskPiercer.png?a05e0b", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "Tusk Poker", "type": "Weapon", "rarity": "Rare", "cost": "8", "effect": "On hit: 50% chance to gain 1 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/b/ba/TuskPoker.png/46px-TuskPoker.png?f3b61d", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "Vineweave <PERSON>", "type": "Bag", "rarity": "Unique", "cost": "20", "effect": "Add 9 backpack slots. Your healing is amplified by 10% + 3% per -item inside. In rounds 1 and 10, sale chance is increased by 20%.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/4/4b/VineweaveBasket.png/99px-VineweaveBasket.png?e6ea94", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "Yggdrasil Leaf", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Start of battle: <PERSON><PERSON> 2 and 1 for each -item. 5 used: Heal for 17 and cleanse 2 debuffs.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/e/e4/YggdrasilLeaf.png/97px-YggdrasilLeaf.png?cc6ffd", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_items"}, {"name": "Ace of Spades", "type": "Playing Card", "rarity": "Rare", "cost": "3", "effect": "On reveal: Your next hit is critical. If the number of cards before is odd, gain 2 and 3 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/c0/AceofSpades.png/59px-AceofSpades.png?d0b04a", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "<PERSON><PERSON><PERSON>", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Shop entered: Upgrade an adjacent Potion. Every 3.3s: Heal for 20 or gain 6 or gain 5 . Triggers 15% faster for each Food or Potion.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/3/37/Cauldron.png/100px-Cauldron.png?17ad56", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Cursed <PERSON>", "type": "Weapon", "rarity": "Unique", "cost": "10", "effect": "On stun: Tri<PERSON>s extra attack. On hit: Inflict 2 random debuffs. This and items have +1% accuracy and +1% critical hit chance per debuff of your opponent.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/3/32/CursedDagger.png/38px-CursedDagger.png?c7bc7b", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Darkest Lotus", "type": "Playing Card", "rarity": "<PERSON><PERSON>", "cost": "7", "effect": "On reveal: For each card before, gain 3 and remove 3 random buffs from your opponent.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/f/f1/DarkestLotus.png/59px-DarkestLotus.png?b29cdc", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Dark Ritual", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "After 17s: Inflict 20 debuffs, gain 10 . Triggers 20% faster for each -item.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/cf/DarkRitual.png/100px-DarkRitual.png?c65021", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "<PERSON> Scythe", "type": "Weapon", "rarity": "Legendary", "cost": "12", "effect": "Items inflict +100% . Opponent reaches 35 : Gain 50% critical hit chance.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/f/f9/DeathScythe.png/75px-DeathScythe.png?11a063", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Deck of Cards", "type": "Accessory", "rarity": "Rare", "cost": "3", "effect": "Playing cards are offered in the shop. Start of battle: Gain 2 . Start revealing the Playing card.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/f/f7/DeckofCards.png/60px-DeckofCards.png?7510e8", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "<PERSON><PERSON>", "type": "Potion", "rarity": "<PERSON><PERSON>", "cost": "7", "effect": "Opponent drops below 50%: Consume this and deal 0.45 -damage for every debuff of your opponent.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/9/9a/DemonicFlask.png/54px-DemonicFlask.png?ef3432", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Doom Cap", "type": "Food", "rarity": "<PERSON><PERSON>", "cost": "10", "effect": "Every 3s: Inflict 3 and reduce opponent's healing by 10%.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/4/42/DoomCap.png/100px-DoomCap.png?64f02f", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Fly Agaric", "type": "Food", "rarity": "Rare", "cost": "3", "effect": "Every 4.3s: Inflict 1 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/9/92/FlyAgaric.png/50px-FlyAgaric.png?b2eb07", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Heart of the Cards", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Requires: Deck of Cards. Card revealed: Gain 3 . If the card is at position 3 or higher in the chain, also gain 4 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/ab/HeartoftheCards.png/83px-HeartoftheCards.png?47a356", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Holo Fire Lizard", "type": "Playing Card", "rarity": "Legendary", "cost": "5", "effect": "On reveal: Increase -damage by 10%. Deal 12 -damage + 4 for each card before. Gain 4 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/7/7b/HoloFireLizard.png/59px-HoloFireLizard.png?1172e8", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Ice Dragon", "type": "Weapon, Pet", "rarity": "<PERSON><PERSON>", "cost": "15", "effect": "On hit: Inflict 1 . Opponent reaches 12 : <PERSON><PERSON> 50 . You take -20% -damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/a3/IceDragon.png/70px-IceDragon.png?3da955", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Jimbo", "type": "Playing Card", "rarity": "<PERSON><PERSON>", "cost": "5", "effect": "On reveal: <PERSON><PERSON> 6 random buffs. For each pair before: Resist 1 critical hit. For each three of a kind before: Your Weapons use -25% stamina. For each four of a kind before: Activate 2 random revealed cards (except <PERSON><PERSON>).", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/1d/Jimbo.png/59px-Jimbo.png?61e390", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Miss Fortune", "type": "Accessory", "rarity": "Epic", "cost": "7", "effect": "Every 2.1s: Use 1 to gain 3 buffs of the type of which you have the most.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/6/6a/MissFortune.png/51px-MissFortune.png?6935eb", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Mrs. <PERSON>", "type": "Accessory", "rarity": "Legendary", "cost": "7", "effect": "Every 3.5s: Remove 1 buff of each type from your opponent. Triggers 10% faster for each -item.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/1e/MrsStruggles.png/59px-MrsStruggles.png?553920", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Mr. <PERSON>", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Every 3s: Inflict Fatigue damage. On debuffed: 25% chance to inflict the same debuff. Health drops below 50%: Items trigger 140% faster for 8s (once). Plushies are offered in the shop.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/e/ea/MrStruggles.png/60px-MrStruggles.png?9a342c", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Mushroom Farm", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Shop entered: If you have at least 3 Mushrooms, generate a Fly Agaric. Start of battle: Mushrooms trigger 50% faster.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/a2/MushroomFarm.png/95px-MushroomFarm.png?b2c738", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Nocturnal Lock Lifter", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Start of battle: Gain 4 . Weapons steal 25% life + 8% per -item. Your healing is increased by 25%.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/d/d6/NocturnalLockLifter.png/100px-NocturnalLockLifter.png?78ddf9", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Poison Goobert", "type": "Pet", "rarity": "Epic", "cost": "12", "effect": "5 item activations: Cleanse 2 and inflict 4 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/3/39/PoisonGoobert.png/97px-PoisonGoobert.png?1ece43", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Rainbow Goobert Omegaooze Primeslime", "type": "Pet", "rarity": "<PERSON><PERSON>", "cost": "68", "effect": "6 item activations: Heal for 20, gain 20 and 2 , inflict 4 and 4 , and Weapons gain 4 damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/2/2c/RainbowGoobertOmegaoozePrimeslime.png/97px-RainbowGoobertOmegaoozePrimeslime.png?e2f1f6", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Relic Case", "type": "Bag", "rarity": "Unique", "cost": "12", "effect": "Add 4 backpack slots. Every 3.2s: Weapons inside deal +5% damage and use -5% stamina.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/3/30/RelicCase.png/34px-RelicCase.png?7ba8a0", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Reverse!", "type": "Playing Card", "rarity": "Rare", "cost": "3", "effect": "On reveal: Reflect 4 debuffs. If there are no duplicate cards before, steal 3 buffs.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/0/06/Reverse.png/59px-Reverse.png?7193f1", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "<PERSON>", "type": "Weapon, Pet", "rarity": "<PERSON><PERSON>", "cost": "15", "effect": "On hit: <PERSON><PERSON> 1 . When you have at least 12 : 30% chance to stun your opponent for 0.4s.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/6/6c/RubyChonk.png/67px-RubyChonk.png?8327b3", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Snake", "type": "Pet", "rarity": "Unique", "cost": "10", "effect": "4% chance for each to protect on your opponent from being cleased. Start of battle: <PERSON>ain 4 and 50 maximum health for each Pet. Every 2.3s: Inflict 2 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/5/5a/Snake.png/66px-Snake.png?8e8b4d", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Staff of Unhealing", "type": "Weapon", "rarity": "<PERSON><PERSON>", "cost": "19", "effect": "Every 2s: Heal for 20. Use 5 : For 2s, deal 100% of your healing as -damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/b/be/StaffofUnhealing.png/24px-StaffofUnhealing.png?a23899", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Storage Coffin", "type": "Bag", "rarity": "Unique", "cost": "20", "effect": "Add 8 backpack slots. Item inside activates: 25% chance to inflict 1 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/e/e2/StorageCoffin.png/63px-StorageCoffin.png?da623c", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Strong Demonic Flask", "type": "Potion", "rarity": "<PERSON><PERSON>", "cost": "14", "effect": "Opponent drops below 50% or you drop below 25%: Consume this and deal 0.75 -damage for each debuff of your opponent with 100% lifesteal. For 3s, reduce opponent's healing by 30%.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/17/StrongDemonicFlask.png/49px-StrongDemonicFlask.png?ba236d", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Strong Divine Potion", "type": "Potion", "rarity": "<PERSON><PERSON>", "cost": "15", "effect": "You reached 10 debuffs: Consume this and cleanse 10 debuffs and gain 8 random buffs.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/3/31/StrongDivinePotion.png/50px-StrongDivinePotion.png?1cbd04", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Strong Mana <PERSON>tion", "type": "Potion", "rarity": "Legendary", "cost": "7", "effect": "used or health drops below 50%: Consume this and gain 9 and 25 maximum health.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/b/bd/StrongManaPotion.png/48px-StrongManaPotion.png?6a7dd2", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Strong Pestilence Flask", "type": "Potion", "rarity": "Legendary", "cost": "10", "effect": "Opponent regenerates health: Consume this and inflict 3 and 1 to yourself. After 6s, inflict another 3 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/7/7a/StrongPestilenceFlask.png/49px-StrongPestilenceFlask.png?3c4e40", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Strong Vampiric Potion", "type": "Potion", "rarity": "<PERSON><PERSON>", "cost": "12", "effect": "Both characters drop below 80% health: Consume this and gain 5 and 35% lifesteal for 6s.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/3/33/StrongVampiricPotion.png/47px-StrongVampiricPotion.png?4bc394", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "The Fool", "type": "Playing Card", "rarity": "Epic", "cost": "4", "effect": "On reveal: Card are revealed 50% faster. If this is the first card in the chain: Gain 1 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/c9/TheFool.png/59px-TheFool.png?e6e853", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "The Lovers", "type": "Playing Card", "rarity": "Common", "cost": "3", "effect": "On reveal: Deal 10 -damage with 100% lifesteal. If the number of cards before is even, gain 2 and your healing is increased by 10%.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/12/TheLovers.png/60px-TheLovers.png?e287e6", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "Toad", "type": "Pet", "rarity": "Epic", "cost": "6", "effect": "items gained 10 buffs: Heal for 12. items used 10 buffs: <PERSON><PERSON> 1 and 1 . Every 3.8s: <PERSON>ain 1 and 1 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/c9/Toad.png/100px-Toad.png?6854d1", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "White-Eyes Blue Dragon", "type": "Playing Card", "rarity": "Epic", "cost": "4", "effect": "On reveal: You take -10% -damage. <PERSON><PERSON> 12 + 6 for each card before. Inflict 4 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/f/f7/WhiteEyesBlueDragon.png/59px-WhiteEyesBlueDragon.png?5d0e93", "source_url": "https://backpackbattles.wiki.gg/wiki/Reaper_items"}, {"name": "An<PERSON>", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Item crafted: Generate a Flame. For each crafted item, the Weapons deal +1 damage and use -5% stamina.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/d/dd/Anvil.png/100px-Anvil.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "Armored Courage Puppy", "type": "Weapon, Pet", "rarity": "<PERSON><PERSON>", "cost": "7", "effect": "Cannot be blocked by shields or trigger . Deals +2 damage for each Pet.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/2/23/ArmoredCouragePuppy.png/100px-ArmoredCouragePuppy.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "Armored Power Puppy", "type": "Pet", "rarity": "<PERSON><PERSON>", "cost": "7", "effect": "Every 2.8s: Randomly gain 1 or 1 or 1 . Triggers 10% faster for each Pet. Triggers 20% faster for each Food.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/8/8f/ArmoredPowerPuppy.png/100px-ArmoredPowerPuppy.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "Armored Wisdom Puppy", "type": "Pet", "rarity": "<PERSON><PERSON>", "cost": "7", "effect": "Every 4s: <PERSON><PERSON> 14 and cleanse 1 . Increase gain by 1. Triggers 15% faster for each Pet.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/5/5a/ArmoredWisdomPuppy.png/51px-ArmoredWisdomPuppy.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "Axe", "type": "Weapon", "rarity": "Rare", "cost": "6", "effect": "On hit: Gain 1 damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/f/fb/Axe.png/54px-Axe.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "Badger Rune", "type": "Gemstone", "rarity": "Legendary", "cost": "4", "effect": "Weapon sockets: On hit: Attack 3% faster. Armor & other sockets: During Battle Rage: Reduce / damage taken by 7. Backpack: Items use -10% stamina.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/8/82/BadgerRune.png/89px-BadgerRune.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "<PERSON> Knuckles", "type": "Weapon", "rarity": "Unique", "cost": "10", "effect": "On hit: 30% chance to stun for 0.5s, this and Items gain 5% accuracy and 5% critical hit chance. During Battle Rage: <PERSON>gger 50% faster.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/4/45/BrassKnuckles.png/100px-BrassKnuckles.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "Busted Blade", "type": "Weapon", "rarity": "<PERSON><PERSON>", "cost": "14", "effect": "During Battle Rage: Decrease stamina usage to 3 and cooldown to 3s. Deals +5 damage per .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/a9/BustedBlade.png/36px-BustedBlade.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "Chain Whip", "type": "Weapon", "rarity": "Legendary", "cost": "8", "effect": "During Battle Rage additionally heal for 8. Deals +1 damage for each buff you removed from your opponent. On hit: Remove 2 random buffs from your opponent.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/9/93/ChainWhip.png/98px-ChainWhip.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "Cheese", "type": "Food", "rarity": "Legendary", "cost": "8", "effect": "Every 4s: <PERSON><PERSON> 10 maximum health and a random buff.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/a5/Cheese.png/100px-Cheese.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "Cheese Goobert", "type": "Pet", "rarity": "<PERSON><PERSON>", "cost": "14", "effect": "5 item activations: <PERSON><PERSON> 18 maximum health and 2 random buffs.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/c4/CheeseGoobert.png/97px-CheeseGoobert.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "<PERSON>", "type": "Weapon, Pet", "rarity": "Legendary", "cost": "7", "effect": "Deals +2 damage for each Pet.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/8/87/CouragePuppy.png/100px-CouragePuppy.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "Deerwood Guardian", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Damage taken reduced by 10%. Battle Rage lasts 0.8s longer for each -item. Every 1s during Battle Rage: Heal for 7 and gain 2 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/7/77/DeerwoodGuardian.png/49px-DeerwoodGuardian.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "Double Axe", "type": "Weapon", "rarity": "Epic", "cost": "12", "effect": "On hit: G<PERSON> 2 damage. Battle Rage entered: <PERSON><PERSON> extra attack. Damage gain increased to 3.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/d/d7/DoubleAxe.png/62px-DoubleAxe.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "Dragon Claws", "type": "Gloves", "rarity": "Epic", "cost": "4", "effect": "10% chance to resist . During Battle Rage: Items trigger 40% faster.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/c5/DragonClaws.png/100px-DragonClaws.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "Dragonscale Armor", "type": "Armor", "rarity": "Epic", "cost": "7", "effect": "Battle Rage entered: <PERSON><PERSON> 40 . During Battle Rage: Damage taken reduced by 10%.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/1f/DragonscaleArmor.png/77px-DragonscaleArmor.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "Dragon Set", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Every 1.2s during Battle Rage: Gain 1 . If you have Dragonscale Armor, Dragonskin Boots and Dragon Claws: You have +2% lifesteal for each (up to 20%).", "image_url": "https://backpackbattles.wiki.gg/images/thumb/f/fd/DragonSet.png/100px-DragonSet.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "<PERSON><PERSON> Boots", "type": "Shoes", "rarity": "Epic", "cost": "6", "effect": "20% chance to resist . Battle Rage entered: Cleanse 3 debuffs, gain 1 and 20 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/6/6b/DragonskinBoots.png/58px-DragonskinBoots.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "<PERSON><PERSON> Bag", "type": "Bag", "rarity": "Unique", "cost": "16", "effect": "Add 6 backpack slots. Health drops below 50%: Enter Battle Rage for 5s (once). During Battle Rage: Items inside trigger 30% faster. You take 20% reduced damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/d/d7/DuffleBag.png/100px-DuffleBag.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "Elephant Rune", "type": "Gemstone", "rarity": "Legendary", "cost": "4", "effect": "Weapon sockets: On hit: 25% chance to stun for 0.5s (cooldown 3s). Armor & other sockets: Start of battle: 40% chance to resist debuffs for 4s. Backpack: Gain 40 maximum health.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/d/de/ElephantRune.png/100px-ElephantRune.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "Extra Angy", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Battle Rage ended: After 4s, enter Battle Rage for 50% of the duration (once).", "image_url": "https://backpackbattles.wiki.gg/images/thumb/0/04/ExtraAngy.png/100px-ExtraAngy.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "Forging Hammer", "type": "Weapon", "rarity": "Unique", "cost": "3", "effect": "Deals additional +1 damage per .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/b/b1/ForgingHammer.png/53px-ForgingHammer.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "<PERSON>", "type": "Gemstone", "rarity": "Legendary", "cost": "4", "effect": "Weapon sockets: Critical hit chance +15%. Critical damage +15%. Armor & other sockets: 40% chance to resist . Backpack: Every 4s: Inflict 1 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/1e/HawkRune.png/91px-HawkRune.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "Power Puppy", "type": "Pet", "rarity": "Legendary", "cost": "7", "effect": "Every 3.2s: Randomly gain 1 or 1 or 1 . Triggers 10% faster for each Pet.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/d/d7/PowerPuppy.png/100px-PowerPuppy.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "<PERSON> Go<PERSON>rt <PERSON><PERSON>", "type": "Pet", "rarity": "<PERSON><PERSON>", "cost": "70", "effect": "6 item activations: Gain 20 maximum health, 20 , 2 and 2 random buffs, inflict 4 , and Weapons gain 4 damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/2/2f/RainbowGoobertDeathslushyMansquisher.png/97px-RainbowGoobertDeathslushyMansquisher.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "Shaman <PERSON>", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Runes are offered in the shop. Start of battle: Gain 1 for each socketed Gemstone. Every 3.4s: Use 2 to gain 5 random buffs.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/f/fa/ShamanMask.png/54px-ShamanMask.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "<PERSON><PERSON>", "type": "Accessory", "rarity": "Legendary", "cost": "6", "effect": "Battle Rage lasts 2s longer. Battle Rage entered: Gain 1 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/9/95/SpikedCollar.png/100px-SpikedCollar.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "Spiked Staff", "type": "Weapon", "rarity": "Legendary", "cost": "16", "effect": "On attack: Use 3 to gain 2 , and during Battle Rage also gain 2 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/a0/SpikedStaff.png/29px-SpikedStaff.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "Utility Pouch", "type": "Bag", "rarity": "Unique", "cost": "18", "effect": "Add 8 backpack slots. Weapons inside deal +30% damage but attack 30% slower. After 5s: Enter Battle Rage for 6s. During Battle Rage: +35% lifesteal.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/e/e2/UtilityPouch.png/96px-UtilityPouch.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "<PERSON>", "type": "Pet", "rarity": "Legendary", "cost": "7", "effect": "Every 4s: <PERSON><PERSON> 10 and cleanse 1 . <PERSON><PERSON>s 15% faster for each Pet.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/8/87/WisdomPuppy.png/51px-WisdomPuppy.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "<PERSON>", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Wolf companions are offered in the shop. Weapons have 10% critical hit chance (+12% for each Pet). Every 3s: If you have at least 10 , gain 1 . Otherwise, gain 10 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/5/58/WolfEmblem.png/81px-WolfEmblem.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Berserker_items"}, {"name": "Amethyst Egg", "type": "Pet", "rarity": "Legendary", "cost": "10", "effect": "Start of battle: Inflict 4 random debuffs. Hatches after 2 rounds in your backpack.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/6/67/AmethystEgg.png/80px-AmethystEgg.png?256a99", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Amethyst Whelp", "type": "Weapon", "rarity": "<PERSON><PERSON>", "cost": "14", "effect": "Start of battle: Inflict 4 random debuffs. On hit: Remove a random buff from your opponent.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/3/36/AmethystWhelp.png/97px-AmethystWhelp.png?2ee1a9", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Book of Ice", "type": "Accessory", "rarity": "Rare", "cost": "10", "effect": "Every 3.2s: Use 2 to inflict 3 . 10% chance to cast the Spell scroll for free.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/e/e7/BookofIce.png/56px-BookofIce.png?81e58d", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Burning Banner", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "25% chance to protect your buffs from removal and your opponent's debuffs from cleansing. -item activates: 50% chance to inflict 1 for 5s. Every 3.8s: Remove 2 buffs from your opponent and gain 2 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/5/57/BurningBanner.png/54px-BurningBanner.png?6dcf37", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Burning Blade", "type": "Weapon", "rarity": "Legendary", "cost": "21", "effect": "On hit: Gain 1 . 4 gained: This and Weapons gain +1 damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/3/36/BurningBlade.png/29px-BurningBlade.png?adcbc6", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Burning Sword", "type": "Weapon", "rarity": "Epic", "cost": "13", "effect": "On hit: 40% chance to gain 1 . 5 gained: This and Weapons gain +1 damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/4/4d/BurningSword.png/42px-BurningSword.png?212fca", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "<PERSON><PERSON>", "type": "Pet", "rarity": "Epic", "cost": "11", "effect": "6 item activations: Heal for 12 and gain 2 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/6/66/ChiliGoobert.png/97px-ChiliGoobert.png?5e013b", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Chili Pepper", "type": "Food", "rarity": "Rare", "cost": "5", "effect": "Every 5s: <PERSON><PERSON> 1 and heal 5. When you have at least 10 , cleanse 1 debuff.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/5/51/ChiliPepper.png/29px-ChiliPepper.png?621919", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Dark Lantern", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Start of battle: Lose 50% health. Before defeat: Reincarnate with 50% life and become invulnerable for 1.5s. On reincarnation: Deal 5 -damage for each -item and inflict 7 debuffs for each -item.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/0/05/DarkLantern.png/49px-DarkLantern.png?722d6f", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Draconic Orb", "type": "Accessory", "rarity": "Epic", "cost": "8", "effect": "15 reached: Your next 3 hits are critical. Every 3.8s: Remove 1 from your opponent and gain 1 per removed .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/3/32/DraconicOrb.png/82px-DraconicOrb.png?1e0df7", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Dragon Nest", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Start of battle: <PERSON><PERSON> 2 , 2 , 4 and 2 . Dragon attacks: Heal for 5. Dragon Eggs hatch after 1 round. Additional dragon eggs are offered in the shop.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/6/6d/DragonNest.png/100px-DragonNest.png?7fe9b5", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Emerald Egg", "type": "Pet", "rarity": "Legendary", "cost": "10", "effect": "Start of battle: <PERSON><PERSON> 3 . <PERSON><PERSON> after 2 rounds in your backpack.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/c9/EmeraldEgg.png/76px-EmeraldEgg.png?54c00b", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Emerald Whelp", "type": "Weapon, Pet", "rarity": "<PERSON><PERSON>", "cost": "14", "effect": "Start of battle: <PERSON><PERSON> 3 . On hit: Inflict 3 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/15/EmeraldWhelp.png/100px-EmeraldWhelp.png?d231c8", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Everburning", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Burning Sword and Burning Blade use -40% stamina. After 7s: Gain 1 for each Flame.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/7/70/Everburning.png/95px-Everburning.png?6bd11a", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Fire Pit", "type": "Bag", "rarity": "Unique", "cost": "20", "effect": "Add 9 backpack slots. Shop entered: Spend 1 gold to generate Flame. Start battle: <PERSON>ain 4 maximum health for each -item inside.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/2/24/FirePit.png/98px-FirePit.png?8d1799", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Flame", "type": "Accessory", "rarity": "Common", "cost": "1", "effect": "Start of battle: Gain 1 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/9/9f/Flame.png/80px-Flame.png?ae260e", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Flame Whip", "type": "Weapon", "rarity": "Legendary", "cost": "10", "effect": "On hit: Use 1 to gain 4 and deal +8 damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/d/d6/FlameWhip.png/100px-FlameWhip.png?e1160a", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Friendly Fire", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Every 3s: Use 1 to gain 2 . Triggers 10% faster for each -item. 20 reached: Gain 5 . 40 reached: Gain 15 . 60 reached: Deal 100 -damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/0/08/FriendlyFire.png/50px-FriendlyFire.png?491cec", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Frozen Flame", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Start of battle: <PERSON><PERSON> 8 for each -item. 6 gained: Inflict 2 . For each of your opponent, the item has +1.5% critical hit chance and +2% critical damage. Additional -items are offered in the shop.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/4/4c/FrozenFlame.png/51px-FrozenFlame.png?5b51d5", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Ice Dragon", "type": "Weapon, Pet", "rarity": "<PERSON><PERSON>", "cost": "15", "effect": "On hit: Inflict 1 . Opponent reaches 12 : <PERSON><PERSON> 50 . You take -20% -damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/a3/IceDragon.png/70px-IceDragon.png?3da955", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "<PERSON><PERSON> Dagger", "type": "Weapon", "rarity": "Epic", "cost": "6", "effect": "On hit: Use 1 to gain 2 damage. On stun: Triggers extra attack.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/6/6b/MoltenDagger.png/39px-MoltenDagger.png?a96fcf", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "<PERSON><PERSON>pear", "type": "Weapon", "rarity": "Epic", "cost": "8", "effect": "Before miss: Use 1 to hit instead and deal +5 damage. On hit: Destroy 5 for each -item in front of it.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/a2/MoltenSpear.png/25px-MoltenSpear.png?775772", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Obsidian Dragon", "type": "Weapon, Pet", "rarity": "<PERSON><PERSON>", "cost": "18", "effect": "8 gained: G<PERSON> 2 damage and the next hit of the Weapon is critical.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/b/b7/ObsidianDragon.png/70px-ObsidianDragon.png?a412ef", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Offering Bowl", "type": "Bag", "rarity": "Unique", "cost": "8", "effect": "Add 4 backpack slots. Start of battle: Gain 1 . Shop entered: Consume all items inside. Create a Flame and different items based on the combined value.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/a/a2/OfferingBowl.png/100px-OfferingBowl.png?a603b9", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Phoenix", "type": "Weapon, Pet", "rarity": "Legendary", "cost": "11", "effect": "On attack: Lose 11 health. Before defeat: Use all your to reincarnate with 6 health per (once).", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/c1/Phoenix.png/96px-Phoenix.png?c8ba8a", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Rainbow Goobert Epicglob Uberviscous", "type": "Pet", "rarity": "<PERSON><PERSON>", "cost": "67", "effect": "6 item activations: Heal for 20, gain 20 , 2 and 4 , inflict 4 , and Weapons gain 4 damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/c/ca/RainbowGoobertEpicglobUberviscous.png/97px-RainbowGoobertEpicglobUberviscous.png?f34637", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Sapphire Egg", "type": "Pet", "rarity": "Legendary", "cost": "10", "effect": "Start of battle: <PERSON><PERSON> 4 . <PERSON><PERSON> after 2 rounds in your backpack.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/7/7a/SapphireEgg.png/79px-SapphireEgg.png?14f4fd", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Sapphire Whelp", "type": "Weapon, Pet", "rarity": "<PERSON><PERSON>", "cost": "14", "effect": "Start of battle: <PERSON><PERSON> 4 . On hit: Use 2 to gain 5 and a random other buff.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/3/3a/SapphireWhelp.png/94px-SapphireWhelp.png?a219d5", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Solar<PERSON>", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "After 10s: Cleanse 20 debuffs. Sun Shield blocked damage: 30% chance to gain 1 . Sun Armor uses : Gain 10 .", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/1f/Solaris.png/100px-Solaris.png?e9d760", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Spell Scroll: <PERSON><PERSON>", "type": "Spell Scroll", "rarity": "Rare", "cost": "4", "effect": "Every 3s: Deal 5 -damage and inflict 4 for 3s. Max uses: 3 + 1 for each -item (except Spell scrolls).", "image_url": "https://backpackbattles.wiki.gg/images/thumb/d/d4/SpellScrollFrostbolt.png/100px-SpellScrollFrostbolt.png?a5386f", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Staff of Fire", "type": "Weapon", "rarity": "Legendary", "cost": "18", "effect": "On attack: Use 2 and 2 to gain 6 damage.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/0/07/StaffofFire.png/28px-StaffofFire.png?4668cb", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Sun Armor", "type": "Armor", "rarity": "<PERSON><PERSON>", "cost": "15", "effect": "-Items gain Holy. Start of battle: <PERSON><PERSON> 70 . <PERSON>ain 1 for each -item. Every 3s: Use 1 to heal for 12 and cleanse 2 debuffs.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/7/72/SunArmor.png/75px-SunArmor.png?343367", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}, {"name": "Sun Shield", "type": "Shield", "rarity": "<PERSON><PERSON>", "cost": "14", "effect": "items gained 12 : Deal 4 -damage. On attacked (/): 30% chance to prevent 14 damage and remove 0.7 stamina from your opponent.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/1/19/SunShield.png/64px-SunShield.png?d46a67", "source_url": "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"}], "recipes": [{"result": "Amulet of Alchemy", "ingredients": ["Unidentified Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Amulet of Energy", "ingredients": ["Unidentified Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Amulet of Feasting", "ingredients": ["Unidentified Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Amulet of Life", "ingredients": ["Unidentified Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Amulet of Steel", "ingredients": ["Unidentified Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Box of Prosperity", "ingredients": ["Box of Riches"], "catalyst": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Bunch of Coins", "ingredients": ["Piggybank"], "catalyst": "Hammer", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Gloves of Power", "ingredients": ["Gloves of Haste", "Stone Skin Potion"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Goobert", "ingredients": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Platinum Customer Card", "ingredients": ["Customer Card", "Customer Card"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Shepherd's Crook", "ingredients": ["Broom", "Whetstone"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Spiked Shield", "ingredients": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Steel Goobert", "ingredients": ["Goobert", "Hero Sword"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Stone Armor", "ingredients": ["<PERSON><PERSON> Armor", "Stone Skin Potion"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON>", "ingredients": ["Cap of Resilience", "Stone Skin Potion"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Stone Shoes", "ingredients": ["<PERSON><PERSON>", "Stone Skin Potion"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Health Potion", "ingredients": ["Health Potion"], "catalyst": "<PERSON><PERSON><PERSON>", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Health Potion", "ingredients": ["Health Potion", "Healing Herbs"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Heroic Potion", "ingredients": ["Heroic Potion"], "catalyst": "<PERSON><PERSON><PERSON>", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Heroic Potion", "ingredients": ["Heroic Potion", "Banana"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Stone Skin Potion", "ingredients": ["Stone Skin Potion", "Stone", "Stone"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Stone Skin Potion", "ingredients": ["Stone Skin Potion"], "catalyst": "<PERSON><PERSON><PERSON>", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Amulet of Darkness", "ingredients": ["Unidentified Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Heart of Darkness", "ingredients": ["Heart Container", "Corrupted Crystal"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "King Crown", "ingredients": ["Glowing Crown", "Box of Riches"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Snowball", "ingredients": ["Wonky Snowman"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Amulet of the Wild", "ingredients": ["Unidentified Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Corrupted Armor", "ingredients": ["Holy Armor", "Corrupted Crystal"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Moon Armor", "ingredients": ["Holy Armor", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Ice Armor", "ingredients": ["<PERSON><PERSON> Armor", "Snowball"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Vampiric Armor", "ingredients": ["<PERSON><PERSON> Armor", "Blood Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Burning Coal", "ingredients": ["Lump of Coal"], "catalyst": "Fire", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Vampiri<PERSON>s", "ingredients": ["Gloves of Haste", "Blood Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Cap of Discomfort", "ingredients": ["Cap of Resilience", "Corrupted Crystal"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "King <PERSON><PERSON>", "ingredients": ["Goobert", "King Crown"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Light Goobert", "ingredients": ["Goobert", "Lightsaber"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Blood Goobert", "ingredients": ["Goobert", "Blood Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON>", "ingredients": ["Health Potion", "Blueberries"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Vampiric Potion", "ingredients": ["Strong Health Potion"], "catalyst": "Blood Amulet", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Moon Shield", "ingredients": ["Shield of Valor", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON><PERSON>", "ingredients": ["<PERSON><PERSON>", "Snowball"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Stone Golem", "ingredients": ["Heart Container", "Stone", "Stone", "Stone", "Stone"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON>", "ingredients": ["<PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Darksaber", "ingredients": ["Lightsaber", "Corrupted Crystal"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Pandamonium", "ingredients": ["Pan", "Corrupted Crystal"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Burning Torch", "ingredients": ["<PERSON>ch"], "catalyst": "Fire", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Eggscalibur", "ingredients": ["Pan", "Heroic Potion"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Holy Spear", "ingredients": ["Spear", "Glowing Crown"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Shell Totem", "ingredients": ["Wooden Sword", "Shiny Shell"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Snow Stick", "ingredients": ["Broom", "Snowball"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Frostbite", "ingredients": ["Hungry Blade", "Snowball"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Magic Torch", "ingredients": ["<PERSON>ch", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON><PERSON> Dagger", "ingredients": ["<PERSON>gger", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Magic Staff", "ingredients": ["Broom", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Serpent Staff", "ingredients": ["Magic Staff", "Pestilence Flask"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Prismatic Sword", "ingredients": ["Wooden Sword", "Prismatic Orb"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON><PERSON><PERSON>", "ingredients": ["Hungry Blade", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Claws of Attack", "ingredients": ["Gloves of Haste", "<PERSON><PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Crossblades", "ingredients": ["Falcon Blade", "<PERSON> Longsword"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Falcon Blade", "ingredients": ["Hero Sword", "Gloves of Haste", "Gloves of Haste"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON> Longsword", "ingredients": ["Hero Sword", "Whetstone", "Whetstone"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Hero Sword", "ingredients": ["Wooden Sword", "Whetstone", "Whetstone"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON>", "ingredients": ["Ripsaw Blade", "Whetstone"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON>", "ingredients": ["<PERSON>gger", "Pestilence Flask"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON><PERSON>", "ingredients": ["Broom", "Pan"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON>ch", "ingredients": ["Wooden Sword", "Lump of Coal"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Thornbloom", "ingredients": ["Thorn Whip", "Heroic Potion"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Bloody Dagger", "ingredients": ["<PERSON>gger", "Blood Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON><PERSON><PERSON>", "ingredients": ["Hungry Blade", "Thorn Whip"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Armored Power Puppy", "ingredients": ["Power Puppy"], "catalyst": "Utility Pouch", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Armored Power Puppy", "ingredients": ["Power Puppy"], "catalyst": "Forging Hammer", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Armored Wisdom Puppy", "ingredients": ["<PERSON>"], "catalyst": "Utility Pouch", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Armored Wisdom Puppy", "ingredients": ["<PERSON>"], "catalyst": "Forging Hammer", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Dragon Claws", "ingredients": ["Gloves of Haste"], "catalyst": "Utility Pouch", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Dragon Claws", "ingredients": ["Gloves of Haste"], "catalyst": "Forging Hammer", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Dragonscale Armor", "ingredients": ["<PERSON><PERSON> Armor"], "catalyst": "Utility Pouch", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Dragonscale Armor", "ingredients": ["<PERSON><PERSON> Armor"], "catalyst": "Forging Hammer", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON> Boots", "ingredients": ["<PERSON><PERSON>"], "catalyst": "Utility Pouch", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON> Boots", "ingredients": ["<PERSON><PERSON>"], "catalyst": "Forging Hammer", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Cheese Goobert", "ingredients": ["Goobert", "Cheese"], "catalyst": "", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Armored Courage Puppy", "ingredients": ["<PERSON>"], "catalyst": "Utility Pouch", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Armored Courage Puppy", "ingredients": ["<PERSON>"], "catalyst": "Forging Hammer", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Chain Whip", "ingredients": ["Thorn Whip"], "catalyst": "Forging Hammer", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Chain Whip", "ingredients": ["Thorn Whip"], "catalyst": "Utility Pouch", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Spiked Staff", "ingredients": ["Magic Staff", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Busted Blade", "ingredients": ["Impractically Large Greatsword"], "catalyst": "Forging Hammer", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Busted Blade", "ingredients": ["Impractically Large Greatsword"], "catalyst": "Utility Pouch", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Double Axe", "ingredients": ["Axe", "Axe"], "catalyst": "", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Sun Armor", "ingredients": ["Holy Armor", "Flame", "Flame"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON>", "ingredients": ["Goobert", "Chili Pepper"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Rainbow Goobert Epicglob Uberviscous", "ingredients": ["Goobert", "Blood Goobert", "<PERSON><PERSON>", "Light Goobert", "Steel Goobert"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Sun Shield", "ingredients": ["Shield of Valor", "Flame", "Flame"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Emerald Whelp", "ingredients": ["Emerald Egg"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Obsidian Dragon", "ingredients": ["<PERSON>", "Draconic Orb"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Sapphire Whelp", "ingredients": ["Sapphire Egg"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Amethyst Whelp", "ingredients": ["Amethyst Egg"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Staff of Fire", "ingredients": ["Magic Staff", "Draconic Orb"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Burning Blade", "ingredients": ["Burning Sword", "Whetstone", "Whetstone"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Burning Blade", "ingredients": ["<PERSON> Longsword", "Flame", "Flame"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Burning Sword", "ingredients": ["Hero Sword", "Flame", "Flame"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Flame Whip", "ingredients": ["Thorn Whip", "Flame", "Flame"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON> Dagger", "ingredients": ["<PERSON>gger", "Flame", "Flame"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON>pear", "ingredients": ["Spear", "Flame", "Flame"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON> Piggy", "ingredients": ["Piggybank", "<PERSON> Clover", "<PERSON> Clover"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Carrot Goobert", "ingredients": ["Goobert", "Carrot", "Carrot"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Rat Chef", "ingredients": ["Rat", "Healing Herbs"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Rainbow Goobert Megasludge Alphapuddle", "ingredients": ["Goobert", "Blood Goobert", "Light Goobert", "Carrot Goobert", "Steel Goobert"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Squirrel Archer", "ingredients": ["Squirrel", "Shortbow"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Critwood Staff", "ingredients": ["Magic Staff", "Acorn Collar"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Belladonna's Shade", "ingredients": ["Shortbow", "Pestilence Flask"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Belladonna's Whisper", "ingredients": ["Bow and Arrow", "Pestilence Flask"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Fortuna's Grace", "ingredients": ["Bow and Arrow", "<PERSON> Clover", "<PERSON> Clover"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Fortuna's Hope", "ingredients": ["Shortbow", "<PERSON> Clover", "<PERSON> Clover"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Tusk Piercer", "ingredients": ["Bow and Arrow", "<PERSON><PERSON><PERSON>"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Tusk Poker", "ingredients": ["Shortbow", "<PERSON><PERSON><PERSON>"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Ice Dragon", "ingredients": ["<PERSON>", "White-Eyes Blue Dragon"], "catalyst": "", "class_restriction": "<PERSON>, Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Pestilence Flask", "ingredients": ["Pestilence Flask", "Fly Agaric"], "catalyst": "", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Pestilence Flask", "ingredients": ["Pestilence Flask"], "catalyst": "<PERSON><PERSON><PERSON>", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Doom Cap", "ingredients": ["Fly Agaric", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Poison Goobert", "ingredients": ["Goobert", "Fly Agaric", "Fly Agaric"], "catalyst": "", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Rainbow Goobert Omegaooze Primeslime", "ingredients": ["Goobert", "Blood Goobert", "Light Goobert", "Poison Goobert", "Steel Goobert"], "catalyst": "", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Demonic Flask", "ingredients": ["<PERSON><PERSON>"], "catalyst": "<PERSON><PERSON><PERSON>", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Demonic Flask", "ingredients": ["<PERSON><PERSON>", "Corrupted Crystal"], "catalyst": "", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Divine Potion", "ingredients": ["Divine Potion"], "catalyst": "<PERSON><PERSON><PERSON>", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Mana <PERSON>tion", "ingredients": ["<PERSON><PERSON>"], "catalyst": "<PERSON><PERSON><PERSON>", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Vampiric Potion", "ingredients": ["Vampiric Potion"], "catalyst": "<PERSON><PERSON><PERSON>", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON>", "ingredients": ["<PERSON>", "Holo Fire Lizard"], "catalyst": "", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Staff of Unhealing", "ingredients": ["Magic Staff", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}], "metadata": {"total_items": 308, "total_recipes": 128, "crawl_timestamp": "2025-06-15 08:32:38", "item_sources": ["https://backpackbattles.wiki.gg/wiki/Neutral_items", "https://backpackbattles.wiki.gg/wiki/Ranger_items", "https://backpackbattles.wiki.gg/wiki/Reaper_items", "https://backpackbattles.wiki.gg/wiki/Berserker_items", "https://backpackbattles.wiki.gg/wiki/Pyromancer_items"], "recipe_sources": ["https://backpackbattles.wiki.gg/wiki/Recipe"]}}