2025-06-14 23:45:33,830 INFO    MainThread:20552 [wandb_setup.py:_flush():81] Current SDK version is 0.20.1
2025-06-14 23:45:33,830 INFO    MainThread:20552 [wandb_setup.py:_flush():81] Configure stats pid to 20552
2025-06-14 23:45:33,830 INFO    MainThread:20552 [wandb_setup.py:_flush():81] Loading settings from C:\Users\<USER>\.config\wandb\settings
2025-06-14 23:45:33,830 INFO    MainThread:20552 [wandb_setup.py:_flush():81] Loading settings from C:\Users\<USER>\Desktop\Games\BBATTLES\wandb\settings
2025-06-14 23:45:33,830 INFO    MainThread:20552 [wandb_setup.py:_flush():81] Loading settings from environment variables
2025-06-14 23:45:33,830 INFO    MainThread:20552 [wandb_init.py:setup_run_log_directory():703] Logging user logs to C:\Users\<USER>\Desktop\Games\BBATTLES\wandb\offline-run-20250614_234533-ymqiyb90\logs\debug.log
2025-06-14 23:45:33,830 INFO    MainThread:20552 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to C:\Users\<USER>\Desktop\Games\BBATTLES\wandb\offline-run-20250614_234533-ymqiyb90\logs\debug-internal.log
2025-06-14 23:45:33,831 INFO    MainThread:20552 [wandb_init.py:init():831] calling init triggers
2025-06-14 23:45:33,831 INFO    MainThread:20552 [wandb_init.py:init():836] wandb.init called with sweep_config: {}
config: {'algorithm': 'PPO', 'learning_rate': 0.001, 'gamma': 0.99, 'exploration_strategy': 'softmax', '_wandb': {}}
2025-06-14 23:45:33,831 INFO    MainThread:20552 [wandb_init.py:init():872] starting backend
2025-06-14 23:45:34,046 INFO    MainThread:20552 [wandb_init.py:init():875] sending inform_init request
2025-06-14 23:45:34,085 INFO    MainThread:20552 [wandb_init.py:init():883] backend started and connected
2025-06-14 23:45:34,085 INFO    MainThread:20552 [wandb_init.py:init():956] updated telemetry
2025-06-14 23:45:34,087 INFO    MainThread:20552 [wandb_init.py:init():980] communicating run to backend with 90.0 second timeout
2025-06-14 23:45:34,240 INFO    MainThread:20552 [wandb_init.py:init():1032] starting run threads in backend
2025-06-14 23:45:34,745 INFO    MainThread:20552 [wandb_run.py:_console_start():2453] atexit reg
2025-06-14 23:45:34,746 INFO    MainThread:20552 [wandb_run.py:_redirect():2301] redirect: wrap_raw
2025-06-14 23:45:34,746 INFO    MainThread:20552 [wandb_run.py:_redirect():2370] Wrapping output streams.
2025-06-14 23:45:34,746 INFO    MainThread:20552 [wandb_run.py:_redirect():2393] Redirects installed.
2025-06-14 23:45:34,748 INFO    MainThread:20552 [wandb_init.py:init():1078] run started, returning control to user process
2025-06-14 23:45:35,259 INFO    MsgRouterThr:20552 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 0 handles.
