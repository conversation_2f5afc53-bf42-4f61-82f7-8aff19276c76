{"enhanced_items": [{"name": "Amulet of Alchemy", "type": "Accessory", "rarity": "Rare", "cost": "6", "effect": "Start of battle: gain 3 💪 random buffs. 🧪 Potion consumed: 70% chance to repeat its effect After 2.5s ⏰.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Amulet_of_Alchemy", "grid_layout": [["star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "cell", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star"]], "grid_width": 27, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 2.5", "Start of battle"]}, {"name": "Amulet of Darkness", "type": "Accessory", "rarity": "Rare", "cost": "6", "effect": "10 -damage dealt: inflict 1 ☠️ random debuff. item activates: 30% chance to deal 5 ❄️ damage.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Amulet_of_Darkness", "grid_layout": [["star", "star"], ["star", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Amulet of Energy", "type": "Accessory", "rarity": "Rare", "cost": "6", "effect": "Start of battle: The item triggers 100% faster for 1s. Buff used: Refund 25% of the used buffs.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Amulet_of_Energy", "grid_layout": [["star", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 1", "Start of battle"]}, {"name": "Amulet of Feasting", "type": "Accessory", "rarity": "Rare", "cost": "6", "effect": "🍎 Food triggers 40% faster. Food bought: Restock with a random Food.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Amulet_of_Feasting", "grid_layout": [["star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "cell", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star"]], "grid_width": 25, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Amulet of Life", "type": "Accessory", "rarity": "Rare", "cost": "6", "effect": "Start of battle: gain 20 ❤️ maximum health. Your healing is increased by 20%.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Amulet_of_Life", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Amulet of Steel", "type": "Accessory", "rarity": "Rare", "cost": "6", "effect": "Start of battle: <PERSON><PERSON> 25 . items gained 35 : Gain 1 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Amulet_of_Steel", "grid_layout": [["star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "cell", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star"]], "grid_width": 27, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Amulet of the Wild", "type": "Accessory", "rarity": "Rare", "cost": "6", "effect": "After 5s ⏰: Trigger the Pet and gain 4 . Return damage limit of against and attacks +50%.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Amulet_of_the_Wild", "grid_layout": [["star", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 5"]}, {"name": "Artifact Stone: Cold", "type": "Weapon", "rarity": "Unique", "cost": "10", "effect": "Can only be thrown once per battle. On hit: Inflict 3 . Weapon hits: Inflict 1 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Artifact_Stone%3A_Cold", "grid_layout": [["star", "cell", "star"]], "grid_width": 3, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "2-4 (0.8/s)", "stamina": "0 (0/s)", "accuracy": "90%", "cooldown": "4s", "sockets": ""}, "bag_properties": null, "timing_effects": []}, {"name": "Artifact Stone: Death", "type": "Weapon", "rarity": "Unique", "cost": "8", "effect": "Can only be thrown once per battle. On hit: Inflict Fatigue damage. items have +7% critical hit chance per Fatigue level of your opponent.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Artifact_Stone%3A_Death", "grid_layout": [["star", "cell", "star"]], "grid_width": 3, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "7-9 (3.5/s)", "stamina": "0 (0/s)", "accuracy": "90%", "cooldown": "2.3s", "sockets": ""}, "bag_properties": null, "timing_effects": []}, {"name": "Artifact Stone: Heat", "type": "Weapon", "rarity": "Unique", "cost": "9", "effect": "Can only be thrown once per battle. On hit: Gain 3 . 10 💪 reached: Weapons gain 8 ⚔️ damage.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Artifact_Stone%3A_Heat", "grid_layout": [["star", "cell", "star"]], "grid_width": 3, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "4-6 (1.3/s)", "stamina": "0 (0/s)", "accuracy": "90%", "cooldown": "4s", "sockets": ""}, "bag_properties": null, "timing_effects": []}, {"name": "Bag of Stones", "type": "Accessory", "rarity": "Rare", "cost": "3", "effect": "Stones above can be thrown repeatedly.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Bag_of_Stones", "grid_layout": [["star", "star", "star", "cell", "cell", "cell", "cell"]], "grid_width": 7, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Bagtacular", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Fanny Packs give +10% trigger speed. Stamina Sacks give 10% base stamina regeneration. 🧪 Potion Belts give 2 buffs when a 🧪 Potion inside is consumed. Protective Purses give +15 .", "image_url": "https://backpackbattles.wiki.gg/images/8/8a/Bagtacular.png?fcc182", "source_url": "https://backpackbattles.wiki.gg/wiki/Bagtacular", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Banana", "type": "Food", "rarity": "Common", "cost": "3", "effect": "Every 5s ⏰: heal for 4 💚 and regenerate 1 stamina. 🍎 Food: Triggers 10% faster for each Food of a different type (not Banana).", "image_url": "https://backpackbattles.wiki.gg/images/6/69/Banana.png?90a3c2", "source_url": "https://backpackbattles.wiki.gg/wiki/Banana", "grid_layout": [["star", "star", "cell", "star", "star", "cell", "cell", "star", "star", "star"]], "grid_width": 10, "grid_height": 1, "positional_synergies": ["For each synergy: for each Food of a different type (not Banana)", "Type synergy: Food: Triggers 10% faster for each Food of a different type (not Banana)"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 5", "Seconds: 1"]}, {"name": "Blood Amulet", "type": "Accessory", "rarity": "Legendary", "cost": "8", "effect": "Start of battle: gain 2 💪 and 20 maximum health.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Blood_Amulet", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Blood Goobert", "type": "Pet", "rarity": "Legendary", "cost": "15", "effect": "Start of battle: Gain 2 . 6 item activations: deal 10 ❄️ damage with 100% lifesteal. Deal +1 for each .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Blood_Goobert", "grid_layout": [["star", "cell", "cell", "star", "star", "cell", "cell", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": ["For each synergy: for each"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Blood Harvester", "type": "Weapon", "rarity": "Unique", "cost": "7", "effect": "Items give +100% . Attacks 5% faster for every .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Blood_Harvester", "grid_layout": [["cell", "cell", "cell", "cell", "star", "star", "cell", "star", "star", "cell", "star", "star"]], "grid_width": 12, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "10-16 (3.3/s)", "stamina": "0 (0/s)", "accuracy": "90%", "cooldown": "4s", "sockets": "3"}, "bag_properties": null, "timing_effects": []}, {"name": "Blood Manipulation", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Deal 20% of your healing as -damage. Every 3s ⏰: Gain 1 . Triggers 15% faster for each -item.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Blood_Manipulation", "grid_layout": [["star", "star", "star", "star", "star", "star", "star", "star", "cell", "star", "star", "star"]], "grid_width": 12, "grid_height": 1, "positional_synergies": ["For each synergy: for each -item"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "Weapon", "rarity": "<PERSON><PERSON>", "cost": "15", "effect": "On hit: use 1 🔮 to gain 1 💪 and 1 . Deals +1 damage per and .", "image_url": "https://backpackbattles.wiki.gg/images/0/02/Bloodthorne.png?8da1c8", "source_url": "https://backpackbattles.wiki.gg/wiki/Bloodthorne", "grid_layout": [["cell", "cell", "cell"]], "grid_width": 3, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "4-8 (3.8/s)", "stamina": "1.6 (1/s)", "accuracy": "90%", "cooldown": "1.6s", "sockets": "2"}, "bag_properties": null, "timing_effects": []}, {"name": "Bloody Dagger", "type": "Weapon", "rarity": "Legendary", "cost": "12", "effect": "On hit: <PERSON>ain 1 (up to 5 per battle). Heal 4 per -item. On stun: Triggers extra attack.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/<PERSON>_Dagger", "grid_layout": [["star", "star", "cell", "star", "star", "cell", "star", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "4-7 (2.3/s)", "stamina": "0 (0/s)", "accuracy": "95%", "cooldown": "2.4s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "Blueberries", "type": "Food", "rarity": "Rare", "cost": "2", "effect": "Every 3.5s ⏰: Gain 1 . If you have at least 10 : gain 1 💪 instead. 🍎 Food: Triggers 10% faster for each Food of a different type (not Blueberries).", "image_url": "https://backpackbattles.wiki.gg/images/a/af/Blueberries.png?f70ba7", "source_url": "https://backpackbattles.wiki.gg/wiki/Blueberries", "grid_layout": [["star", "star", "cell", "star", "star"]], "grid_width": 5, "grid_height": 1, "positional_synergies": ["For each synergy: for each Food of a different type (not Blueberries)", "Type synergy: Food: Triggers 10% faster for each Food of a different type (not Blueberries)"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3.5"]}, {"name": "Box of Prosperity", "type": "Bag", "rarity": "Epic", "cost": "5", "effect": "Add 4 🎒 backpack slots. Shop entered: If this has at least 2 Godly or Unique items inside 🎒, generate a chipped Gemstone.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Box_of_Prosperity", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": ["Items inside: items inside, generate a chipped Gemstone"], "weapon_stats": null, "bag_properties": {"slots_added": 4, "special_effects": []}, "timing_effects": []}, {"name": "Box of Riches", "type": "Accessory", "rarity": "Rare", "cost": "5", "effect": "Shop entered: Generate a low-quality gemstone. Gemstones are offered in the shop.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Box_of_Riches", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Amethyst", "type": "Gemstone", "rarity": "Varies", "cost": "1", "effect": "Weapon sockets: On hit: 20/30/45/65/100% chance to remove a random buff from your opponent. Armor & other sockets: Reduce opponent's healing by 15/20/25/30/40%. Backpack: Every 4/3/2.5/2/1.2s: cleanse 1 ☠️ debuff.", "image_url": "https://backpackbattles.wiki.gg/images/f/f2/BPB_Amethyst_Line.png?535ff2", "source_url": "https://backpackbattles.wiki.gg/wiki/Amethyst", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 1.2"]}, {"name": "Emerald", "type": "Gemstone", "rarity": "Varies", "cost": "1", "effect": "Weapon sockets: On hit: 35/50/80/80/100% chance to inflict 1/1/1/2/3 . Armor & other sockets: 10/15/20/25/35% chance to resist . Backpack: After 3/4/4/3.5/3s: <PERSON>ain 1/2/3/4/6 .", "image_url": "https://backpackbattles.wiki.gg/images/f/fb/BPB_Emerald_Line.png?9861b7", "source_url": "https://backpackbattles.wiki.gg/wiki/Emerald", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3"]}, {"name": "<PERSON>", "type": "Gemstone", "rarity": "Varies", "cost": "1", "effect": "Weapon sockets: On hit: <PERSON><PERSON> 7/10/15/20/30% lifesteal. Armor & other sockets: Your healing is increased by 10/15/20/25/35%. Backpack: After 5s ⏰: Deal 4/6/10/15/30 -damage with 100% lifesteal.", "image_url": "https://backpackbattles.wiki.gg/images/2/2f/BPB_Ruby_Line.png?b03e69", "source_url": "https://backpackbattles.wiki.gg/wiki/Ruby", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 5"]}, {"name": "Sapphire", "type": "Gemstone", "rarity": "Varies", "cost": "1", "effect": "Weapon sockets: On hit: 15/25/40/60/80% chance to ignore , gain 1 💪 and inflict 1 . Armor & other sockets: 5 💪 gained: Gain 2/3/4/5/7 . Backpack: After 3/4/4/3.5/3s: Inflict 1/2/3/4/6 .", "image_url": "https://backpackbattles.wiki.gg/images/8/86/BPB_Sapphire_Line.png?330f8d", "source_url": "https://backpackbattles.wiki.gg/wiki/Sapphire", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3"]}, {"name": "Topaz", "type": "Gemstone", "rarity": "Varies", "cost": "1", "effect": "Weapon sockets: Attacks 10/15/20/25/35% faster. Armor & other sockets: 10/15/20/30/35% chance to resist stuns. 4/6/8/10/15% chance resist critical hits. Backpack: Base stamina regeneration +8/12/20/30/45%.", "image_url": "https://backpackbattles.wiki.gg/images/4/4e/BPB_Topaz_Line.png?488599", "source_url": "https://backpackbattles.wiki.gg/wiki/Topaz", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Broom", "type": "Weapon", "rarity": "Common", "cost": "4", "effect": "Opponent misses attack: <PERSON><PERSON> +2 damage for the next attack. On hit: 33% chance to inflict 1 .", "image_url": "https://backpackbattles.wiki.gg/images/2/26/Broom.png?972204", "source_url": "https://backpackbattles.wiki.gg/wiki/Broom", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "2-4 (1.8/s)", "stamina": "1.2 (0.7/s)", "accuracy": "95%", "cooldown": "1.7s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "Bunch of Coins", "type": "Accessory", "rarity": "Rare", "cost": "9", "effect": "They don't do anything. But you can sell them for profit!", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Bunch_of_Coins", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Burning Coal", "type": "Gemstone", "rarity": "Rare", "cost": "2", "effect": "Weapon sockets: On hit: 12% chance to deal +6 ⚔️ damage and gain 1 . Armor & other sockets: Start of battle: Gain 15 . Resist 5 . Backpack: After 5s ⏰: Gain 2 , cleanse 3 ☠️ debuffs.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Burning_Coal", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 5", "Start of battle"]}, {"name": "Burning Torch", "type": "Weapon", "rarity": "Epic", "cost": "5", "effect": "Start of battle: <PERSON><PERSON> 2 . On hit: 30% chance to gain 1 damage.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Burning_Torch", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "2-3 (1.8/s)", "stamina": "1 (0.7/s)", "accuracy": "90%", "cooldown": "1.4s", "sockets": "1"}, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Buy the Holy Light", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "-Items have +15% chance to be on sale. Oil Lamps and Djinn Lamps gain . -Items trigger 40% faster.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Buy_the_Holy_Light", "grid_layout": [["star", "star", "star", "star", "star", "star", "star", "star", "cell", "star", "star", "star", "star", "star", "star", "star", "star"]], "grid_width": 17, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Cap of Discomfort", "type": "<PERSON><PERSON><PERSON>", "rarity": "Legendary", "cost": "14", "effect": "Start of battle: Reduce damage taken by 25% for 5s. Opponent gains buff: 15% chance to nullify it. Your opponent's healing is reduced by 30%.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Cap_of_Discomfort", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "1"}, "bag_properties": null, "timing_effects": ["Seconds: 5", "Start of battle"]}, {"name": "Cap of Resilience", "type": "<PERSON><PERSON><PERSON>", "rarity": "Epic", "cost": "7", "effect": "Start of battle: Reduce damage taken by 25% for 3s. 15% chance to resist critical hits. 15% chance to resist stuns.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Cap_of_Resilience", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "1"}, "bag_properties": null, "timing_effects": ["Seconds: 3", "Start of battle"]}, {"name": "Ch<PERSON>l<PERSON>", "type": "Pet", "rarity": "Unique", "cost": "8", "effect": "Every 3.3s ⏰: deal 10 ❄️ damage with 100% lifesteal and trigger a random 🍎 Food. 🍎 Food gains . Triggers 15% faster for each -item.", "image_url": "https://backpackbattles.wiki.gg/images/b/b8/Chtulhu.png?65006a", "source_url": "https://backpackbattles.wiki.gg/wiki/Chtulhu", "grid_layout": [["star", "star", "star", "star", "cell", "cell", "cell", "star", "star", "cell", "cell", "cell", "star", "star", "star", "star"]], "grid_width": 16, "grid_height": 1, "positional_synergies": ["For each synergy: for each -item"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3.3"]}, {"name": "Claws of Attack", "type": "Weapon", "rarity": "Epic", "cost": "8", "effect": "After 4 hits, gain 1 . Attacks 5% faster for every .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Claws_of_Attack", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "4-5 (2.8/s)", "stamina": "0.5 (0.3/s)", "accuracy": "90%", "cooldown": "1.6s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "Corrupted Armor", "type": "Armor", "rarity": "<PERSON><PERSON>", "cost": "20", "effect": "-Items gain . 10% chance for each -item to protect debuffs on your opponent from being cleansed. Start of battle: <PERSON><PERSON> 85 . Every 2.4s ⏰: cleanse 2 ☠️ debuffs and inflict them on your opponent.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Corrupted_Armor", "grid_layout": [["star", "star", "star", "cell", "cell", "star", "star", "cell", "cell", "star", "star", "cell", "cell", "star", "star", "star"]], "grid_width": 16, "grid_height": 1, "positional_synergies": ["For each synergy: for each -item to protect debuffs on your opponent from being cleansed"], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "3"}, "bag_properties": null, "timing_effects": ["Seconds: 2.4", "Start of battle"]}, {"name": "Corrupted Crystal", "type": "Gemstone", "rarity": "Epic", "cost": "7", "effect": "Weapon sockets: Opponent below 30% health: Deal +50% damage. Armor & other sockets: 7 debuffs inflicted: Gain 6 . Backpack: Every 5.5s ⏰: Inflict Fatigue damage.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Corrupted_Crystal", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 5.5"]}, {"name": "Crossblades", "type": "Weapon", "rarity": "<PERSON><PERSON>", "cost": "38", "effect": "Start of battle: The Weapon gains 10 damage. The item triggers 60% faster. On hit: Gain +1 damage and trigger 4% faster.", "image_url": "https://backpackbattles.wiki.gg/images/thumb/8/8c/Crossblades.png/100px-Crossblades.png?62087d", "source_url": "https://backpackbattles.wiki.gg/wiki/Neutral_items", "grid_layout": [], "grid_width": 0, "grid_height": 0, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "<PERSON><PERSON><PERSON>", "type": "Pet", "rarity": "Unique", "cost": "8", "effect": "activates: 35% chance to gain 1 . activates: 30% chance to use 1 🔮 to gain 1 .", "image_url": "https://backpackbattles.wiki.gg/images/6/6a/Cubert.png?6e5743", "source_url": "https://backpackbattles.wiki.gg/wiki/Cubert", "grid_layout": [["diamond", "diamond", "star", "cell", "cell", "star", "star", "cell", "cell", "star", "diamond", "diamond"]], "grid_width": 12, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Customer Card", "type": "Accessory", "rarity": "Rare", "cost": "4", "effect": "Increases the rarity of 1 item in the shop every time it refreshes.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Customer_Card", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "<PERSON>gger", "type": "Weapon", "rarity": "Rare", "cost": "4", "effect": "On stun: <PERSON><PERSON><PERSON> extra attack.", "image_url": "https://backpackbattles.wiki.gg/images/4/45/Dagger.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Dagger", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "2-5 (1/s)", "stamina": "0 (0/s)", "accuracy": "95%", "cooldown": "3.5s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "Dancing Dragon", "type": "Weapon", "rarity": "Unique", "cost": "9", "effect": "You have a 2% chance to resist debuffs for each . Start of battle: gain 2 💪 and 2 for each -item. Deals +0.5 damage per .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Dancing_Dragon", "grid_layout": [["star", "star", "star", "star", "cell", "cell", "cell", "star", "star", "cell", "cell", "cell", "star", "star", "star", "star"]], "grid_width": 16, "grid_height": 1, "positional_synergies": ["For each synergy: for each", "For each synergy: for each -item"], "weapon_stats": {"damage": "15-20 (8/s)", "stamina": "2 (0.9/s)", "accuracy": "85%", "cooldown": "2.2s", "sockets": "2"}, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Darksaber", "type": "Weapon", "rarity": "<PERSON><PERSON>", "cost": "19", "effect": "On attack: use 1 🔮 to inflict 1 . Deals +0.5 damage for each debuff of your opponent.", "image_url": "https://backpackbattles.wiki.gg/images/e/e8/Darksaber.png?e8af7f", "source_url": "https://backpackbattles.wiki.gg/wiki/Darksaber", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": ["For each synergy: for each debuff of your opponent"], "weapon_stats": {"damage": "10-15 (8.3/s)", "stamina": "1 (0.7/s)", "accuracy": "95%", "cooldown": "1.5s", "sockets": "2"}, "bag_properties": null, "timing_effects": []}, {"name": "Divine Potion", "type": "Potion", "rarity": "<PERSON><PERSON>", "cost": "7", "effect": "You reached 10 debuff: Consume this and cleanse 10 ☠️ debuffs.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Divine_Potion", "grid_layout": [["star", "cell", "star", "star", "cell", "cell", "cell", "cell", "cell"]], "grid_width": 9, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "<PERSON><PERSON><PERSON>", "type": "Accessory", "rarity": "<PERSON><PERSON>", "cost": "11", "effect": "Every 1.6s ⏰: gain 1 💪 or 1 or 1 , depending on what you have the least of. Use 7 , 7 , 7 , 7 and 27 health: Give the Weapon +27 damage (once).", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Djinn_<PERSON>p", "grid_layout": [["cell", "cell", "star"]], "grid_width": 3, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 1.6"]}, {"name": "Eggscalibur", "type": "Weapon", "rarity": "Legendary", "cost": "10", "effect": "On attack: Use 11 : <PERSON><PERSON> all Food. Deals +1 damage for each Food.", "image_url": "https://backpackbattles.wiki.gg/images/7/76/Eggscalibur.png?29e5d4", "source_url": "https://backpackbattles.wiki.gg/wiki/Eggscalibur", "grid_layout": [["star", "star", "star", "star", "cell", "cell", "star", "star", "cell", "cell", "star", "star", "star", "star"]], "grid_width": 14, "grid_height": 1, "positional_synergies": ["For each synergy: for each Food"], "weapon_stats": {"damage": "8-9 (5.3/s)", "stamina": "1.3 (0.8/s)", "accuracy": "90%", "cooldown": "1.6s", "sockets": "2"}, "bag_properties": null, "timing_effects": []}, {"name": "Falcon Blade", "type": "Weapon", "rarity": "Legendary", "cost": "19", "effect": "Start of battle: items trigger 30% faster. Attacks twice.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Falcon_Blade", "grid_layout": [["star", "star", "cell", "star", "star", "cell", "star", "star", "cell", "star", "star"]], "grid_width": 11, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "5-6 (3.2/s)", "stamina": "1 (0.6/s)", "accuracy": "100%", "cooldown": "1.7s", "sockets": "2"}, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "<PERSON><PERSON>", "type": "Weapon", "rarity": "<PERSON><PERSON>", "cost": "12", "effect": "On hit: use 3 🔮 to gain 3 damage. On miss: Gain 3 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Fancy_Fencing_Rapier", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "10-16 (9.3/s)", "stamina": "0.8 (0.6/s)", "accuracy": "60%", "cooldown": "1.4s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "Fanfare", "type": "Accessory", "rarity": "<PERSON><PERSON>", "cost": "7", "effect": "Every 3s ⏰: Randomly gain 1 💪 or gain 3 💪 and remove 2 💪 from opponent or remove 1 💪 stamina from opponent. Triggers 10% faster for each item.", "image_url": "https://backpackbattles.wiki.gg/images/7/7f/Fanfare.png?c192ef", "source_url": "https://backpackbattles.wiki.gg/wiki/Fanfare", "grid_layout": [["star", "star", "star", "cell", "cell", "cell", "star", "star", "star", "star", "star", "star"]], "grid_width": 12, "grid_height": 1, "positional_synergies": ["For each synergy: for each item"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3", "Seconds: 1"]}, {"name": "<PERSON>", "type": "Bag", "rarity": "Rare", "cost": "3", "effect": "Add 2 🎒 backpack slots. items inside 🎒 trigger 10% faster.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Fanny_Pack", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": ["Items inside: Items inside trigger 10% faster"], "weapon_stats": null, "bag_properties": {"slots_added": 2, "special_effects": ["Increased trigger speed"]}, "timing_effects": []}, {"name": "Flame Badge", "type": "Accessory", "rarity": "Unique", "cost": "5", "effect": "Pyromancer items are offered in the shop. Shop entered: 65% chance to spend 1 and generate a Flame. Start of battle: Gain 6 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Flame_Badge", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Flute", "type": "Accessory", "rarity": "Epic", "cost": "6", "effect": "Every 4.7s ⏰: Randomly gain 14 💪 or 2 stamina or 2 . Triggers 10% faster for each item.", "image_url": "https://backpackbattles.wiki.gg/images/d/db/Flute.png?2a0b71", "source_url": "https://backpackbattles.wiki.gg/wiki/Flute", "grid_layout": [["star", "star", "star", "cell", "cell", "cell", "star", "star", "star", "star", "star", "star"]], "grid_width": 12, "grid_height": 1, "positional_synergies": ["For each synergy: for each item"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 4.7", "Seconds: 2"]}, {"name": "Frostbite", "type": "Weapon", "rarity": "Legendary", "cost": "11", "effect": "On hit: 60% chance to inflict 1 . Opponent reaches 30 : <PERSON><PERSON> 5 (once). Deals +1 damage per and +0.4 per of your opponent.", "image_url": "https://backpackbattles.wiki.gg/images/e/ee/Frostbite.png?b60bb0", "source_url": "https://backpackbattles.wiki.gg/wiki/Frostbite", "grid_layout": [["cell", "cell", "cell"]], "grid_width": 3, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "4-7 (3.4/s)", "stamina": "1 (0.6/s)", "accuracy": "90%", "cooldown": "1.6s", "sockets": "2"}, "bag_properties": null, "timing_effects": []}, {"name": "<PERSON><PERSON><PERSON>", "type": "Shield", "rarity": "Epic", "cost": "8", "effect": "On attacked (): 30% chance to prevent 7 damage, remove 0.5 stamina from opponent and inflict 1 (up to 10).", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Frozen_<PERSON>ler", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "1"}, "bag_properties": null, "timing_effects": ["Seconds: 0.5"]}, {"name": "<PERSON><PERSON><PERSON>", "type": "Food", "rarity": "Common", "cost": "2", "effect": "Every 4s ⏰: <PERSON>ain 3 . 30% chance to remove 1 💪 from your opponent. 🍎 Food: Triggers 10% faster for each Food of a different type (not Garlic).", "image_url": "https://backpackbattles.wiki.gg/images/c/cc/Garlic.png?1d9604", "source_url": "https://backpackbattles.wiki.gg/wiki/Garlic", "grid_layout": [["star", "star", "cell", "star", "star", "cell", "star", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": ["For each synergy: for each Food of a different type (not Garlic)", "Type synergy: Food: Triggers 10% faster for each Food of a different type (not Garlic)"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 4"]}, {"name": "Gingerbread Jerry", "type": "Food", "rarity": "Unique", "cost": "8", "effect": "Start of battle: gain 40 ❤️ maximum health. Every 3s ⏰: Use 1 , 1 and 1 : Gain 1 , 3 and 20 maximum health. 🍎 Food: Triggers 10% faster for each Food of a different type (not Gingerbread Jerry).", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Gingerbread_Jerry", "grid_layout": [["star", "star", "cell", "star", "star", "cell", "star", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": ["For each synergy: for each Food of a different type (not Gingerbread Jerry)", "Type synergy: Food: Triggers 10% faster for each Food of a different type (not Gingerbread Jerry)"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3", "Start of battle"]}, {"name": "Girl Power", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Every 3.5s ⏰: gain 2 💪 or 2 , depending on which you have less of. Triggers 20% faster for each distinct Class item.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Girl_Power", "grid_layout": [["star", "star", "star", "star", "star", "star", "star", "cell", "star", "star", "star", "star", "star", "star", "star", "star"]], "grid_width": 16, "grid_height": 1, "positional_synergies": ["For each synergy: for each distinct Class item"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3.5"]}, {"name": "Gloves of Haste", "type": "Gloves", "rarity": "Rare", "cost": "4", "effect": "Start of battle: items trigger 20% faster.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Gloves_of_<PERSON><PERSON>", "grid_layout": [["star", "cell"], ["cell", "star"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Gloves of Power", "type": "Gloves", "rarity": "Legendary", "cost": "10", "effect": "Weapons deal +20% damage but attack 10% slower. Weapon hits: gain 7 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Gloves_of_Power", "grid_layout": [["star", "cell"], ["cell", "star"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Glowing Crown", "type": "<PERSON><PERSON><PERSON>", "rarity": "<PERSON><PERSON>", "cost": "12", "effect": "Every 2.4s ⏰: cleanse 1 ☠️ and heal for 5 💚. Use 10 : Become invulnerable for 2s (once).", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Glowing_Crown", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "1"}, "bag_properties": null, "timing_effects": ["Seconds: 2.4", "Seconds: 2"]}, {"name": "Goobert", "type": "Pet", "rarity": "Rare", "cost": "6", "effect": "5 item activations: heal for 9 💚.", "image_url": "https://backpackbattles.wiki.gg/images/c/c3/Goobert.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Goobert", "grid_layout": [["star", "cell", "cell", "star", "star", "cell", "cell", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "<PERSON><PERSON><PERSON>", "type": "Pet", "rarity": "Common", "cost": "2", "effect": "3 item activations: heal for 4 💚.", "image_url": "https://backpackbattles.wiki.gg/images/8/8b/Goobling.png?259c4d", "source_url": "https://backpackbattles.wiki.gg/wiki/Goobling", "grid_layout": [["star", "cell", "star"]], "grid_width": 3, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Hammer", "type": "Weapon", "rarity": "Rare", "cost": "8", "effect": "On hit: 45% chance to stun your opponent for 0.5s.", "image_url": "https://backpackbattles.wiki.gg/images/8/81/Hammer.png?38d36f", "source_url": "https://backpackbattles.wiki.gg/wiki/Hammer", "grid_layout": [["cell", "cell", "cell", "cell", "cell"]], "grid_width": 5, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "7-11 (4.5/s)", "stamina": "3 (1.5/s)", "accuracy": "75%", "cooldown": "2s", "sockets": "2"}, "bag_properties": null, "timing_effects": ["Seconds: 0.5"]}, {"name": "Hardwood", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Common Weapons deal +150% damage. Start of battle: gain 10 💪 for each Common item.", "image_url": "https://backpackbattles.wiki.gg/images/e/ed/Hardwood.png?e80c98", "source_url": "https://backpackbattles.wiki.gg/wiki/Hardwood", "grid_layout": [["star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "cell", "star", "star", "star", "star"]], "grid_width": 17, "grid_height": 1, "positional_synergies": ["For each synergy: for each Common item"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Healing Herbs", "type": "Accessory", "rarity": "Common", "cost": "4", "effect": "Start of battle: Gain 2 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Healing_Herbs", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Health Potion", "type": "Potion", "rarity": "Rare", "cost": "4", "effect": "Health drops below 50%: Consume this and heal for 12 💚 and cleanse 4 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Health_Potion", "grid_layout": [["star", "cell", "star", "star", "cell", "cell", "cell", "cell", "cell"]], "grid_width": 9, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Heart Container", "type": "Accessory", "rarity": "<PERSON><PERSON>", "cost": "12", "effect": "Every 3s ⏰: Gain 1 . Use 7 : gain 100 ❤️ maximum health, 2 and your healing is increased by 15% (once).", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Heart_Container", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "1"}, "bag_properties": null, "timing_effects": ["Seconds: 3"]}, {"name": "Heart of Darkness", "type": "Accessory", "rarity": "<PERSON><PERSON>", "cost": "19", "effect": "Every 4s ⏰: Steal 2 buffs, prioritizing . Triggers 20% faster for each -item. Use 7 : gain 100 ❤️ maximum health, 4 and your opponent's healing is reduced by 40% (once).", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Heart_of_Darkness", "grid_layout": [["star", "star", "star", "cell", "cell", "star", "star", "cell", "cell", "star", "star", "star"]], "grid_width": 12, "grid_height": 1, "positional_synergies": ["For each synergy: for each -item"], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "1"}, "bag_properties": null, "timing_effects": ["Seconds: 4"]}, {"name": "Heavy Drinking", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Every 10s ⏰: Trigger the effect of a random Potion. Triggers 60% faster for each distinct Potion.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Heavy_Drinking", "grid_layout": [["star", "star", "star", "star", "star", "star", "star", "cell", "star", "star", "star", "star", "star", "star", "star"]], "grid_width": 15, "grid_height": 1, "positional_synergies": ["For each synergy: for each distinct Potion"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 10"]}, {"name": "Heroic Potion", "type": "Potion", "rarity": "Legendary", "cost": "6", "effect": "Out of stamina: Consume this and regenerate 2 stamina and gain 1 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Heroic_Potion", "grid_layout": [["star", "cell", "star", "star", "cell", "cell", "cell", "cell", "cell"]], "grid_width": 9, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 2"]}, {"name": "<PERSON> Longsword", "type": "Weapon", "rarity": "Legendary", "cost": "19", "effect": "Start of battle: Weapons gain 4 ⚔️ damage.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Hero_Longsword", "grid_layout": [["star", "star", "cell", "star", "star", "cell", "star", "star", "cell", "star", "star"]], "grid_width": 11, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "10-12 (6.9/s)", "stamina": "1 (0.6/s)", "accuracy": "90%", "cooldown": "1.6s", "sockets": "2"}, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Hero Sword", "type": "Weapon", "rarity": "Epic", "cost": "7", "effect": "Start of battle: Weapons gain 1 ⚔️ damage.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Hero_Sword", "grid_layout": [["star", "star", "cell", "star", "star", "cell", "star", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "2-4 (2.1/s)", "stamina": "0.7 (0.5/s)", "accuracy": "90%", "cooldown": "1.4s", "sockets": "1"}, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Holy Armor", "type": "Armor", "rarity": "Legendary", "cost": "13", "effect": "Start of battle: <PERSON><PERSON> 65 . gain 2 💪 for each -item. Every 2.6s ⏰: Cleanse 2 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Holy_Armor", "grid_layout": [["star", "star", "star", "cell", "cell", "star", "star", "cell", "cell", "star", "star", "cell", "cell", "star", "star", "star"]], "grid_width": 16, "grid_height": 1, "positional_synergies": ["For each synergy: for each -item"], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "3"}, "bag_properties": null, "timing_effects": ["Seconds: 2.6", "Start of battle"]}, {"name": "Holy Spear", "type": "Weapon", "rarity": "<PERSON><PERSON>", "cost": "18", "effect": "On hit: Destroy 10 and cleanse 1 ☠️ debuff for each free slot or -item in front of it. Use 10 : Become invulnerable and attack 100% faster for 3s (once).", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Holy_Spear", "grid_layout": [["star", "star", "star", "star", "star", "cell", "cell", "cell", "cell"]], "grid_width": 9, "grid_height": 1, "positional_synergies": ["For each synergy: for each free slot or -item in front of it"], "weapon_stats": {"damage": "13-18 (6.2/s)", "stamina": "0.4 (0.2/s)", "accuracy": "200%", "cooldown": "2.5s", "sockets": "2"}, "bag_properties": null, "timing_effects": ["Seconds: 3"]}, {"name": "Hungry Blade", "type": "Weapon", "rarity": "Epic", "cost": "7", "effect": "Start of battle: Gain 1 . On hit: use 1 🔮 to gain 1 . Deals +1 maximum damage per .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Hungry_Blade", "grid_layout": [["cell", "cell", "cell"]], "grid_width": 3, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "3-6 (2.8/s)", "stamina": "1 (0.6/s)", "accuracy": "90%", "cooldown": "1.6s", "sockets": "1"}, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Ice Armor", "type": "Armor", "rarity": "Epic", "cost": "11", "effect": "Start of battle: gain 45 💪 and inflict 4 . Every 5s ⏰: use 1 🔮 to inflict 2 ☠️ and gain 10 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Ice_Armor", "grid_layout": [["cell", "cell", "cell"], ["cell", "cell", "cell"]], "grid_width": 3, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "2"}, "bag_properties": null, "timing_effects": ["Seconds: 5", "Start of battle"]}, {"name": "Impractically Large Greatsword", "type": "Weapon", "rarity": "<PERSON><PERSON>", "cost": "14", "effect": "While you have at least 5 , decrease stamina usage to 2 and cooldown to 2s.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Impractically_Large_Greatsword", "grid_layout": [["cell", "cell", "cell", "cell", "cell", "cell", "cell"]], "grid_width": 7, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "40-50 (9/s)", "stamina": "5 (1/s)", "accuracy": "90%", "cooldown": "5s", "sockets": "4"}, "bag_properties": null, "timing_effects": ["Seconds: 2"]}, {"name": "Investment Opportunity", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Shop entered: Gain 1 . item used buff: gain 2 ❤️ maximum health.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Investment_Opportunity", "grid_layout": [["star", "star", "star", "star", "star", "star", "star", "star", "cell", "star", "star", "star", "star", "star"]], "grid_width": 14, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "It's Slime Time!", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Gooblings are offered in the shop. On buy: Generate a Goobling. Every 3s ⏰: Advance all Gooberts and Gooblings by 1 activation.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/It's_Slime_Time!", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3"]}, {"name": "Just Stats", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Start of battle: Gain 15% maximum health and 10% base stamina regeneration. Always offered in round 4.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Just_Stats", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Jynx torquilla", "type": "Pet", "rarity": "Legendary", "cost": "8", "effect": "Every 3s ⏰: items trigger 5% faster (up to 40%). remove 1 💪 from your opponent.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Jynx_torquilla", "grid_layout": [["star", "star", "star", "star", "star", "star", "cell", "star", "star", "star", "star", "cell", "cell", "star", "star", "star", "star", "star", "star"]], "grid_width": 19, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3"]}, {"name": "<PERSON><PERSON>", "type": "Weapon", "rarity": "<PERSON><PERSON>", "cost": "14", "effect": "On hit: remove 1 💪 damage gained in battle from all opponent Weapons and gain 1 damage. If your opponent has at least 20 buffs, remove 2 💪 of the type they have the most of.", "image_url": "https://backpackbattles.wiki.gg/images/e/e9/Katana.png", "source_url": "https://backpackbattles.wiki.gg/wiki/Katana", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "8-10 (5/s)", "stamina": "1.5 (0.8/s)", "accuracy": "90%", "cooldown": "1.8s", "sockets": "3"}, "bag_properties": null, "timing_effects": []}, {"name": "King Crown", "type": "Accessory", "rarity": "<PERSON><PERSON>", "cost": "17", "effect": "Every 2.4s ⏰: heal for 5 💚 and protect 1 buff from removal. Use 10 : Become invulnerable for 2.5s (once). Effects of Gemstones socketed in this are increased by 50%.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/King_Crown", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "2"}, "bag_properties": null, "timing_effects": ["Seconds: 2.4", "Seconds: 2.5"]}, {"name": "King <PERSON><PERSON>", "type": "Pet", "rarity": "<PERSON><PERSON>", "cost": "23", "effect": "6 item activations: heal for 35 💚, protect 3 buffs from removal and use 4 🔮 to become invulnerable for 1.5s (up to 3 times). Effects of Gemstones socketed in this are increased by 50%.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/King_Go<PERSON>rt", "grid_layout": [["star", "cell", "cell", "star", "star", "cell", "cell", "star", "star", "cell", "cell", "star"]], "grid_width": 12, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "2"}, "bag_properties": null, "timing_effects": ["Seconds: 1.5"]}, {"name": "Leaf Badge", "type": "Accessory", "rarity": "Unique", "cost": "5", "effect": "Ranger items are offered in the shop. items gain 2% critical hit chance for each . Every 2.2s ⏰: Gain 1 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Leaf_Badge", "grid_layout": [["star", "star", "cell", "star", "star"]], "grid_width": 5, "grid_height": 1, "positional_synergies": ["For each synergy: for each"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 2.2"]}, {"name": "<PERSON><PERSON> Armor", "type": "Armor", "rarity": "Rare", "cost": "7", "effect": "Start of battle: <PERSON><PERSON> 45 . resist 3 🛡️ debuffs.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Leather_Armor", "grid_layout": [["cell", "cell", "cell"], ["cell", "cell", "cell"]], "grid_width": 3, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "2"}, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "<PERSON><PERSON>", "type": "Bag", "rarity": "Common", "cost": "4", "effect": "Add 4 🎒 backpack slots.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Leather_Bag", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": null, "bag_properties": {"slots_added": 4, "special_effects": []}, "timing_effects": []}, {"name": "<PERSON><PERSON>", "type": "Shoes", "rarity": "Epic", "cost": "6", "effect": "Health drops below 70%: Gain 1 , 1 and 15 (once).", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Leather_Boots", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "Light Goobert", "type": "Pet", "rarity": "<PERSON><PERSON>", "cost": "18", "effect": "6 item activations: heal for 20 💚 and inflict 6 ☠️ for 3s.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Light_Goobert", "grid_layout": [["star", "cell", "cell", "star", "star", "cell", "cell", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3"]}, {"name": "Lightsaber", "type": "Weapon", "rarity": "<PERSON><PERSON>", "cost": "12", "effect": "Use 3 : inflict 8 ☠️ for 6s (unstackable). Deals +1 damage for each of your opponent.", "image_url": "https://backpackbattles.wiki.gg/images/4/49/Lightsaber.png?7a3baf", "source_url": "https://backpackbattles.wiki.gg/wiki/Lightsaber", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": ["For each synergy: for each of your opponent"], "weapon_stats": {"damage": "8-12 (6.7/s)", "stamina": "1 (0.7/s)", "accuracy": "95%", "cooldown": "1.5s", "sockets": "2"}, "bag_properties": null, "timing_effects": ["Seconds: 6"]}, {"name": "Lump of Coal", "type": "Gemstone", "rarity": "Common", "cost": "2", "effect": "Weapon sockets: On attack: 70% chance to deal +1 ⚔️ damage. Armor & other sockets: Start of battle: Gain 8 . resist 1 🛡️ debuff. Backpack: After 3s ⏰: <PERSON>ain a random buff, inflict a random debuff.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Lump_of_Coal", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3", "Start of battle"]}, {"name": "Magic Staff", "type": "Weapon", "rarity": "Epic", "cost": "10", "effect": "On attack: use 3 🔮 to deal +6 ⚔️ damage and gain 2 damage.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Magic_Staff", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "6-8 (3.9/s)", "stamina": "1 (0.6/s)", "accuracy": "90%", "cooldown": "1.8s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "Magic Torch", "type": "Weapon", "rarity": "Legendary", "cost": "11", "effect": "On hit: Use 1 : This and Weapons gain 1 ⚔️ damage.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Magic_Torch", "grid_layout": [["star", "cell"], ["star", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "3-6 (3/s)", "stamina": "0.7 (0.5/s)", "accuracy": "200%", "cooldown": "1.5s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "<PERSON><PERSON>y", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "<PERSON><PERSON> gain +17 random buffs. Every 6s ⏰: <PERSON><PERSON> 3 . Triggers 20% faster for each -item.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/<PERSON>a_Mastery", "grid_layout": [["star", "star", "star", "star", "star", "star", "cell", "star", "star", "star", "star", "star", "star"]], "grid_width": 13, "grid_height": 1, "positional_synergies": ["For each synergy: for each -item"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 6"]}, {"name": "<PERSON><PERSON>", "type": "Accessory", "rarity": "Epic", "cost": "6", "effect": "item activates: 50% chance to gain 1 . Use 35 : gain 17 💪 random other buffs (once).", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/<PERSON><PERSON>_<PERSON>b", "grid_layout": [["star", "star", "cell", "star", "star"]], "grid_width": 5, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "<PERSON><PERSON>", "type": "Potion", "rarity": "Epic", "cost": "6", "effect": "used or health drops below 50%: Consume this and gain 4 💪 and 18 maximum health.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/<PERSON><PERSON>_Potion", "grid_layout": [["star", "cell", "star", "star", "cell", "cell", "cell", "cell", "cell"]], "grid_width": 9, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "Weapon", "rarity": "Legendary", "cost": "13", "effect": "On hit: <PERSON>ain 2 . 30 💪 gained: deal 10 ❄️ damage with 100% lifesteal. Deal +1 for each .", "image_url": "https://backpackbattles.wiki.gg/images/5/53/Manathirst.png?886b1a", "source_url": "https://backpackbattles.wiki.gg/wiki/Manathirst", "grid_layout": [["cell", "cell", "cell"]], "grid_width": 3, "grid_height": 1, "positional_synergies": ["For each synergy: for each"], "weapon_stats": {"damage": "5-7 (4.3/s)", "stamina": "0.5 (0.4/s)", "accuracy": "200%", "cooldown": "1.4s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "Accessory", "rarity": "Legendary", "cost": "10", "effect": "Sale chance +3%. Value of items > 20 15% chance to resist critical hits. Value of items > 40 Godly and Unique items trigger 15% faster.", "image_url": "https://backpackbattles.wiki.gg/images/1/1a/Maneki-neko.png?5c2910", "source_url": "https://backpackbattles.wiki.gg/wiki/Maneki-neko", "grid_layout": [["star", "star", "cell", "star", "star", "cell", "star", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Moon Armor", "type": "Armor", "rarity": "<PERSON><PERSON>", "cost": "19", "effect": "Start of battle: <PERSON><PERSON> 50 + 20 for each -item. Every 2.6s ⏰: gain 3 💪 and reflect 2 debuffs.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Moon_Armor", "grid_layout": [["star", "star", "star", "cell", "cell", "star", "star", "cell", "cell", "star", "star", "cell", "cell", "star", "star", "star"]], "grid_width": 16, "grid_height": 1, "positional_synergies": ["For each synergy: for each -item"], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "3"}, "bag_properties": null, "timing_effects": ["Seconds: 2.6", "Start of battle"]}, {"name": "Moon Shield", "type": "Shield", "rarity": "<PERSON><PERSON>", "cost": "18", "effect": "items give +30% . items gained 12 : Gain 1 . On attacked (/): 30% chance to prevent 12 damage and remove 0.7 stamina from your opponent.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Moon_Shield", "grid_layout": [["star", "star", "star", "cell", "cell", "star", "star", "cell", "cell", "star", "star", "cell", "cell", "star", "star", "star"]], "grid_width": 16, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "2"}, "bag_properties": null, "timing_effects": ["Seconds: 0.7"]}, {"name": "More Stats", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Start of battle: <PERSON><PERSON> 15% maximum health. Your Weapons deal +5% damage. Always offered in round 10.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/More_Stats", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Oil Lamp", "type": "Accessory", "rarity": "Epic", "cost": "7", "effect": "Start of battle: <PERSON><PERSON> 2 . Every 3.4s ⏰: The Weapon gains 1 damage and 5% accuracy.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Oil_Lamp", "grid_layout": [["cell", "star", "cell"]], "grid_width": 3, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3.4", "Start of battle"]}, {"name": "Pan", "type": "Weapon", "rarity": "Common", "cost": "4", "effect": "Deals +1 damage for each Food.", "image_url": "https://backpackbattles.wiki.gg/images/c/ca/Pan.png?a43964", "source_url": "https://backpackbattles.wiki.gg/wiki/Pan", "grid_layout": [["star", "star", "cell", "star", "star", "cell", "cell", "star", "star", "star"]], "grid_width": 10, "grid_height": 1, "positional_synergies": ["For each synergy: for each Food"], "weapon_stats": {"damage": "4-5 (2/s)", "stamina": "2 (0.9/s)", "accuracy": "85%", "cooldown": "2.2s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "Pandamonium", "type": "Weapon", "rarity": "Legendary", "cost": "11", "effect": "Food activates: Inflict 1 .", "image_url": "https://backpackbattles.wiki.gg/images/a/a6/Pandamonium.png?8b4cc3", "source_url": "https://backpackbattles.wiki.gg/wiki/Pandamonium", "grid_layout": [["star", "star", "star", "star", "star", "star", "star", "cell", "cell", "cell", "star"]], "grid_width": 11, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "9-11 (5/s)", "stamina": "0.7 (0.4/s)", "accuracy": "90%", "cooldown": "2s", "sockets": "2"}, "bag_properties": null, "timing_effects": []}, {"name": "Pestilence Flask", "type": "Potion", "rarity": "Epic", "cost": "7", "effect": "Opponent heals: Consume this and inflict 3 ☠️ and 1 to yourself.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Pestilence_Flask", "grid_layout": [["star", "cell", "star", "star", "cell", "cell", "cell", "cell", "cell"]], "grid_width": 9, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Piggybank", "type": "Accessory", "rarity": "Common", "cost": "3", "effect": "Shop entered: Gain 1 . Start of battle: gain 2 ❤️ maximum health for each Start of battle item.", "image_url": "https://backpackbattles.wiki.gg/images/3/33/Piggybank.png?48998a", "source_url": "https://backpackbattles.wiki.gg/wiki/Piggybank", "grid_layout": [["star", "star", "cell"], ["cell", "star", "star"]], "grid_width": 3, "grid_height": 2, "positional_synergies": ["For each synergy: for each Start of battle item"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "<PERSON><PERSON>", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Destroying a Piggybank generates items instead. Shop entered: Piggybanks have a 30% chance to explode. Items gain 5% critical hit chance.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/<PERSON><PERSON>_<PERSON>a", "grid_layout": [["star", "cell"], ["star", "star"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Pineapple", "type": "Food", "rarity": "Legendary", "cost": "7", "effect": "Every 3.3s ⏰: gain 1 💪 and heal for 4 💚. 🍎 Food: Triggers 10% faster for each Food of a different type (not Pineapple).", "image_url": "https://backpackbattles.wiki.gg/images/f/fb/Pineapple.png?8bf553", "source_url": "https://backpackbattles.wiki.gg/wiki/Pineapple", "grid_layout": [["star", "star", "cell", "star", "star", "cell", "star", "star", "cell", "star", "star"]], "grid_width": 11, "grid_height": 1, "positional_synergies": ["For each synergy: for each Food of a different type (not Pineapple)", "Type synergy: Food: Triggers 10% faster for each Food of a different type (not Pineapple)"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3.3"]}, {"name": "Platinum Customer Card", "type": "Accessory", "rarity": "Epic", "cost": "8", "effect": "Start of battle: Reflect 2 debuffs for each Legendary, Godly or Unique item. 20% chance to protect your buffs from removal. Chance to find -items +25%. You can obtain +1 -item.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Platinum_Customer_Card", "grid_layout": [["star", "star", "cell", "star", "star"]], "grid_width": 5, "grid_height": 1, "positional_synergies": ["For each synergy: for each Legendary, Godly or Unique item"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Pocket Sand", "type": "Accessory", "rarity": "Common", "cost": "2", "effect": "Start of battle: Inflict 1 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Pocket_Sand", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "<PERSON><PERSON>", "type": "Weapon", "rarity": "Epic", "cost": "11", "effect": "On hit: Inflict 2 . On stun: <PERSON>ggers extra attack.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/<PERSON><PERSON>_<PERSON>gger", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "3-6 (1.7/s)", "stamina": "0 (0/s)", "accuracy": "95%", "cooldown": "2.7s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "Pop", "type": "Weapon, Pet", "rarity": "Unique", "cost": "6", "effect": "Attacks 3% faster for each (up to 60%).", "image_url": "https://backpackbattles.wiki.gg/images/7/71/Pop.png?d5088e", "source_url": "https://backpackbattles.wiki.gg/wiki/Pop", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": ["For each synergy: for each (up to 60%)"], "weapon_stats": {"damage": "1-2 (1/s)", "stamina": "0 (0/s)", "accuracy": "90%", "cooldown": "1.5s", "sockets": "4"}, "bag_properties": null, "timing_effects": []}, {"name": "Potion Belt", "type": "Bag", "rarity": "Legendary", "cost": "5", "effect": "Add 4 🎒 backpack slots. First 🧪 Potion inside consumed: Gain a random buff. 4 Potions inside consumed: cleanse 8 ☠️ debuffs.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Potion_Belt", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": null, "bag_properties": {"slots_added": 4, "special_effects": []}, "timing_effects": []}, {"name": "Power of the Moon", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Fatigue starts 4s earlier. Moon Armor activates: Inflict 1 . Moon Shield activates: Reflect 1 debuff. Fatigue starts: heal for 50 💚% of your maximum health.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Power_of_the_Moon", "grid_layout": [["star", "star", "star", "star", "star", "star", "cell", "star", "star", "star", "star", "star", "star", "star", "star", "star"]], "grid_width": 16, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 4"]}, {"name": "Present", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Shop entered: Instead of gold, you receive items with a higher value. Start of battle: gain 5 💪 random buffs.", "image_url": "https://backpackbattles.wiki.gg/images/0/02/Present.png?5862f4", "source_url": "https://backpackbattles.wiki.gg/wiki/Present", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Prismatic Orb", "type": "Accessory", "rarity": "<PERSON><PERSON>", "cost": "13", "effect": "Start of battle: For each... -item: <PERSON>ain 2 . -item: Gain 1 . -item: Increase your healing by 4%. -item: Inflict a random debuff. Every 8s ⏰: gain 1 💪 of every type of buff.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Prismatic_Orb", "grid_layout": [["star", "star", "star", "star", "cell", "star", "star", "star", "star"]], "grid_width": 9, "grid_height": 1, "positional_synergies": ["For each synergy: For each"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 8", "Start of battle"]}, {"name": "Prismatic Sword", "type": "Weapon", "rarity": "<PERSON><PERSON>", "cost": "15", "effect": "For each... -item: +8% attack speed. -item: +15% lifesteal. -item: Gain +0.3 damage on hit. -item: +12% chance to inflict 4 ☠️ random debuffs on hit.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Prismatic_Sword", "grid_layout": [["star", "star", "cell", "star", "star", "cell", "star", "star", "cell", "star", "star"]], "grid_width": 11, "grid_height": 1, "positional_synergies": ["For each synergy: For each"], "weapon_stats": {"damage": "8-14 (7.9/s)", "stamina": "1 (0.7/s)", "accuracy": "90%", "cooldown": "1.4s", "sockets": "2"}, "bag_properties": null, "timing_effects": []}, {"name": "Protective Purse", "type": "Bag", "rarity": "<PERSON><PERSON>", "cost": "2", "effect": "Add 1 🎒 backpack slots. Start of battle: G<PERSON> 15 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Protective_Purse", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": {"slots_added": 1, "special_effects": []}, "timing_effects": ["Start of battle"]}, {"name": "<PERSON><PERSON><PERSON>", "type": "Weapon, Food", "rarity": "Unique", "cost": "8", "effect": "On hit: 50% chance to stun for 0.5s. Fatigue starts: gain 10 . 🍎 Food: Triggers 10% faster for each Food of a different type (not Pumpkin).", "image_url": "https://backpackbattles.wiki.gg/images/6/64/Pumpkin.png?3f68d8", "source_url": "https://backpackbattles.wiki.gg/wiki/P<PERSON>kin", "grid_layout": [["star", "star", "star", "cell", "cell", "star", "star", "cell", "cell", "star", "star", "star"]], "grid_width": 12, "grid_height": 1, "positional_synergies": ["For each synergy: for each Food of a different type (not Pumpkin)", "Type synergy: Food: Triggers 10% faster for each Food of a different type (not Pumpkin)"], "weapon_stats": {"damage": "6-12 (1.8/s)", "stamina": "0 (0/s)", "accuracy": "90%", "cooldown": "5s", "sockets": "2"}, "bag_properties": null, "timing_effects": ["Seconds: 0.5"]}, {"name": "<PERSON> Badge", "type": "Accessory", "rarity": "Unique", "cost": "5", "effect": "Items of all classes are offered in the shop. After 7s ⏰: gain 1 💪 of every type of buff.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Rainbow_Badge", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 7"]}, {"name": "Ripsaw Blade", "type": "Weapon", "rarity": "Legendary", "cost": "10", "effect": "On hit: remove 1 💪 damage gained in battle from all opponent Weapons and gain 0.5 damage.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Ripsaw_Blade", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "8-10 (5/s)", "stamina": "1.5 (0.8/s)", "accuracy": "90%", "cooldown": "1.8s", "sockets": "3"}, "bag_properties": null, "timing_effects": []}, {"name": "<PERSON>", "type": "Pet", "rarity": "Legendary", "cost": "10", "effect": "Start of battle: <PERSON><PERSON> 4 . <PERSON><PERSON>ct 3 debuffs. Hatches after 2 rounds in your backpack.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Ruby_Egg", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle", "Hatching mechanic"]}, {"name": "<PERSON>", "type": "Weapon, Pet", "rarity": "<PERSON><PERSON>", "cost": "10", "effect": "Start of battle: <PERSON><PERSON> 4 . <PERSON><PERSON><PERSON> 3 debuffs.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Ruby_Whelp", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "5-10 (3.6/s)", "stamina": "0 (0/s)", "accuracy": "90%", "cooldown": "2.1s", "sockets": "1"}, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Sack of Surprises", "type": "Bag", "rarity": "Unique", "cost": "10", "effect": "Game started: Replace this with random starting bags and items.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Sack_of_Surprises", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": null, "bag_properties": {"slots_added": 0, "special_effects": []}, "timing_effects": []}, {"name": "Serpent Staff", "type": "Weapon", "rarity": "Legendary", "cost": "17", "effect": "On attack: use 4 🔮 to gain 2 damage and inflict 1 ☠️ for each 4 damage dealt. You have 30% chance to duplicate you inflict.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Serpent_Staff", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": ["For each synergy: for each 4 damage dealt"], "weapon_stats": {"damage": "8-10 (5/s)", "stamina": "1 (0.6/s)", "accuracy": "90%", "cooldown": "1.8s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "Shell Totem", "type": "Weapon", "rarity": "Rare", "cost": "5", "effect": "Every 3.4s ⏰: If your health is above 70%, gain 1 . Otherwise, heal for 8 💚. Uses -15% stamina for each -item.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Shell_Totem", "grid_layout": [["star", "star", "star", "cell", "star", "star", "star", "cell"]], "grid_width": 8, "grid_height": 1, "positional_synergies": ["For each synergy: for each -item"], "weapon_stats": {"damage": "", "stamina": "2 (0.6/s)", "accuracy": "", "cooldown": "3.4s", "sockets": ""}, "bag_properties": null, "timing_effects": ["Seconds: 3.4"]}, {"name": "<PERSON><PERSON>", "type": "Accessory", "rarity": "Rare", "cost": "8", "effect": "Start of battle: Weapons gain 2 ⚔️ damage. 25% chance to protect your buffs from removal. 25% chance to resist and .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Shepherd's_Crook", "grid_layout": [["cell", "cell", "cell", "star", "cell", "star", "cell", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Shielded", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Shields have +25% chance to block. Armors trigger 50% faster.", "image_url": "https://backpackbattles.wiki.gg/images/c/cb/Shielded.png?910ad1", "source_url": "https://backpackbattles.wiki.gg/wiki/Shielded", "grid_layout": [["star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "cell", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star"]], "grid_width": 24, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Shield of Valor", "type": "Shield", "rarity": "Legendary", "cost": "12", "effect": "Items give 30% more . On attacked (): 30% chance to prevent 12 damage and remove 0.7 stamina from opponent.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Shield_of_Valor", "grid_layout": [["star", "star", "star", "cell", "cell", "star", "star", "cell", "cell", "star", "star", "cell", "cell", "star", "star", "star"]], "grid_width": 16, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "2"}, "bag_properties": null, "timing_effects": ["Seconds: 0.7"]}, {"name": "Shiny Shell", "type": "Accessory", "rarity": "Common", "cost": "2", "effect": "After 5s ⏰: heal for 5 💚 + 3 for each -item.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Shiny_Shell", "grid_layout": [["star", "cell", "star"]], "grid_width": 3, "grid_height": 1, "positional_synergies": ["For each synergy: for each -item"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 5"]}, {"name": "<PERSON><PERSON><PERSON>", "type": "Weapon", "rarity": "Rare", "cost": "8", "effect": "Shop entered: Dig up a random item. On hit: 40% chance to inflict 1 .", "image_url": "https://backpackbattles.wiki.gg/images/d/d5/Shovel_item.png?b58fc9", "source_url": "https://backpackbattles.wiki.gg/wiki/Shovel", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "5-8 (2.7/s)", "stamina": "1.7 (0.7/s)", "accuracy": "95%", "cooldown": "2.4s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "Skull Badge", "type": "Accessory", "rarity": "Unique", "cost": "5", "effect": "Reaper items are offered in the shop. Every 1.5s ⏰: Inflict a random debuff.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Skull_Badge", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 1.5"]}, {"name": "<PERSON><PERSON><PERSON>", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "<PERSON><PERSON><PERSON> gains +5 . 20 💪 gained: Inflict 1 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Smelly_Wall", "grid_layout": [["star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star", "cell"]], "grid_width": 12, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Smithing For Dummies", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "On buy: Generate a Whetstone. Crafted Weapons deal +1 ⚔️ damage and use -25% stamina.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Smithing_For_Dummies", "grid_layout": [["star", "star", "star", "star", "cell", "star", "star", "star", "star"]], "grid_width": 9, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Snowball", "type": "Accessory", "rarity": "Epic", "cost": "4", "effect": "Start of battle: Inflict 2 . Your opponent gains 15% less maximum health from items.", "image_url": "https://backpackbattles.wiki.gg/images/0/04/Snowball.png?1473bb", "source_url": "https://backpackbattles.wiki.gg/wiki/Snowball", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Snowcake", "type": "Food", "rarity": "Unique", "cost": "6", "effect": "Every 3s ⏰: Inflict 1 . If your opponent has at least 10 , increase -damage by 10% and deal 10 ❄️ damage. 🍎 Food: Triggers 10% faster for each Food of a different type (not Snowcake).", "image_url": "https://backpackbattles.wiki.gg/images/f/f8/Snowcake.png?e44d58", "source_url": "https://backpackbattles.wiki.gg/wiki/Snowcake", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": ["For each synergy: for each Food of a different type (not Snowcake)", "Type synergy: Food: Triggers 10% faster for each Food of a different type (not Snowcake)"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3"]}, {"name": "Snow Stick", "type": "Weapon", "rarity": "Legendary", "cost": "8", "effect": "On hit: inflict 3 ☠️ and 2 to yourself.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Snow_Stick", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "5-8 (3.8/s)", "stamina": "1.2 (0.7/s)", "accuracy": "95%", "cooldown": "1.7s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "Spear", "type": "Weapon", "rarity": "Rare", "cost": "6", "effect": "On hit: Des<PERSON>y 4 for each free slot in front of this.", "image_url": "https://backpackbattles.wiki.gg/images/4/4f/Spear.png?887bc8", "source_url": "https://backpackbattles.wiki.gg/wiki/Spear", "grid_layout": [["star", "star", "star", "star", "star", "cell", "cell", "cell", "cell"]], "grid_width": 9, "grid_height": 1, "positional_synergies": ["For each synergy: for each free slot in front of this"], "weapon_stats": {"damage": "3-8 (3.7/s)", "stamina": "1 (0.7/s)", "accuracy": "85%", "cooldown": "1.5s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "<PERSON><PERSON><PERSON> Dagger", "type": "Weapon", "rarity": "Legendary", "cost": "10", "effect": "On attack: use 1 🔮 to ignore and deal +7 ⚔️ damage. On stun: Triggers extra attack.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Spectral_Dagger", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "3-6 (2.1/s)", "stamina": "0 (0/s)", "accuracy": "95%", "cooldown": "2.1s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "Spicy <PERSON>", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Banana activates: 30% chance to gain 1 . 1 stamina used: heal for 3 💚.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Spicy_Banana", "grid_layout": [["star", "star", "star", "star", "star", "star", "star", "cell", "star", "star"]], "grid_width": 10, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 1"]}, {"name": "Spiked Shield", "type": "Shield", "rarity": "Rare", "cost": "8", "effect": "On attacked (): 30% chance to prevent 7 damage, remove 0.3 stamina from opponent, and gain 1 (up to 4).", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Spiked_Shield", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "1"}, "bag_properties": null, "timing_effects": ["Seconds: 0.3"]}, {"name": "Stable Recombobulator", "type": "Accessory", "rarity": "Unique", "cost": "6", "effect": "Shop entered: Consume items. Create different items based on the combined value. Every 2.5s ⏰: gain 1 💪 random buff and cleanse 1 ☠️ debuff.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Stable_Recombobulator", "grid_layout": [["star", "star", "star", "star", "star", "star", "star", "star", "cell", "star", "star", "star", "star", "star", "star", "star", "star"]], "grid_width": 17, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 2.5"]}, {"name": "<PERSON><PERSON><PERSON>", "type": "Bag", "rarity": "Epic", "cost": "5", "effect": "Add 3 🎒 backpack slots. Gain 1 maximum stamina.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Stamina_Sack", "grid_layout": [["cell", "cell", "cell"]], "grid_width": 3, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": {"slots_added": 3, "special_effects": ["Stamina bonus"]}, "timing_effects": []}, {"name": "Steel Goobert", "type": "Pet", "rarity": "Legendary", "cost": "17", "effect": "5 item activations: Weapons gain +2 damage. Gain 16 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Steel_Goobert", "grid_layout": [["diamond", "diamond", "star", "cell", "cell", "star", "star", "cell", "cell", "star", "diamond", "diamond"]], "grid_width": 12, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Stone", "type": "Weapon", "rarity": "Common", "cost": "1", "effect": "Can only be thrown once per battle. On hit: Destroy 4 .", "image_url": "https://backpackbattles.wiki.gg/images/d/d4/Stone.png?13e0c3", "source_url": "https://backpackbattles.wiki.gg/wiki/Stone", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "2-4 (1.2/s)", "stamina": "0 (0/s)", "accuracy": "70%", "cooldown": "2.5s", "sockets": ""}, "bag_properties": null, "timing_effects": []}, {"name": "Stone Armor", "type": "Armor", "rarity": "Legendary", "cost": "13", "effect": "Items use +20% stamina. Start of battle: <PERSON><PERSON> 90 . Every 4s ⏰: remove 1 💪 and 1 from opponent. Health drops below 50%: Gain equal to 40% of your missing health (once).", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Stone_Armor", "grid_layout": [["cell", "cell", "cell"], ["cell", "cell", "cell"]], "grid_width": 3, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "2"}, "bag_properties": null, "timing_effects": ["Seconds: 4", "Start of battle"]}, {"name": "Stone Badge", "type": "Accessory", "rarity": "Unique", "cost": "5", "effect": "Your starting class items are no longer offered in the shop (even when this item is in storage). Shop entered: Generate items worth 1 . Every 3s ⏰: Gain 4 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Stone_Badge", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3"]}, {"name": "Stoned", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Stone or Stone Golem dealt damage: <PERSON>ain 45% of the damage as . While you have : 40% chance to resist critical hits.", "image_url": "https://backpackbattles.wiki.gg/images/a/ab/Stoned.png?22d016", "source_url": "https://backpackbattles.wiki.gg/wiki/Stoned", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Stone Golem", "type": "Weapon, Pet", "rarity": "<PERSON><PERSON>", "cost": "16", "effect": "On hit: <PERSON><PERSON> 1 . 30% chance to stun for 0.5s. Use 7 : Reduce cooldown to 2.6s and gain 150 (once). Deals +10 damage for each Bag of Stones.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Stone_Golem", "grid_layout": [["star", "star", "cell", "cell", "star", "cell", "cell", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": ["For each synergy: for each Bag of Stones"], "weapon_stats": {"damage": "8-13 (1.9/s)", "stamina": "0 (0/s)", "accuracy": "85%", "cooldown": "5.5s", "sockets": "1"}, "bag_properties": null, "timing_effects": ["Seconds: 0.5", "Seconds: 2.6"]}, {"name": "<PERSON>", "type": "<PERSON><PERSON><PERSON>", "rarity": "Legendary", "cost": "13", "effect": "Start of battle: Reduce damage taken by 25% for 5s and gain 35 . 25% chance to resist critical hits. 30% chance to resist stuns.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/<PERSON>_<PERSON>lm", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "1"}, "bag_properties": null, "timing_effects": ["Seconds: 5", "Start of battle"]}, {"name": "Stone Shoes", "type": "Shoes", "rarity": "Legendary", "cost": "12", "effect": "Health drops below 70%: Gain 1 , 1 , and 30 . Reduce /-damage taken by 30% for 5s (once).", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Stone_Shoes", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "1"}, "bag_properties": null, "timing_effects": ["Seconds: 5"]}, {"name": "Stone Skin Potion", "type": "Potion", "rarity": "Epic", "cost": "6", "effect": "45 💪 reached: Consume this and convert 15 health to 30 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Stone_Skin_Potion", "grid_layout": [["star", "cell", "star", "star", "cell", "cell", "cell", "cell", "cell"]], "grid_width": 9, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Strong Health Potion", "type": "Potion", "rarity": "Epic", "cost": "8", "effect": "Health drops below 50%: Consume this and heal for 24 💚, gain 3 💪 and cleanse 4 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Strong_Health_Potion", "grid_layout": [["star", "cell", "star", "star", "cell", "cell", "cell", "cell", "cell"]], "grid_width": 9, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Strong Heroic Potion", "type": "Potion", "rarity": "Legendary", "cost": "9", "effect": "Out of stamina: Consume this and regenerate 4 stamina and gain 1 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Strong_Heroic_Potion", "grid_layout": [["star", "cell", "star", "star", "cell", "cell", "cell", "cell", "cell"]], "grid_width": 9, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 4"]}, {"name": "Strong Stone Skin Potion", "type": "Potion", "rarity": "Legendary", "cost": "9", "effect": "45 💪 reached: Consume this and convert 15 health to 35 and gain 2 💪 for 4s.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Strong_Stone_Skin_Potion", "grid_layout": [["star", "cell", "star", "star", "cell", "cell", "cell", "cell", "cell"]], "grid_width": 9, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 4"]}, {"name": "Superspacious", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Bags appear more often in the shop and have +30% chance to be on sale. item trigger 9% faster for each free slot.", "image_url": "https://backpackbattles.wiki.gg/images/e/e6/Superspacious.png?be537c", "source_url": "https://backpackbattles.wiki.gg/wiki/Superspacious", "grid_layout": [["star", "diamond", "star", "diamond", "cell", "diamond", "star", "diamond", "star"]], "grid_width": 9, "grid_height": 1, "positional_synergies": ["For each synergy: for each free slot"], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Thornbloom", "type": "Weapon", "rarity": "<PERSON><PERSON>", "cost": "14", "effect": "On hit: <PERSON><PERSON> 1 . 60% chance to gain 1 . gained: gain 10 ❤️ maximum health. Deals +1 damage per .", "image_url": "https://backpackbattles.wiki.gg/images/6/6f/Thornbloom.png?24edc0", "source_url": "https://backpackbattles.wiki.gg/wiki/Thornbloom", "grid_layout": [["cell", "cell", "cell"], ["cell", "cell", "cell"]], "grid_width": 3, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "7-12 (4.5/s)", "stamina": "2.1 (1/s)", "accuracy": "80%", "cooldown": "2.1s", "sockets": "2"}, "bag_properties": null, "timing_effects": []}, {"name": "Thornburst", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Every 10s ⏰: Stun for 0.5s and gain 3 (up to 3 times). Triggers 5% faster for each .", "image_url": "https://backpackbattles.wiki.gg/images/7/7e/Thornburst.png?1d3d6c", "source_url": "https://backpackbattles.wiki.gg/wiki/Thornburst", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": ["For each synergy: for each"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 10", "Seconds: 0.5"]}, {"name": "Thorn Whip", "type": "Weapon", "rarity": "Epic", "cost": "8", "effect": "On hit: <PERSON><PERSON> 1 . Deals +1 damage per .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Thorn_Whip", "grid_layout": [["cell", "cell", "cell", "cell", "cell"]], "grid_width": 5, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "4-9 (3/s)", "stamina": "2.2 (1/s)", "accuracy": "80%", "cooldown": "2.2s", "sockets": "2"}, "bag_properties": null, "timing_effects": []}, {"name": "<PERSON>", "type": "Gemstone", "rarity": "Unique", "cost": "10", "effect": "Weapon sockets: On hit: 50% chance to steal a random buff. Armor & other sockets: 25% chance to resist debuffs or critical hits. Backpack: Opponent drops below 30%: heal for 50 💚 and gain 5 .", "image_url": "https://backpackbattles.wiki.gg/images/e/ea/Tim.png?127103", "source_url": "https://backpackbattles.wiki.gg/wiki/Tim", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Time Dilator", "type": "Accessory", "rarity": "Unique", "cost": "8", "effect": "Your and your opponent's Weapons attack 30% slower. Every 1s ⏰: Your item with the highest cooldown triggers 6% faster.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Time_Dilator", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 1"]}, {"name": "<PERSON>ch", "type": "Weapon", "rarity": "Rare", "cost": "5", "effect": "On hit: 25% chance to gain 1 damage.", "image_url": "https://backpackbattles.wiki.gg/images/b/b2/Torch.png?cbccb7", "source_url": "https://backpackbattles.wiki.gg/wiki/Torch", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "2-3 (1.8/s)", "stamina": "1 (0.7/s)", "accuracy": "90%", "cooldown": "1.4s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "Unidentified Amulet", "type": "Accessory", "rarity": "Rare", "cost": "6", "effect": "On buy: Gain a random effect.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Unidentified_Amulet", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Uniquely Unique", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "The item triggers 20% faster +10% for each Unique item, Customer Card or Platinum Customer Card. Chance to find -items +50%. You can obtain +2 -items.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Uniquely_Unique", "grid_layout": [["diamond", "diamond", "diamond", "diamond", "diamond", "diamond", "star", "diamond", "diamond", "cell", "diamond", "diamond", "diamond", "diamond", "diamond", "diamond", "diamond", "diamond"]], "grid_width": 18, "grid_height": 1, "positional_synergies": ["For each synergy: for each Unique item, Customer Card or Platinum Customer Card"], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Unsettling Presence", "type": "Pet", "rarity": "Unique", "cost": "10", "effect": "Deal +30% of your healing as -damage. Every 3s ⏰: Use a random buff to heal for 12 💚.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Unsettling_Presence", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3"]}, {"name": "Unstable Recombobulator", "type": "Accessory", "rarity": "Epic", "cost": "6", "effect": "Shop entered: Consume this and items. Create different items based on the combined value. Every 4s ⏰: gain 1 💪 random buff and cleanse 1 ☠️ debuff.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Unstable_Recombobulator", "grid_layout": [["star", "star", "star", "star", "cell", "star", "star", "star", "star"]], "grid_width": 9, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 4"]}, {"name": "Vampiric Armor", "type": "Armor", "rarity": "Legendary", "cost": "16", "effect": "Start of battle: Convert 30 health into 65 and gain 2 . Every 2.8s ⏰: Convert 10 health into 20 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Vampiric_Armor", "grid_layout": [["cell", "cell", "cell"], ["cell", "cell", "cell"]], "grid_width": 3, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "2"}, "bag_properties": null, "timing_effects": ["Seconds: 2.8", "Start of battle"]}, {"name": "Vampiri<PERSON>s", "type": "Gloves", "rarity": "<PERSON><PERSON>", "cost": "12", "effect": "After 4s ⏰: <PERSON><PERSON> 5 , items trigger 35% faster.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Vampiric_Gloves", "grid_layout": [["star", "cell"], ["cell", "star"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 4"]}, {"name": "Vampiric Potion", "type": "Potion", "rarity": "Legendary", "cost": "8", "effect": "Both characters drop below 80% health: Consume this and gain 3 💪 and deal 15 ❄️ damage with 100% lifesteal.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Vampiric_Potion", "grid_layout": [["star", "cell", "star", "star", "cell", "cell", "cell", "cell", "cell"]], "grid_width": 9, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "<PERSON>in <PERSON>", "type": "Weapon", "rarity": "Unique", "cost": "7", "effect": "-Weapons deal -2 damage. Deals +4 damage per -Weapon.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/<PERSON>in_<PERSON>", "grid_layout": [["star", "star", "star", "star", "star", "star", "cell", "star", "star", "star", "star", "cell", "star", "star", "star", "star", "star", "star"]], "grid_width": 18, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "2-4 (2.3/s)", "stamina": "0.3 (0.2/s)", "accuracy": "90%", "cooldown": "1.3s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "<PERSON><PERSON><PERSON>", "type": "Accessory", "rarity": "Common", "cost": "4", "effect": "Start of battle: Gain 1 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Walrus_Tusk", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Whetstone", "type": "Accessory", "rarity": "Common", "cost": "4", "effect": "Start of battle: Weapons gain 1 ⚔️ damage.", "image_url": "https://backpackbattles.wiki.gg/images/9/9f/Whetstone.png?e5238e", "source_url": "https://backpackbattles.wiki.gg/wiki/Whetstone", "grid_layout": [["star", "cell", "star"]], "grid_width": 3, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Winged Boots", "type": "Shoes", "rarity": "<PERSON><PERSON>", "cost": "13", "effect": "Health drops below 70%: Gain 1 , cleanse 15 ☠️ debuffs and dodge the next 3 /-attacks (once).", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Winged_Boots", "grid_layout": [["cell", "cell", "cell"]], "grid_width": 3, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "<PERSON> Badge", "type": "Accessory", "rarity": "Unique", "cost": "5", "effect": "Berserker items are offered in the shop. Health drops below 50%: Enter Battle Rage for 5s (once). During Battle Rage: items trigger 25% faster. You take 20% reduced damage.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/<PERSON>_<PERSON>", "grid_layout": [["star", "star", "star", "star", "cell", "star", "star", "star", "star"]], "grid_width": 9, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 5"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "Pet", "rarity": "<PERSON><PERSON>", "cost": "12", "effect": "Increase base stamina regeneration by 0.7% for each buff you have. Every 5s ⏰: gain 3 💪 of the buff you have least of. Triggers 15% faster for each Pet.", "image_url": "https://backpackbattles.wiki.gg/images/c/c3/Wolpertinger.png?c48373", "source_url": "https://backpackbattles.wiki.gg/wiki/W<PERSON><PERSON><PERSON>", "grid_layout": [["star", "star", "star", "cell", "cell", "star", "star", "cell", "cell", "star", "star", "star"]], "grid_width": 12, "grid_height": 1, "positional_synergies": ["For each synergy: for each buff you have", "For each synergy: for each Pet"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 5"]}, {"name": "Wonky Snowman", "type": "Accessory", "rarity": "Epic", "cost": "8", "effect": "On Buy: Split into 2 Snowballs.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Wonky_Snowman", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 2"]}, {"name": "<PERSON><PERSON>", "type": "Shield", "rarity": "Common", "cost": "4", "effect": "On attacked (): 30% chance to prevent 7 damage and remove 0.3 stamina from opponent.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/<PERSON><PERSON>_<PERSON>ler", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "1"}, "bag_properties": null, "timing_effects": ["Seconds: 0.3"]}, {"name": "Wooden Sword", "type": "Weapon", "rarity": "Common", "cost": "3", "effect": "", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/<PERSON>en_Sword", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "1-3 (1.4/s)", "stamina": "1 (0.7/s)", "accuracy": "90%", "cooldown": "1.4s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "Acorn Ace", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Acorn Collars have more slots. Items affected by Acorn Collar use -8% stamina. Critwood Staffs use -75% stamina.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Acorn_Ace", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Acorn Collar", "type": "Accessory", "rarity": "Epic", "cost": "6", "effect": "items gain 5% critical hit chance for each .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Acorn_Collar", "grid_layout": [["star", "star", "star", "star", "cell", "star", "star", "star", "star"]], "grid_width": 9, "grid_height": 1, "positional_synergies": ["For each synergy: for each"], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Belladonna's Shade", "type": "Weapon", "rarity": "Legendary", "cost": "11", "effect": "On hit: 70% chance to inflict 2 ☠️ and a random debuff.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Belladonna's_Shade", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "4-11 (4.4/s)", "stamina": "0.7 (0.4/s)", "accuracy": "85%", "cooldown": "1.7s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "Belladonna's Whisper", "type": "Weapon", "rarity": "Legendary", "cost": "14", "effect": "For every 5 damage Weapon deals: Inflict +1 on the next attack. Deals +0.5 damage per of your opponent.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Belladonna's_Whisper", "grid_layout": [["cell", "cell", "cell"], ["cell", "star", "cell"]], "grid_width": 3, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "9-12 (3.5/s)", "stamina": "1.2 (0.4/s)", "accuracy": "85%", "cooldown": "3s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "Big Bowl of Treats", "type": "Food", "rarity": "Unique", "cost": "10", "effect": "Every 3.7s ⏰: gain 2 💪 random buffs and make 🍎 Food trigger 25% faster (up to 100%). All your Pets have a 20% chance to activate twice. Friends of the forest are offered in the shop. 🍎 Food: Triggers 10% faster for each Food of a different type (not Big Bowl of Treats).", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Big_Bowl_of_Treats", "grid_layout": [["star", "star", "star", "star", "star", "cell", "cell", "star", "star", "star"]], "grid_width": 10, "grid_height": 1, "positional_synergies": ["For each synergy: for each Food of a different type (not Big Bowl of Treats)", "Type synergy: Food: Triggers 10% faster for each Food of a different type (not Big Bowl of Treats)"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3.7"]}, {"name": "Bow and Arrow", "type": "Weapon", "rarity": "Epic", "cost": "7", "effect": "Weapon hits: <PERSON><PERSON> gain +1 damage (up to 7).", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Bow_and_Arrow", "grid_layout": [["cell", "cell", "cell"], ["cell", "star", "cell"]], "grid_width": 3, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "6-9 (2.5/s)", "stamina": "1.2 (0.4/s)", "accuracy": "85%", "cooldown": "3s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "Carrot", "type": "Food", "rarity": "Rare", "cost": "3", "effect": "Every 3.2s ⏰: cleanse 1 ☠️ debuff. If you have at least 4 : 55% chance to gain 1 . 🍎 Food: Triggers 10% faster for each Food of a different type (not Carrot).", "image_url": "https://backpackbattles.wiki.gg/images/c/c3/Carrot.png?39787a", "source_url": "https://backpackbattles.wiki.gg/wiki/Carrot", "grid_layout": [["star", "star", "star", "cell", "cell", "star", "star", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": ["For each synergy: for each Food of a different type (not Carrot)", "Type synergy: Food: Triggers 10% faster for each Food of a different type (not Carrot)"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3.2"]}, {"name": "Carrot Goobert", "type": "Pet", "rarity": "Epic", "cost": "12", "effect": "6 item activations: cleanse 4 ☠️ random debuffs and gain 2 💪 for 6s.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Carrot_Goobert", "grid_layout": [["star", "cell", "cell", "star", "star", "cell", "cell", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 6"]}, {"name": "Critwood Staff", "type": "Weapon", "rarity": "Legendary", "cost": "16", "effect": "On attack: use 3 🔮 to deal +7 ⚔️ damage and for the next 1.2s, all your attacks are critical.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/<PERSON>ritwood_Staff", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "6-8 (3.9/s)", "stamina": "1 (0.6/s)", "accuracy": "90%", "cooldown": "1.8s", "sockets": "1"}, "bag_properties": null, "timing_effects": ["Seconds: 1.2"]}, {"name": "Fortuna's Grace", "type": "Weapon", "rarity": "Legendary", "cost": "11", "effect": "Start of battle: G<PERSON> 3 . Weapon crits: Attack twice on the next attack.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Fortuna's_Grace", "grid_layout": [["cell", "cell", "cell"], ["cell", "star", "cell"]], "grid_width": 3, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "6-9 (2.5/s)", "stamina": "1.2 (0.4/s)", "accuracy": "85%", "cooldown": "3s", "sockets": "1"}, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Fortuna's Hope", "type": "Weapon", "rarity": "Epic", "cost": "8", "effect": "On hit: 70% chance to gain 1 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Fortuna's_Hope", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "2-3 (1.5/s)", "stamina": "0.7 (0.4/s)", "accuracy": "100%", "cooldown": "1.7s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "Hedgehog", "type": "Pet", "rarity": "Epic", "cost": "7", "effect": "Every 5s ⏰: deal 10 ❄️ damage + 0.5 for each . Health drops below 70%: gain 2 💪 and 15 (once). Triggers 15% faster for each Pet or Food.", "image_url": "https://backpackbattles.wiki.gg/images/c/c4/Hedgehog.png?533403", "source_url": "https://backpackbattles.wiki.gg/wiki/Hedgehog", "grid_layout": [["cell", "star", "cell", "star", "star"]], "grid_width": 5, "grid_height": 1, "positional_synergies": ["For each synergy: for each", "For each synergy: for each Pet or Food"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 5"]}, {"name": "<PERSON> Clover", "type": "Accessory", "rarity": "Rare", "cost": "2", "effect": "Start of battle: Gain 1 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Lucky_Clover", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "<PERSON> Piggy", "type": "Accessory", "rarity": "Epic", "cost": "7", "effect": "Shop entered: Gain 1 . Start of battle: Gain 2 . Chance-based effects of the items are 15% more likely to trigger.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/<PERSON>_Piggy", "grid_layout": [["star", "cell"], ["cell", "star"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Markswoman", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "-Weapons deal +20% damage, attack +30% faster and have +15% accuracy.", "image_url": "https://backpackbattles.wiki.gg/images/e/ea/Markswoman.png?922064", "source_url": "https://backpackbattles.wiki.gg/wiki/Markswoman", "grid_layout": [["cell", "star", "star", "star", "star", "star", "star", "star", "star", "star", "star"]], "grid_width": 11, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Mega Clover", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Sale chance +5% Chance to find -items +20%. Shop entered: Generate two Lucky Clovers 15 💪 reached: gain 25 💪 random other buffs.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Mega_Clover", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Piercing Arrow", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Weapons deal +50% critical damage. They remove 15 💪 on crit. Item activates: 65% chance to gain 1 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Piercing_Arrow", "grid_layout": [["star", "diamond", "diamond", "cell", "cell", "star", "star", "diamond"]], "grid_width": 8, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Poison Ivy", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "You have a 5% chance to resist debuffs for each -item. gained: Inflict 2 . Opponent reaches 18 : They take +25% damage.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Poison_Ivy", "grid_layout": [["star", "star", "star", "star", "cell", "star", "star", "star", "star"]], "grid_width": 9, "grid_height": 1, "positional_synergies": ["For each synergy: for each -item"], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Rainbow Goobert Megasludge Alphapuddle", "type": "Pet", "rarity": "<PERSON><PERSON>", "cost": "68", "effect": "6 item activations: heal for 20 💚, gain 20 , 2 and 2 , inflict 4 , and Weapons gain 4 ⚔️ damage.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Rainbow_Goobert_Megasludge_Alphapuddle", "grid_layout": [["star", "star", "star", "star", "star", "cell", "cell", "star", "star", "cell", "cell", "star", "star", "star", "star", "star"]], "grid_width": 16, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "<PERSON>", "type": "Bag", "rarity": "Unique", "cost": "16", "effect": "Add 6 🎒 backpack slots. items inside 🎒 gain 10% critical hit chance +3% for each .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Ranger_Bag", "grid_layout": [["cell", "cell", "cell"], ["cell", "cell", "cell"]], "grid_width": 3, "grid_height": 2, "positional_synergies": ["For each synergy: for each", "Items inside: Items inside gain 10% critical hit chance +3% for each"], "weapon_stats": null, "bag_properties": {"slots_added": 6, "special_effects": []}, "timing_effects": []}, {"name": "Rat", "type": "Pet", "rarity": "Common", "cost": "4", "effect": "Every 3.3s ⏰: deal 5 ❄️ damage. 75% to inflict 1 . 10% to inflict 1 . Triggers 15% faster for each Pet or Food.", "image_url": "https://backpackbattles.wiki.gg/images/8/8b/Rat.png?eb7de9", "source_url": "https://backpackbattles.wiki.gg/wiki/Rat", "grid_layout": [["star", "star", "star", "cell", "cell", "star", "star", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": ["For each synergy: for each Pet or Food"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3.3"]}, {"name": "Rat Chef", "type": "Pet", "rarity": "Rare", "cost": "8", "effect": "Start of battle: gain 1 💪 for each 🍎 Food. Every 7s ⏰: Regenerate 2 stamina and gain 1 . Triggers 15% faster for each Pet or Food.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Rat_Chef", "grid_layout": [["star", "star", "cell", "star", "star", "cell", "star", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": ["For each synergy: for each Food", "For each synergy: for each Pet or Food"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 7", "Seconds: 2", "Start of battle"]}, {"name": "Shortbow", "type": "Weapon", "rarity": "Common", "cost": "4", "effect": "", "image_url": "https://backpackbattles.wiki.gg/images/f/f9/Shortbow.png?b72e3e", "source_url": "https://backpackbattles.wiki.gg/wiki/Shortbow", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "2-3 (1.5/s)", "stamina": "0.7 (0.4/s)", "accuracy": "85%", "cooldown": "1.7s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "Squirrel", "type": "Pet", "rarity": "Rare", "cost": "5", "effect": "Every 4s ⏰: Steal a random buff. Triggers 15% faster for each Pet or Food.", "image_url": "https://backpackbattles.wiki.gg/images/4/41/Squirrel.png?6782f8", "source_url": "https://backpackbattles.wiki.gg/wiki/Squirrel", "grid_layout": [["star", "star", "cell", "star", "star", "cell", "cell", "star", "star", "star"]], "grid_width": 10, "grid_height": 1, "positional_synergies": ["For each synergy: for each Pet or Food"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 4"]}, {"name": "Squirrel Archer", "type": "Weapon, Pet", "rarity": "Epic", "cost": "9", "effect": "On hit: Steal a random buff. Triggers 15% faster for each Pet or Food.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Squirrel_<PERSON>", "grid_layout": [["star", "star", "cell", "star", "star", "cell", "cell", "star", "star", "star"]], "grid_width": 10, "grid_height": 1, "positional_synergies": ["For each synergy: for each Pet or Food"], "weapon_stats": {"damage": "2-3 (0.8/s)", "stamina": "0 (0/s)", "accuracy": "85%", "cooldown": "3.2s", "sockets": ""}, "bag_properties": null, "timing_effects": []}, {"name": "Tusk Piercer", "type": "Weapon", "rarity": "Legendary", "cost": "11", "effect": "Start of battle: <PERSON><PERSON> 4 . Weapon hits: use 1 🔮 to deal +9 ⚔️ damage on the next attack.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Tusk_Piercer", "grid_layout": [["cell", "cell", "cell"], ["cell", "star", "cell"]], "grid_width": 3, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "9-12 (3.5/s)", "stamina": "1.2 (0.4/s)", "accuracy": "85%", "cooldown": "3s", "sockets": "1"}, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Tusk Poker", "type": "Weapon", "rarity": "Rare", "cost": "8", "effect": "On hit: 50% chance to gain 1 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Tusk_Poker", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "2-3 (1.5/s)", "stamina": "0.7 (0.4/s)", "accuracy": "85%", "cooldown": "1.7s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "Vineweave <PERSON>", "type": "Bag", "rarity": "Unique", "cost": "20", "effect": "Add 9 🎒 backpack slots. Your healing is amplified by 10% + 3% per -items inside 🎒. In rounds 1 and 10, sale chance is increased by 20%.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Vineweave_Basket", "grid_layout": [["cell", "cell", "cell", "cell", "cell", "cell", "cell", "cell", "cell"]], "grid_width": 9, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": {"slots_added": 9, "special_effects": ["Healing bonus"]}, "timing_effects": []}, {"name": "Yggdrasil Leaf", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Start of battle: gain 2 💪 and 1 for each -item. 5 used: heal for 17 💚 and cleanse 2 ☠️ debuffs.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Yggdrasil_Leaf", "grid_layout": [["star", "star", "star", "star", "star", "star", "cell", "star", "star"]], "grid_width": 9, "grid_height": 1, "positional_synergies": ["For each synergy: for each -item"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Ace of Spades", "type": "Playing Card", "rarity": "Rare", "cost": "3", "effect": "On reveal: Your next hit is critical. If the number of cards before is odd, gain 2 💪 and 3 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Ace_of_Spades", "grid_layout": [["star", "cell", "cell"]], "grid_width": 3, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "<PERSON><PERSON><PERSON>", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Shop entered: Upgrade an adjacent Potion. Every 3.3s ⏰: heal for 20 💚 or gain 6 💪 or gain 5 . Triggers 15% faster for each Food or Potion.", "image_url": "https://backpackbattles.wiki.gg/images/3/37/Cauldron.png?17ad56", "source_url": "https://backpackbattles.wiki.gg/wiki/Cauldron", "grid_layout": [["star", "star", "star", "cell", "cell", "star", "star", "cell", "cell", "star"]], "grid_width": 10, "grid_height": 1, "positional_synergies": ["For each synergy: for each Food or Potion", "Adjacency: adjacent Potion"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3.3"]}, {"name": "Cursed <PERSON>", "type": "Weapon", "rarity": "Unique", "cost": "10", "effect": "On stun: Triggers extra attack. On hit: inflict 2 ☠️ random debuffs. This and items have +1% accuracy and +1% critical hit chance per debuff of your opponent.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Cursed_Dagger", "grid_layout": [["star", "star", "cell"], ["star", "cell", "star"]], "grid_width": 3, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "4-7 (2.8/s)", "stamina": "0 (0/s)", "accuracy": "95%", "cooldown": "2s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "Darkest Lotus", "type": "Playing Card", "rarity": "<PERSON><PERSON>", "cost": "7", "effect": "On reveal: For each card before, gain 3 💪 and remove 3 💪 random buffs from your opponent.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Darkest_Lotus", "grid_layout": [["star", "cell", "cell"]], "grid_width": 3, "grid_height": 1, "positional_synergies": ["For each synergy: For each card before, gain 3 and remove 3 random buffs from your opponent"], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Dark Ritual", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "After 17s ⏰: inflict 20 ☠️ debuffs, gain 10 . Triggers 20% faster for each -item.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Dark_Ritual", "grid_layout": [["star", "star", "star", "star", "star", "star", "star", "star", "star", "cell", "star", "star", "star", "star", "star", "star"]], "grid_width": 16, "grid_height": 1, "positional_synergies": ["For each synergy: for each -item"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 17"]}, {"name": "<PERSON> Scythe", "type": "Weapon", "rarity": "Legendary", "cost": "12", "effect": "Items inflict +100% . Opponent reaches 35 : Gain 50% critical hit chance.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Death_Scythe", "grid_layout": [["cell", "cell", "cell", "cell", "star", "star", "cell", "star", "star", "cell", "star", "star"]], "grid_width": 12, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "6-10 (5.7/s)", "stamina": "1.2 (0.9/s)", "accuracy": "90%", "cooldown": "1.4s", "sockets": "3"}, "bag_properties": null, "timing_effects": []}, {"name": "Deck of Cards", "type": "Accessory", "rarity": "Rare", "cost": "3", "effect": "Playing cards are offered in the shop. Start of battle: Gain 2 . Start revealing the Playing card.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Deck_of_Cards", "grid_layout": [["star", "cell", "cell"]], "grid_width": 3, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "<PERSON><PERSON>", "type": "Potion", "rarity": "<PERSON><PERSON>", "cost": "7", "effect": "Opponent drops below 50%: Consume this and deal 0.45 -damage for every debuff of your opponent.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Demonic_Flask", "grid_layout": [["star", "cell", "star", "star", "cell", "cell", "cell", "cell", "cell"]], "grid_width": 9, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Doom Cap", "type": "Food", "rarity": "<PERSON><PERSON>", "cost": "10", "effect": "Every 3s ⏰: inflict 3 ☠️ and reduce opponent's healing by 10%. 🍎 Food: Triggers 10% faster for each Food of a different type (not Doom Cap).", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Doom_Cap", "grid_layout": [["star", "star", "star", "star", "cell", "cell", "cell", "star", "star", "cell", "star", "star"]], "grid_width": 12, "grid_height": 1, "positional_synergies": ["For each synergy: for each Food of a different type (not Doom Cap)", "Type synergy: Food: Triggers 10% faster for each Food of a different type (not Doom Cap)"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3"]}, {"name": "Fly Agaric", "type": "Food", "rarity": "Rare", "cost": "3", "effect": "Every 4.3s ⏰: Inflict 1 . 🍎 Food: Triggers 10% faster for each Food of a different type (not Fly Agaric).", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Fly_Agaric", "grid_layout": [["star", "star", "cell", "star", "star", "cell", "star", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": ["For each synergy: for each Food of a different type (not Fly Agaric)", "Type synergy: Food: Triggers 10% faster for each Food of a different type (not Fly Agaric)"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 4.3"]}, {"name": "Heart of the Cards", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Requires: Deck of Cards. Card revealed: Gain 3 . If the card is at position 3 or higher in the chain, also gain 4 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Heart_of_the_Cards", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Holo Fire Lizard", "type": "Playing Card", "rarity": "Legendary", "cost": "5", "effect": "On reveal: Increase -damage by 10%. deal 12 ❄️ damage + 4 for each card before. Gain 4 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Holo_Fire_Lizard", "grid_layout": [["star", "cell", "cell"]], "grid_width": 3, "grid_height": 1, "positional_synergies": ["For each synergy: for each card before"], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Ice Dragon", "type": "Weapon, Pet", "rarity": "<PERSON><PERSON>", "cost": "15", "effect": "On hit: Inflict 1 . Opponent reaches 12 : <PERSON><PERSON> 50 . You take -20% -damage.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Ice_Dragon", "grid_layout": [["cell", "cell", "cell"], ["cell", "cell", "cell"]], "grid_width": 3, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "15-20 (8.3/s)", "stamina": "0 (0/s)", "accuracy": "90%", "cooldown": "2.1s", "sockets": "2"}, "bag_properties": null, "timing_effects": []}, {"name": "Jimbo", "type": "Playing Card", "rarity": "<PERSON><PERSON>", "cost": "5", "effect": "On reveal: gain 6 💪 random buffs. For each pair before: resist 1 🛡️ critical hit. For each three of a kind before: Your Weapons use -25% stamina. For each four of a kind before: Activate 2 random revealed cards (except <PERSON><PERSON>).", "image_url": "https://backpackbattles.wiki.gg/images/1/1d/Jimbo.png?61e390", "source_url": "https://backpackbattles.wiki.gg/wiki/Jimbo", "grid_layout": [["star", "cell", "cell"]], "grid_width": 3, "grid_height": 1, "positional_synergies": ["For each synergy: For each pair before: Resist 1 critical hit", "For each synergy: For each three of a kind before: Your Weapons use -25% stamina", "For each synergy: For each four of a kind before: Activate 2 random revealed cards (except <PERSON><PERSON>)"], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Miss Fortune", "type": "Accessory", "rarity": "Epic", "cost": "7", "effect": "Every 2.1s ⏰: use 1 🔮 to gain 3 💪 buffs of the type of which you have the most.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Miss_Fortune", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 2.1"]}, {"name": "Mrs. <PERSON>", "type": "Accessory", "rarity": "Legendary", "cost": "7", "effect": "Every 3.5s ⏰: remove 1 💪 buff of each type from your opponent. Triggers 10% faster for each -item.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Mrs._<PERSON>", "grid_layout": [["star", "star", "star", "cell", "cell", "star", "star", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": ["For each synergy: for each -item"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3.5"]}, {"name": "Mr. <PERSON>", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Every 3s ⏰: Inflict Fatigue damage. On debuffed: 25% chance to inflict the same debuff. Health drops below 50%: Items trigger 140% faster for 8s (once). Plushies are offered in the shop.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Mr._<PERSON>", "grid_layout": [["star", "cell"], ["star", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3", "Seconds: 8"]}, {"name": "Mushroom Farm", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Shop entered: If you have at least 2 Mushrooms, generate a Fly Agaric. Start of battle: Mushrooms trigger 50% faster.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Mushroom_Farm", "grid_layout": [["star", "star", "star", "star", "star", "star", "star", "star", "star", "cell", "star", "star", "star", "star"]], "grid_width": 14, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Nocturnal Lock Lifter", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Start of battle: Gain 4 . Weapons steal 25% life + 8% per -item. Your healing is increased by 25%.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Nocturnal_Lock_Lifter", "grid_layout": [["diamond", "diamond", "diamond", "diamond", "diamond", "cell", "diamond", "star", "star", "star", "star", "star"]], "grid_width": 12, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Poison Goobert", "type": "Pet", "rarity": "Epic", "cost": "12", "effect": "5 item activations: cleanse 2 ☠️ and inflict 4 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Poison_Goobert", "grid_layout": [["star", "cell", "cell", "star", "star", "cell", "cell", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Rainbow Goobert Omegaooze Primeslime", "type": "Pet", "rarity": "<PERSON><PERSON>", "cost": "68", "effect": "6 item activations: heal for 20 💚, gain 20 💪 and 2 , inflict 4 ☠️ and 4 , and Weapons gain 4 ⚔️ damage.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Rainbow_Goobert_Omegaooze_Primeslime", "grid_layout": [["star", "star", "star", "star", "star", "cell", "cell", "star", "star", "cell", "cell", "star", "star", "star", "star", "star"]], "grid_width": 16, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Relic Case", "type": "Bag", "rarity": "Unique", "cost": "12", "effect": "Add 4 🎒 backpack slots. Every 3.2s ⏰: Weapons inside deal +5% damage and use -5% stamina.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Relic_Case", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": null, "bag_properties": {"slots_added": 4, "special_effects": ["Stamina bonus", "Combat bonus"]}, "timing_effects": ["Seconds: 3.2"]}, {"name": "Reverse!", "type": "Playing Card", "rarity": "Rare", "cost": "3", "effect": "On reveal: Reflect 4 debuffs. If there are no duplicate cards before, steal 3 buffs.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Reverse!", "grid_layout": [["star", "cell", "cell"]], "grid_width": 3, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "<PERSON>", "type": "Weapon, Pet", "rarity": "<PERSON><PERSON>", "cost": "15", "effect": "On hit: <PERSON><PERSON> 1 . When you have at least 12 : 30% chance to stun your opponent for 0.4s.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/<PERSON>_<PERSON>nk", "grid_layout": [["cell", "cell", "cell"], ["cell", "cell", "cell"]], "grid_width": 3, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "17-22 (9.3/s)", "stamina": "0 (0/s)", "accuracy": "90%", "cooldown": "2.1s", "sockets": "2"}, "bag_properties": null, "timing_effects": ["Seconds: 0.4"]}, {"name": "Snake", "type": "Pet", "rarity": "Unique", "cost": "10", "effect": "4% chance for each to protect on your opponent from being cleased. Start of battle: gain 4 💪 and 50 maximum health for each Pet. Every 2.3s ⏰: Inflict 2 .", "image_url": "https://backpackbattles.wiki.gg/images/5/5a/Snake.png?8e8b4d", "source_url": "https://backpackbattles.wiki.gg/wiki/Snake", "grid_layout": [["star", "star", "cell", "star", "star", "cell", "cell", "star", "cell"]], "grid_width": 9, "grid_height": 1, "positional_synergies": ["For each synergy: for each to protect on your opponent from being cleased", "For each synergy: for each Pet"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 2.3", "Start of battle"]}, {"name": "Staff of Unhealing", "type": "Weapon", "rarity": "<PERSON><PERSON>", "cost": "19", "effect": "Every 2s ⏰: heal for 20 💚. Use 5 : For 2s, deal 100% of your healing as -damage.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Staff_of_Unhealing", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "", "stamina": "1.5 (0.8/s)", "accuracy": "", "cooldown": "2s", "sockets": ""}, "bag_properties": null, "timing_effects": ["Seconds: 2", "Seconds: 2"]}, {"name": "Storage Coffin", "type": "Bag", "rarity": "Unique", "cost": "20", "effect": "Add 8 🎒 backpack slots. items inside 🎒 activates: 25% chance to inflict 1 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Storage_Coffin", "grid_layout": [["cell", "cell", "cell", "cell", "cell", "cell", "cell", "cell"]], "grid_width": 8, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": {"slots_added": 8, "special_effects": []}, "timing_effects": []}, {"name": "Strong Demonic Flask", "type": "Potion", "rarity": "<PERSON><PERSON>", "cost": "14", "effect": "Opponent drops below 50% or you drop below 25%: Consume this and deal 0.75 -damage for each debuff of your opponent with 100% lifesteal. For 3s, reduce opponent's healing by 30%.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Strong_Demonic_Flask", "grid_layout": [["star", "cell", "star", "star", "cell", "cell", "cell", "cell", "cell"]], "grid_width": 9, "grid_height": 1, "positional_synergies": ["For each synergy: for each debuff of your opponent with 100% lifesteal"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3"]}, {"name": "Strong Divine Potion", "type": "Potion", "rarity": "<PERSON><PERSON>", "cost": "15", "effect": "You reached 10 debuffs: Consume this and cleanse 10 ☠️ debuffs and gain 8 💪 random buffs.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Strong_Divine_Potion", "grid_layout": [["star", "cell", "star", "star", "cell", "cell", "cell", "cell", "cell"]], "grid_width": 9, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Strong Mana <PERSON>tion", "type": "Potion", "rarity": "Legendary", "cost": "7", "effect": "used or health drops below 50%: Consume this and gain 9 💪 and 25 maximum health.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/<PERSON>_<PERSON>a_Potion", "grid_layout": [["star", "cell", "star", "star", "cell", "cell", "cell", "cell", "cell"]], "grid_width": 9, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Strong Pestilence Flask", "type": "Potion", "rarity": "Legendary", "cost": "10", "effect": "Opponent regenerates health: Consume this and inflict 3 ☠️ and 1 to yourself. After 6s ⏰, inflict another 3 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Strong_Pestilence_Flask", "grid_layout": [["star", "cell", "star", "star", "cell", "cell", "cell", "cell", "cell"]], "grid_width": 9, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 6"]}, {"name": "Strong Vampiric Potion", "type": "Potion", "rarity": "<PERSON><PERSON>", "cost": "12", "effect": "Both characters drop below 80% health: Consume this and gain 5 💪 and 35% lifesteal for 6s.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Strong_Vampiric_Potion", "grid_layout": [["star", "cell", "star", "star", "cell", "cell", "cell", "cell", "cell"]], "grid_width": 9, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 6"]}, {"name": "The Fool", "type": "Playing Card", "rarity": "Epic", "cost": "4", "effect": "On reveal: Card are revealed 50% faster. If this is the first card in the chain: Gain 1 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/The_Fool", "grid_layout": [["star", "cell", "cell"]], "grid_width": 3, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "The Lovers", "type": "Playing Card", "rarity": "Common", "cost": "3", "effect": "On reveal: deal 10 ❄️ damage with 100% lifesteal. If the number of cards before is even, gain 2 💪 and your healing is increased by 10%.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/The_Lovers", "grid_layout": [["star", "cell", "cell"]], "grid_width": 3, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Toad", "type": "Pet", "rarity": "Epic", "cost": "6", "effect": "items gained 10 buffs: heal for 12 💚. items used 10 buffs: gain 1 💪 and 1 . Every 3.8s ⏰: gain 1 💪 and 1 .", "image_url": "https://backpackbattles.wiki.gg/images/c/c9/Toad.png?6854d1", "source_url": "https://backpackbattles.wiki.gg/wiki/Toad", "grid_layout": [["star", "star", "cell", "star", "star"]], "grid_width": 5, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3.8"]}, {"name": "White-Eyes Blue Dragon", "type": "Playing Card", "rarity": "Epic", "cost": "4", "effect": "On reveal: You take -10% -damage. <PERSON><PERSON> 12 + 6 for each card before. Inflict 4 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/White-Eyes_Blue_Dragon", "grid_layout": [["star", "cell", "cell"]], "grid_width": 3, "grid_height": 1, "positional_synergies": ["For each synergy: for each card before"], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "An<PERSON>", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Item crafted: Generate a Flame. For each crafted item, the Weapons deal +1 ⚔️ damage and use -5% stamina.", "image_url": "https://backpackbattles.wiki.gg/images/d/dd/Anvil.png?65521c", "source_url": "https://backpackbattles.wiki.gg/wiki/Anvil", "grid_layout": [["star", "diamond", "diamond", "star", "star", "cell", "cell", "star", "star", "star", "star", "star"]], "grid_width": 12, "grid_height": 1, "positional_synergies": ["For each synergy: For each crafted item, the Weapons deal +1 damage and use -5% stamina"], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Armored Courage Puppy", "type": "Weapon, Pet", "rarity": "<PERSON><PERSON>", "cost": "7", "effect": "Cannot be blocked by shields or trigger . Deals +2 damage for each Pet.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Armored_Courage_Puppy", "grid_layout": [["star", "star", "star", "cell", "cell", "star", "star", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": ["For each synergy: for each Pet"], "weapon_stats": {"damage": "7-9 (2.3/s)", "stamina": "0 (0/s)", "accuracy": "95%", "cooldown": "3.5s", "sockets": ""}, "bag_properties": null, "timing_effects": []}, {"name": "Armored Power Puppy", "type": "Pet", "rarity": "<PERSON><PERSON>", "cost": "7", "effect": "Every 2.8s ⏰: Randomly gain 1 💪 or 1 or 1 . Triggers 10% faster for each 🐾 Pet. Triggers 20% faster for each Food.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Armored_Power_Puppy", "grid_layout": [["star", "star", "star", "cell", "cell", "star", "star", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": ["For each synergy: for each Pet", "For each synergy: for each Food"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 2.8"]}, {"name": "Armored Wisdom Puppy", "type": "Pet", "rarity": "<PERSON><PERSON>", "cost": "7", "effect": "Every 4s ⏰: gain 14 💪 and cleanse 1 . Increase gain by 1. Triggers 15% faster for each Pet.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Armored_Wisdom_Puppy", "grid_layout": [["star", "star", "cell", "star", "star", "cell", "star", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": ["For each synergy: for each Pet"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 4"]}, {"name": "Axe", "type": "Weapon", "rarity": "Rare", "cost": "6", "effect": "On hit: Gain 1 damage.", "image_url": "https://backpackbattles.wiki.gg/images/f/fb/Axe.png?aadf73", "source_url": "https://backpackbattles.wiki.gg/wiki/Axe", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "3-6 (2.3/s)", "stamina": "1.4 (0.7/s)", "accuracy": "85%", "cooldown": "2s", "sockets": "2"}, "bag_properties": null, "timing_effects": []}, {"name": "Badger Rune", "type": "Gemstone", "rarity": "Legendary", "cost": "4", "effect": "Weapon sockets: On hit: Attack 3% faster. Armor & other sockets: During Battle Rage: Reduce / damage taken by 7. Backpack: Items use -10% stamina.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/<PERSON><PERSON>_Rune", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "<PERSON> Knuckles", "type": "Weapon", "rarity": "Unique", "cost": "10", "effect": "On hit: 30% chance to stun for 0.5s, this and Items gain 5% accuracy and 5% critical hit chance. During Battle Rage: <PERSON>gger 50% faster.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/<PERSON>_<PERSON>nuckles", "grid_layout": [["star", "star", "star"], ["star", "cell", "star"]], "grid_width": 3, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "3-5 (1.6/s)", "stamina": "0 (0/s)", "accuracy": "90%", "cooldown": "2.5s", "sockets": ""}, "bag_properties": null, "timing_effects": ["Seconds: 0.5"]}, {"name": "Busted Blade", "type": "Weapon", "rarity": "<PERSON><PERSON>", "cost": "14", "effect": "During Battle Rage: Decrease stamina usage to 3 and cooldown to 3s. Deals +5 damage per .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Busted_Blade", "grid_layout": [["cell", "cell", "cell", "cell", "cell", "cell", "cell", "cell", "cell"]], "grid_width": 9, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "50-60 (11/s)", "stamina": "5 (1/s)", "accuracy": "95%", "cooldown": "5s", "sockets": "6"}, "bag_properties": null, "timing_effects": ["Seconds: 3"]}, {"name": "Chain Whip", "type": "Weapon", "rarity": "Legendary", "cost": "8", "effect": "During Battle Rage additionally heal for 8 💚. Deals +1 damage for each buff you removed from your opponent. On hit: remove 2 💪 random buffs from your opponent.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Chain_Whip", "grid_layout": [["cell", "cell", "cell", "cell", "cell"]], "grid_width": 5, "grid_height": 1, "positional_synergies": ["For each synergy: for each buff you removed from your opponent"], "weapon_stats": {"damage": "4-9 (2.7/s)", "stamina": "2.1 (0.9/s)", "accuracy": "85%", "cooldown": "2.4s", "sockets": "2"}, "bag_properties": null, "timing_effects": []}, {"name": "Cheese", "type": "Food", "rarity": "Legendary", "cost": "8", "effect": "Every 4s ⏰: gain 10 ❤️ maximum health and a random buff. 🍎 Food: Triggers 10% faster for each Food of a different type (not Cheese).", "image_url": "https://backpackbattles.wiki.gg/images/a/a5/Cheese.png?5b7e3b", "source_url": "https://backpackbattles.wiki.gg/wiki/Cheese", "grid_layout": [["star", "star", "star", "cell", "cell", "star", "star", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": ["For each synergy: for each Food of a different type (not Cheese)", "Type synergy: Food: Triggers 10% faster for each Food of a different type (not Cheese)"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 4"]}, {"name": "Cheese Goobert", "type": "Pet", "rarity": "<PERSON><PERSON>", "cost": "14", "effect": "5 item activations: gain 18 ❤️ maximum health and 2 random buffs.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Cheese_Goobert", "grid_layout": [["star", "cell", "cell", "star", "star", "cell", "cell", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "<PERSON>", "type": "Weapon, Pet", "rarity": "Legendary", "cost": "7", "effect": "Deals +2 damage for each Pet.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Courage_Puppy", "grid_layout": [["star", "star", "star", "cell", "cell", "star", "star", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": ["For each synergy: for each Pet"], "weapon_stats": {"damage": "5-7 (1.7/s)", "stamina": "0 (0/s)", "accuracy": "95%", "cooldown": "3.5s", "sockets": ""}, "bag_properties": null, "timing_effects": []}, {"name": "Deerwood Guardian", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Damage taken reduced by 10%. Battle Rage lasts 0.8s longer for each -item. Every 1s ⏰ during Battle Rage: heal for 8 💚 and gain 3 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Deerwood_Guardian", "grid_layout": [["star", "star", "star", "star", "star", "star", "star", "star", "star", "cell", "cell"]], "grid_width": 11, "grid_height": 1, "positional_synergies": ["For each synergy: for each -item"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 0.8", "Seconds: 1"]}, {"name": "Double Axe", "type": "Weapon", "rarity": "Epic", "cost": "12", "effect": "On hit: gain 2 😡 damage. Battle Rage entered: <PERSON><PERSON> extra attack. Damage gain increased to 3.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Double_Axe", "grid_layout": [["cell", "cell", "cell", "cell", "cell", "cell", "cell", "cell"]], "grid_width": 8, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "6-12 (5/s)", "stamina": "2 (1.1/s)", "accuracy": "85%", "cooldown": "1.8s", "sockets": "5"}, "bag_properties": null, "timing_effects": []}, {"name": "Dragon Claws", "type": "Gloves", "rarity": "Epic", "cost": "4", "effect": "10% chance to resist . During Battle Rage: Items trigger 40% faster.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Dragon_Claws", "grid_layout": [["star", "cell"], ["cell", "star"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Dragonscale Armor", "type": "Armor", "rarity": "Epic", "cost": "7", "effect": "Battle Rage entered: <PERSON><PERSON> 40 . During Battle Rage: Damage taken reduced by 10%.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Dragonscale_Armor", "grid_layout": [["cell", "cell", "cell"], ["cell", "cell", "cell"]], "grid_width": 3, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "3"}, "bag_properties": null, "timing_effects": []}, {"name": "Dragon Set", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Every 1.2s ⏰ during Battle Rage: Gain 1 . If you have Dragonscale Armor, Dragonskin Boots and Dragon Claws: You have +2% lifesteal for each (up to 20%).", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Dragon_Set", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": ["For each synergy: for each (up to 20%)"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 1.2"]}, {"name": "<PERSON><PERSON> Boots", "type": "Shoes", "rarity": "Epic", "cost": "6", "effect": "20% chance to resist . Battle Rage entered: cleanse 3 ☠️ debuffs, gain 1 💪 and 20 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Dragonskin_Boots", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "<PERSON><PERSON> Bag", "type": "Bag", "rarity": "Unique", "cost": "16", "effect": "Add 6 🎒 backpack slots. Health drops below 50%: Enter Battle Rage for 5s (once). During Battle Rage: items inside 🎒 trigger 30% faster. You take 20% reduced damage.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/<PERSON><PERSON>_Bag", "grid_layout": [["cell", "cell", "cell"], ["cell", "cell", "cell"]], "grid_width": 3, "grid_height": 2, "positional_synergies": ["Items inside: Items inside trigger 30% faster"], "weapon_stats": null, "bag_properties": {"slots_added": 6, "special_effects": ["Increased trigger speed", "Healing bonus", "Combat bonus"]}, "timing_effects": ["Seconds: 5"]}, {"name": "Elephant Rune", "type": "Gemstone", "rarity": "Legendary", "cost": "4", "effect": "Weapon sockets: On hit: 25% chance to stun for 0.5s (cooldown 3s). Armor & other sockets: Start of battle: 40% chance to resist debuffs for 4s. Backpack: gain 40 ❤️ maximum health.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Elephant_Rune", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 0.5", "Seconds: 3", "Seconds: 4", "Start of battle"]}, {"name": "Extra Angy", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Battle Rage ended: After 4s ⏰, enter Battle Rage for 50% of the duration (once).", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Extra_Angy", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 4"]}, {"name": "Forging Hammer", "type": "Weapon", "rarity": "Unique", "cost": "3", "effect": "Deals additional +1 damage per .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Forging_Hammer", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "2-5 (1/s)", "stamina": "0 (0/s)", "accuracy": "95%", "cooldown": "3.5s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "<PERSON>", "type": "Gemstone", "rarity": "Legendary", "cost": "4", "effect": "Weapon sockets: Critical hit chance +15%. Critical damage +15%. Armor & other sockets: 40% chance to resist . Backpack: Every 4s ⏰: Inflict 1 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/<PERSON>_<PERSON>e", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 4"]}, {"name": "Power Puppy", "type": "Pet", "rarity": "Legendary", "cost": "7", "effect": "Every 3.2s ⏰: Randomly gain 1 💪 or 1 or 1 . Triggers 10% faster for each Pet.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Power_Puppy", "grid_layout": [["star", "star", "star", "cell", "cell", "star", "star", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": ["For each synergy: for each Pet"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3.2"]}, {"name": "<PERSON> Go<PERSON>rt <PERSON><PERSON>", "type": "Pet", "rarity": "<PERSON><PERSON>", "cost": "70", "effect": "6 item activations: gain 20 ❤️ maximum health, 20 , 2 and 2 random buffs, inflict 4 , and Weapons gain 4 ⚔️ damage.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Rainbow_Goobert_Deathslushy_Mansquisher", "grid_layout": [["star", "star", "star", "star", "star", "cell", "cell", "star", "star", "cell", "cell", "star", "star", "star", "star", "star"]], "grid_width": 16, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Shaman <PERSON>", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Runes are offered in the shop. Start of battle: gain 1 💪 for each socketed Gemstone. Every 3.4s ⏰: use 2 🔮 to gain 5 💪 random buffs.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Shaman_Mask", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": ["For each synergy: for each socketed Gemstone"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3.4", "Start of battle"]}, {"name": "<PERSON><PERSON>", "type": "Accessory", "rarity": "Legendary", "cost": "6", "effect": "Battle Rage lasts 2s longer. Battle Rage entered: Gain 1 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Spiked_Collar", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 2"]}, {"name": "Spiked Staff", "type": "Weapon", "rarity": "Legendary", "cost": "16", "effect": "On attack: use 3 🔮 to gain 2 , and during Battle Rage also gain 2 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Spiked_Staff", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "8-10 (5/s)", "stamina": "1 (0.6/s)", "accuracy": "90%", "cooldown": "1.8s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "Utility Pouch", "type": "Bag", "rarity": "Unique", "cost": "18", "effect": "Add 8 🎒 backpack slots. Weapons inside deal +30% damage but attack 30% slower. After 5s ⏰: Enter Battle Rage for 6s. During Battle Rage: +35% lifesteal.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Utility_Pouch", "grid_layout": [["cell", "cell", "cell", "cell", "cell", "cell", "cell", "cell"]], "grid_width": 8, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": {"slots_added": 8, "special_effects": ["Combat bonus"]}, "timing_effects": ["Seconds: 5", "Seconds: 6"]}, {"name": "<PERSON>", "type": "Pet", "rarity": "Legendary", "cost": "7", "effect": "Every 4s ⏰: gain 10 💪 and cleanse 1 . Triggers 15% faster for each Pet.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Wisdom_Puppy", "grid_layout": [["star", "star", "cell", "star", "star", "cell", "star", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": ["For each synergy: for each Pet"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 4"]}, {"name": "<PERSON>", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Wolf companions are offered in the shop. Weapons have 10% critical hit chance (+12% for each Pet). Every 3s ⏰: If you have at least 10 , gain 1 . Otherwise, gain 10 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/<PERSON>_<PERSON>m", "grid_layout": [["diamond", "diamond", "diamond", "diamond", "diamond", "diamond", "diamond", "star", "star", "star", "cell", "star", "star", "star", "diamond", "diamond", "diamond", "diamond", "diamond", "diamond", "diamond"]], "grid_width": 21, "grid_height": 1, "positional_synergies": ["For each synergy: for each Pet)"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3"]}, {"name": "Amethyst Egg", "type": "Pet", "rarity": "Legendary", "cost": "10", "effect": "Start of battle: inflict 4 ☠️ random debuffs. Hatches after 2 rounds in your backpack.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Amethyst_Egg", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle", "Hatching mechanic"]}, {"name": "Amethyst Whelp", "type": "Weapon", "rarity": "<PERSON><PERSON>", "cost": "14", "effect": "Start of battle: inflict 4 ☠️ random debuffs. On hit: Remove a random buff from your opponent.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Amethyst_Whelp", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "5-10 (3.6/s)", "stamina": "0 (0/s)", "accuracy": "90%", "cooldown": "2.1s", "sockets": "1"}, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Book of Ice", "type": "Accessory", "rarity": "Rare", "cost": "10", "effect": "Every 3.2s ⏰: use 2 🔮 to inflict 3 . 10% chance to cast the Spell scroll for free.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Book_of_Ice", "grid_layout": [["cell", "star"], ["cell", "star"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3.2"]}, {"name": "Burning Banner", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "25% chance to protect your buffs from removal and your opponent's debuffs from cleansing. -item activates: 50% chance to inflict 1 ☠️ for 5s. Every 3.8s ⏰: remove 2 💪 buffs from your opponent and gain 2 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Burning_Banner", "grid_layout": [["star", "star", "star", "star", "cell", "star", "star", "star", "star", "star", "star", "star", "star", "cell", "star", "star", "star", "star"]], "grid_width": 18, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 5", "Seconds: 3.8"]}, {"name": "Burning Blade", "type": "Weapon", "rarity": "Legendary", "cost": "21", "effect": "On hit: Gain 1 . 4 💪 gained: This and Weapons gain +1 damage.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Burning_Blade", "grid_layout": [["star", "star", "cell", "star", "star", "cell", "star", "star", "cell", "star", "star"]], "grid_width": 11, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "7-9 (5/s)", "stamina": "1 (0.6/s)", "accuracy": "90%", "cooldown": "1.6s", "sockets": "2"}, "bag_properties": null, "timing_effects": []}, {"name": "Burning Sword", "type": "Weapon", "rarity": "Epic", "cost": "13", "effect": "On hit: 40% chance to gain 1 . 5 💪 gained: This and Weapons gain +1 damage.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Burning_Sword", "grid_layout": [["star", "star", "cell", "star", "star", "cell", "star", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "2-4 (1.9/s)", "stamina": "1 (0.6/s)", "accuracy": "90%", "cooldown": "1.6s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "<PERSON><PERSON>", "type": "Pet", "rarity": "Epic", "cost": "11", "effect": "6 item activations: heal for 12 💚 and gain 2 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Chi<PERSON>_Goobert", "grid_layout": [["star", "cell", "cell", "star", "star", "cell", "cell", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Chili Pepper", "type": "Food", "rarity": "Rare", "cost": "5", "effect": "Every 5s ⏰: gain 1 💪 and heal 5. When you have at least 10 , cleanse 1 ☠️ debuff. 🍎 Food: Triggers 10% faster for each Food of a different type (not Chili Pepper).", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Chili_Pepper", "grid_layout": [["star", "star", "cell", "star", "star", "cell", "star", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": ["For each synergy: for each Food of a different type (not Chili Pepper)", "Type synergy: Food: Triggers 10% faster for each Food of a different type (not Chili Pepper)"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 5"]}, {"name": "Dark Lantern", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Start of battle: Lose 50% health. Before defeat: Reincarnate with 50% life and become invulnerable for 1.5s. On reincarnation: deal 5 ❄️ damage for each -item and inflict 7 ☠️ debuffs for each -item.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Dark_Lantern", "grid_layout": [["star", "diamond", "star", "cell", "diamond", "star", "cell", "diamond", "star", "diamond"]], "grid_width": 10, "grid_height": 1, "positional_synergies": ["For each synergy: for each -item and inflict 7 debuffs for each -item"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 1.5", "Start of battle"]}, {"name": "Draconic Orb", "type": "Accessory", "rarity": "Epic", "cost": "8", "effect": "15 💪 reached: Your next 3 hits are critical. Every 3.8s ⏰: remove 1 💪 from your opponent and gain 1 💪 per removed .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Dracon<PERSON>_Orb", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3.8"]}, {"name": "Dragon Nest", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Start of battle: <PERSON>ain 2 , 2 , 4 and 2 . 🐉 Dragon attacks: heal for 5 💚. Dragon Eggs hatch after 1 round. Additional dragon eggs are offered in the shop.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Dragon_Nest", "grid_layout": [["star", "star", "star", "cell", "cell", "star", "star", "cell", "cell", "star", "star", "star"]], "grid_width": 12, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle", "Hatching mechanic"]}, {"name": "Emerald Egg", "type": "Pet", "rarity": "Legendary", "cost": "10", "effect": "Start of battle: <PERSON><PERSON> 3 . <PERSON><PERSON> after 2 rounds in your backpack.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Emerald_Egg", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle", "Hatching mechanic"]}, {"name": "Emerald Whelp", "type": "Weapon, Pet", "rarity": "<PERSON><PERSON>", "cost": "14", "effect": "Start of battle: <PERSON><PERSON> 3 . On hit: Inflict 3 .", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Emerald_Whelp", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "5-10 (3.6/s)", "stamina": "0 (0/s)", "accuracy": "90%", "cooldown": "2.1s", "sockets": "1"}, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Everburning", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "Burning Sword and Burning Blade use -40% stamina. After 7s ⏰: gain 1 💪 for each Flame.", "image_url": "https://backpackbattles.wiki.gg/images/7/70/Everburning.png?6bd11a", "source_url": "https://backpackbattles.wiki.gg/wiki/Everburning", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": ["For each synergy: for each Flame"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 7"]}, {"name": "Fire Pit", "type": "Bag", "rarity": "Unique", "cost": "20", "effect": "Add 9 🎒 backpack slots. Shop entered: Spend 1 gold to generate Flame. Start battle: gain 4 ❤️ maximum health for each -items inside 🎒.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Fire_Pit", "grid_layout": [["cell", "cell", "cell", "cell", "cell", "cell", "cell", "cell", "cell"]], "grid_width": 9, "grid_height": 1, "positional_synergies": ["For each synergy: for each -item inside"], "weapon_stats": null, "bag_properties": {"slots_added": 9, "special_effects": ["Healing bonus"]}, "timing_effects": []}, {"name": "Flame", "type": "Accessory", "rarity": "Common", "cost": "1", "effect": "Start of battle: Gain 1 .", "image_url": "https://backpackbattles.wiki.gg/images/9/9f/Flame.png?ae260e", "source_url": "https://backpackbattles.wiki.gg/wiki/Flame", "grid_layout": [["cell"]], "grid_width": 1, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Flame Whip", "type": "Weapon", "rarity": "Legendary", "cost": "10", "effect": "On hit: use 1 🔮 to gain 4 💪 and deal +8 ⚔️ damage.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Flame_Whip", "grid_layout": [["cell", "cell", "cell", "cell", "cell"]], "grid_width": 5, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "6-10 (4.7/s)", "stamina": "1.5 (0.9/s)", "accuracy": "90%", "cooldown": "1.7s", "sockets": "2"}, "bag_properties": null, "timing_effects": []}, {"name": "Friendly Fire", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Every 3s ⏰: use 1 🔮 to gain 2 . Triggers 10% faster for each -item. 20 💪 reached: Gain 5 . 40 💪 reached: Gain 15 . 60 💪 reached: deal 100 ❄️ damage.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Friendly_Fire", "grid_layout": [["star", "star", "star", "star", "cell", "star", "star", "cell", "star", "star", "star", "star"]], "grid_width": 12, "grid_height": 1, "positional_synergies": ["For each synergy: for each -item"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3"]}, {"name": "Frozen Flame", "type": "Accessory", "rarity": "Unique", "cost": "10", "effect": "Start of battle: gain 8 💪 for each -item. 6 💪 gained: Inflict 2 . For each of your opponent, the item has ****% critical hit chance and +2% critical damage. Additional -items are offered in the shop.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Frozen_Flame", "grid_layout": [["diamond", "star", "cell", "star", "star", "cell", "star", "star", "star", "star"]], "grid_width": 10, "grid_height": 1, "positional_synergies": ["For each synergy: for each -item", "For each synergy: For each of your opponent, the item has +1"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Ice Dragon", "type": "Weapon, Pet", "rarity": "<PERSON><PERSON>", "cost": "15", "effect": "On hit: Inflict 1 . Opponent reaches 12 : <PERSON><PERSON> 50 . You take -20% -damage.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Ice_Dragon", "grid_layout": [["cell", "cell", "cell"], ["cell", "cell", "cell"]], "grid_width": 3, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "15-20 (8.3/s)", "stamina": "0 (0/s)", "accuracy": "90%", "cooldown": "2.1s", "sockets": "2"}, "bag_properties": null, "timing_effects": []}, {"name": "<PERSON><PERSON> Dagger", "type": "Weapon", "rarity": "Epic", "cost": "6", "effect": "On hit: use 1 🔮 to gain 2 damage. On stun: Triggers extra attack.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/<PERSON><PERSON>_<PERSON>", "grid_layout": [["cell", "cell"]], "grid_width": 2, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "3-7 (1.4/s)", "stamina": "0 (0/s)", "accuracy": "95%", "cooldown": "3.5s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "<PERSON><PERSON>pear", "type": "Weapon", "rarity": "Epic", "cost": "8", "effect": "Before miss: use 1 🔮 to hit instead and deal +5 ⚔️ damage. On hit: Destroy 5 for each -item in front of it.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Mo<PERSON>_Spear", "grid_layout": [["star", "star", "star", "star", "star", "cell", "cell", "cell", "cell"]], "grid_width": 9, "grid_height": 1, "positional_synergies": ["For each synergy: for each -item in front of it"], "weapon_stats": {"damage": "3-8 (3.7/s)", "stamina": "1 (0.7/s)", "accuracy": "65%", "cooldown": "1.5s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "Obsidian Dragon", "type": "Weapon, Pet", "rarity": "<PERSON><PERSON>", "cost": "18", "effect": "8 💪 gained: Gain 2 damage and the next hit of the Weapon is critical.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Obsidian_Dragon", "grid_layout": [["cell", "cell", "cell", "cell", "star", "cell", "cell"]], "grid_width": 7, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "11-16 (6.4/s)", "stamina": "0 (0/s)", "accuracy": "90%", "cooldown": "2.1s", "sockets": "2"}, "bag_properties": null, "timing_effects": []}, {"name": "Offering Bowl", "type": "Bag", "rarity": "Unique", "cost": "8", "effect": "Add 4 🎒 backpack slots. Start of battle: Gain 1 . Shop entered: Consume all items inside 🎒. Create a Flame and different items based on the combined value.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Offering_Bowl", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": ["Items inside: items inside"], "weapon_stats": null, "bag_properties": {"slots_added": 4, "special_effects": []}, "timing_effects": ["Start of battle"]}, {"name": "Phoenix", "type": "Weapon, Pet", "rarity": "Legendary", "cost": "11", "effect": "On attack: Lose 11 health. Before defeat: Use all your to reincarnate with 6 health per (once).", "image_url": "https://backpackbattles.wiki.gg/images/c/c1/Phoenix.png?c8ba8a", "source_url": "https://backpackbattles.wiki.gg/wiki/Phoenix", "grid_layout": [["cell", "cell", "cell"], ["cell", "cell", "cell"]], "grid_width": 3, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "15-20 (7/s)", "stamina": "0 (0/s)", "accuracy": "85%", "cooldown": "2.5s", "sockets": ""}, "bag_properties": null, "timing_effects": []}, {"name": "Rainbow Goobert Epicglob Uberviscous", "type": "Pet", "rarity": "<PERSON><PERSON>", "cost": "67", "effect": "6 item activations: heal for 20 💚, gain 20 , 2 and 4 , inflict 4 , and Weapons gain 4 ⚔️ damage.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Rainbow_Goobert_Epicglob_Uberviscous", "grid_layout": [["star", "star", "star", "star", "star", "cell", "cell", "star", "star", "cell", "cell", "star", "star", "star", "star", "star"]], "grid_width": 16, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": []}, {"name": "Sapphire Egg", "type": "Pet", "rarity": "Legendary", "cost": "10", "effect": "Start of battle: <PERSON><PERSON> 4 . <PERSON><PERSON> after 2 rounds in your backpack.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Sapphire_Egg", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Start of battle", "Hatching mechanic"]}, {"name": "Sapphire Whelp", "type": "Weapon, Pet", "rarity": "<PERSON><PERSON>", "cost": "14", "effect": "Start of battle: <PERSON><PERSON> 4 . On hit: use 2 🔮 to gain 5 💪 and a random other buff.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Sapphire_Whelp", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "5-10 (3.6/s)", "stamina": "0 (0/s)", "accuracy": "90%", "cooldown": "2.1s", "sockets": "1"}, "bag_properties": null, "timing_effects": ["Start of battle"]}, {"name": "Solar<PERSON>", "type": "Skill", "rarity": "Unique", "cost": "5", "effect": "After 10s ⏰: cleanse 20 ☠️ debuffs. Sun Shield blocked damage: 30% chance to gain 1 . Sun Armor uses : Gain 10 .", "image_url": "https://backpackbattles.wiki.gg/images/1/1f/Solaris.png?e9d760", "source_url": "https://backpackbattles.wiki.gg/wiki/Solaris", "grid_layout": [["star", "star", "star", "star", "star", "star", "star", "star", "cell", "star", "star", "star", "star", "star", "star", "star", "star"]], "grid_width": 17, "grid_height": 1, "positional_synergies": [], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 10"]}, {"name": "Spell Scroll: <PERSON><PERSON>", "type": "Spell Scroll", "rarity": "Rare", "cost": "4", "effect": "Every 3s ⏰: deal 5 ❄️ damage and inflict 4 ☠️ for 3s. Max uses: 3 + 1 for each -item (except Spell scrolls).", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Spell_Scroll%3A_Frostbolt", "grid_layout": [["star", "star", "star", "cell", "cell", "star", "star", "star"]], "grid_width": 8, "grid_height": 1, "positional_synergies": ["For each synergy: for each -item (except Spell scrolls)"], "weapon_stats": null, "bag_properties": null, "timing_effects": ["Seconds: 3", "Seconds: 3"]}, {"name": "Staff of Fire", "type": "Weapon", "rarity": "Legendary", "cost": "18", "effect": "On attack: use 2 🔮 and 2 to gain 6 damage.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Staff_of_Fire", "grid_layout": [["cell", "cell"], ["cell", "cell"]], "grid_width": 2, "grid_height": 2, "positional_synergies": [], "weapon_stats": {"damage": "10-12 (6.1/s)", "stamina": "1 (0.6/s)", "accuracy": "90%", "cooldown": "1.8s", "sockets": "1"}, "bag_properties": null, "timing_effects": []}, {"name": "Sun Armor", "type": "Armor", "rarity": "<PERSON><PERSON>", "cost": "15", "effect": "-Items gain Holy. Start of battle: <PERSON><PERSON> 70 . gain 1 💪 for each -item. Every 3s ⏰: use 1 🔮 to heal for 12 💚 and cleanse 2 ☠️ debuffs.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Sun_Armor", "grid_layout": [["star", "star", "star", "cell", "cell", "star", "star", "cell", "cell", "star", "star", "cell", "cell", "star", "star", "star"]], "grid_width": 16, "grid_height": 1, "positional_synergies": ["For each synergy: for each -item"], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "3"}, "bag_properties": null, "timing_effects": ["Seconds: 3", "Start of battle"]}, {"name": "Sun Shield", "type": "Shield", "rarity": "<PERSON><PERSON>", "cost": "14", "effect": "items gained 12 : deal 4 ❄️ damage. On attacked (/): 30% chance to prevent 14 damage and remove 0.7 stamina from your opponent.", "image_url": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Sun_Shield", "grid_layout": [["star", "star", "star", "cell", "cell", "star", "star", "cell", "cell", "star", "star", "cell", "cell", "star", "star", "star"]], "grid_width": 16, "grid_height": 1, "positional_synergies": [], "weapon_stats": {"damage": "", "stamina": "", "accuracy": "", "cooldown": "", "sockets": "2"}, "bag_properties": null, "timing_effects": ["Seconds: 0.7"]}], "recipes": [{"result": "Amulet of Alchemy", "ingredients": ["Unidentified Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Amulet of Energy", "ingredients": ["Unidentified Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Amulet of Feasting", "ingredients": ["Unidentified Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Amulet of Life", "ingredients": ["Unidentified Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Amulet of Steel", "ingredients": ["Unidentified Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Box of Prosperity", "ingredients": ["Box of Riches"], "catalyst": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Bunch of Coins", "ingredients": ["Piggybank"], "catalyst": "Hammer", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Gloves of Power", "ingredients": ["Gloves of Haste", "Stone Skin Potion"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Goobert", "ingredients": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Platinum Customer Card", "ingredients": ["Customer Card", "Customer Card"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Shepherd's Crook", "ingredients": ["Broom", "Whetstone"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Spiked Shield", "ingredients": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Steel Goobert", "ingredients": ["Goobert", "Hero Sword"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Stone Armor", "ingredients": ["<PERSON><PERSON> Armor", "Stone Skin Potion"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON>", "ingredients": ["Cap of Resilience", "Stone Skin Potion"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Stone Shoes", "ingredients": ["<PERSON><PERSON>", "Stone Skin Potion"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Health Potion", "ingredients": ["Health Potion"], "catalyst": "<PERSON><PERSON><PERSON>", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Health Potion", "ingredients": ["Health Potion", "Healing Herbs"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Heroic Potion", "ingredients": ["Heroic Potion"], "catalyst": "<PERSON><PERSON><PERSON>", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Heroic Potion", "ingredients": ["Heroic Potion", "Banana"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Stone Skin Potion", "ingredients": ["Stone Skin Potion", "Stone", "Stone"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Stone Skin Potion", "ingredients": ["Stone Skin Potion"], "catalyst": "<PERSON><PERSON><PERSON>", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Amulet of Darkness", "ingredients": ["Unidentified Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Heart of Darkness", "ingredients": ["Heart Container", "Corrupted Crystal"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "King Crown", "ingredients": ["Glowing Crown", "Box of Riches"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Snowball", "ingredients": ["Wonky Snowman"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Amulet of the Wild", "ingredients": ["Unidentified Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Corrupted Armor", "ingredients": ["Holy Armor", "Corrupted Crystal"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Moon Armor", "ingredients": ["Holy Armor", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Ice Armor", "ingredients": ["<PERSON><PERSON> Armor", "Snowball"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Vampiric Armor", "ingredients": ["<PERSON><PERSON> Armor", "Blood Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Burning Coal", "ingredients": ["Lump of Coal"], "catalyst": "Fire", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Vampiri<PERSON>s", "ingredients": ["Gloves of Haste", "Blood Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Cap of Discomfort", "ingredients": ["Cap of Resilience", "Corrupted Crystal"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "King <PERSON><PERSON>", "ingredients": ["Goobert", "King Crown"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Light Goobert", "ingredients": ["Goobert", "Lightsaber"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Blood Goobert", "ingredients": ["Goobert", "Blood Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON>", "ingredients": ["Health Potion", "Blueberries"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Vampiric Potion", "ingredients": ["Strong Health Potion"], "catalyst": "Blood Amulet", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Moon Shield", "ingredients": ["Shield of Valor", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON><PERSON>", "ingredients": ["<PERSON><PERSON>", "Snowball"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Stone Golem", "ingredients": ["Heart Container", "Stone", "Stone", "Stone", "Stone"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON>", "ingredients": ["<PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Darksaber", "ingredients": ["Lightsaber", "Corrupted Crystal"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Pandamonium", "ingredients": ["Pan", "Corrupted Crystal"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Burning Torch", "ingredients": ["<PERSON>ch"], "catalyst": "Fire", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Eggscalibur", "ingredients": ["Pan", "Heroic Potion"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Holy Spear", "ingredients": ["Spear", "Glowing Crown"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Shell Totem", "ingredients": ["Wooden Sword", "Shiny Shell"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Snow Stick", "ingredients": ["Broom", "Snowball"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Frostbite", "ingredients": ["Hungry Blade", "Snowball"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Magic Torch", "ingredients": ["<PERSON>ch", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON><PERSON> Dagger", "ingredients": ["<PERSON>gger", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Magic Staff", "ingredients": ["Broom", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Serpent Staff", "ingredients": ["Magic Staff", "Pestilence Flask"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Prismatic Sword", "ingredients": ["Wooden Sword", "Prismatic Orb"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON><PERSON><PERSON>", "ingredients": ["Hungry Blade", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Claws of Attack", "ingredients": ["Gloves of Haste", "<PERSON><PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Crossblades", "ingredients": ["Falcon Blade", "<PERSON> Longsword"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Falcon Blade", "ingredients": ["Hero Sword", "Gloves of Haste", "Gloves of Haste"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON> Longsword", "ingredients": ["Hero Sword", "Whetstone", "Whetstone"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Hero Sword", "ingredients": ["Wooden Sword", "Whetstone", "Whetstone"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON>", "ingredients": ["Ripsaw Blade", "Whetstone"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON>", "ingredients": ["<PERSON>gger", "Pestilence Flask"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON><PERSON>", "ingredients": ["Broom", "Pan"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON>ch", "ingredients": ["Wooden Sword", "Lump of Coal"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Thornbloom", "ingredients": ["Thorn Whip", "Heroic Potion"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Bloody Dagger", "ingredients": ["<PERSON>gger", "Blood Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON><PERSON><PERSON>", "ingredients": ["Hungry Blade", "Thorn Whip"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Armored Power Puppy", "ingredients": ["Power Puppy"], "catalyst": "Utility Pouch", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Armored Power Puppy", "ingredients": ["Power Puppy"], "catalyst": "Forging Hammer", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Armored Wisdom Puppy", "ingredients": ["<PERSON>"], "catalyst": "Utility Pouch", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Armored Wisdom Puppy", "ingredients": ["<PERSON>"], "catalyst": "Forging Hammer", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Dragon Claws", "ingredients": ["Gloves of Haste"], "catalyst": "Utility Pouch", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Dragon Claws", "ingredients": ["Gloves of Haste"], "catalyst": "Forging Hammer", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Dragonscale Armor", "ingredients": ["<PERSON><PERSON> Armor"], "catalyst": "Utility Pouch", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Dragonscale Armor", "ingredients": ["<PERSON><PERSON> Armor"], "catalyst": "Forging Hammer", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON> Boots", "ingredients": ["<PERSON><PERSON>"], "catalyst": "Utility Pouch", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON> Boots", "ingredients": ["<PERSON><PERSON>"], "catalyst": "Forging Hammer", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Cheese Goobert", "ingredients": ["Goobert", "Cheese"], "catalyst": "", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Armored Courage Puppy", "ingredients": ["<PERSON>"], "catalyst": "Utility Pouch", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Armored Courage Puppy", "ingredients": ["<PERSON>"], "catalyst": "Forging Hammer", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Chain Whip", "ingredients": ["Thorn Whip"], "catalyst": "Forging Hammer", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Chain Whip", "ingredients": ["Thorn Whip"], "catalyst": "Utility Pouch", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Spiked Staff", "ingredients": ["Magic Staff", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Busted Blade", "ingredients": ["Impractically Large Greatsword"], "catalyst": "Forging Hammer", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Busted Blade", "ingredients": ["Impractically Large Greatsword"], "catalyst": "Utility Pouch", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Double Axe", "ingredients": ["Axe", "Axe"], "catalyst": "", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Sun Armor", "ingredients": ["Holy Armor", "Flame", "Flame"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON>", "ingredients": ["Goobert", "Chili Pepper"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Rainbow Goobert Epicglob Uberviscous", "ingredients": ["Goobert", "Blood Goobert", "<PERSON><PERSON>", "Light Goobert", "Steel Goobert"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Sun Shield", "ingredients": ["Shield of Valor", "Flame", "Flame"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Emerald Whelp", "ingredients": ["Emerald Egg"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Obsidian Dragon", "ingredients": ["<PERSON>", "Draconic Orb"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Sapphire Whelp", "ingredients": ["Sapphire Egg"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Amethyst Whelp", "ingredients": ["Amethyst Egg"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Staff of Fire", "ingredients": ["Magic Staff", "Draconic Orb"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Burning Blade", "ingredients": ["Burning Sword", "Whetstone", "Whetstone"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Burning Blade", "ingredients": ["<PERSON> Longsword", "Flame", "Flame"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Burning Sword", "ingredients": ["Hero Sword", "Flame", "Flame"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Flame Whip", "ingredients": ["Thorn Whip", "Flame", "Flame"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON> Dagger", "ingredients": ["<PERSON>gger", "Flame", "Flame"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON>pear", "ingredients": ["Spear", "Flame", "Flame"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON> Piggy", "ingredients": ["Piggybank", "<PERSON> Clover", "<PERSON> Clover"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Carrot Goobert", "ingredients": ["Goobert", "Carrot", "Carrot"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Rat Chef", "ingredients": ["Rat", "Healing Herbs"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Rainbow Goobert Megasludge Alphapuddle", "ingredients": ["Goobert", "Blood Goobert", "Light Goobert", "Carrot Goobert", "Steel Goobert"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Squirrel Archer", "ingredients": ["Squirrel", "Shortbow"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Critwood Staff", "ingredients": ["Magic Staff", "Acorn Collar"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Belladonna's Shade", "ingredients": ["Shortbow", "Pestilence Flask"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Belladonna's Whisper", "ingredients": ["Bow and Arrow", "Pestilence Flask"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Fortuna's Grace", "ingredients": ["Bow and Arrow", "<PERSON> Clover", "<PERSON> Clover"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Fortuna's Hope", "ingredients": ["Shortbow", "<PERSON> Clover", "<PERSON> Clover"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Tusk Piercer", "ingredients": ["Bow and Arrow", "<PERSON><PERSON><PERSON>"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Tusk Poker", "ingredients": ["Shortbow", "<PERSON><PERSON><PERSON>"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Ice Dragon", "ingredients": ["<PERSON>", "White-Eyes Blue Dragon"], "catalyst": "", "class_restriction": "<PERSON>, Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Pestilence Flask", "ingredients": ["Pestilence Flask", "Fly Agaric"], "catalyst": "", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Pestilence Flask", "ingredients": ["Pestilence Flask"], "catalyst": "<PERSON><PERSON><PERSON>", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Doom Cap", "ingredients": ["Fly Agaric", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Poison Goobert", "ingredients": ["Goobert", "Fly Agaric", "Fly Agaric"], "catalyst": "", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Rainbow Goobert Omegaooze Primeslime", "ingredients": ["Goobert", "Blood Goobert", "Light Goobert", "Poison Goobert", "Steel Goobert"], "catalyst": "", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Demonic Flask", "ingredients": ["<PERSON><PERSON>"], "catalyst": "<PERSON><PERSON><PERSON>", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Demonic Flask", "ingredients": ["<PERSON><PERSON>", "Corrupted Crystal"], "catalyst": "", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Divine Potion", "ingredients": ["Divine Potion"], "catalyst": "<PERSON><PERSON><PERSON>", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Mana <PERSON>tion", "ingredients": ["<PERSON><PERSON>"], "catalyst": "<PERSON><PERSON><PERSON>", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Vampiric Potion", "ingredients": ["Vampiric Potion"], "catalyst": "<PERSON><PERSON><PERSON>", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON>", "ingredients": ["<PERSON>", "Holo Fire Lizard"], "catalyst": "", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Staff of Unhealing", "ingredients": ["Magic Staff", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}], "metadata": {"total_items": 308, "total_recipes": 128, "crawl_timestamp": "2025-06-15 08:59:42", "enhancement_note": "Items enhanced with positional, weapon stats, bag properties, and timing data", "emoji_restoration": {"total_items_processed": 308, "items_with_emoji_fixes": 197, "restoration_timestamp": "2025-06-15 09:20:33"}}}