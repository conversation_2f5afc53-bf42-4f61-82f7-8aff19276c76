import matplotlib.pyplot as plt

def plot_training_metrics(avg_rewards, win_rates, episode_lengths):
    """
    Plots the training metrics over time.

    Args:
        avg_rewards (list): List of average rewards per episode.
        win_rates (list): List of win rates.
        episode_lengths (list): List of episode lengths.
    """
    episodes = range(len(avg_rewards))

    plt.figure(figsize=(12, 8))

    plt.subplot(3, 1, 1)
    plt.plot(episodes, avg_rewards)
    plt.xlabel("Episode")
    plt.ylabel("Average Reward")
    plt.title("Average Reward per Episode during Training")

    plt.subplot(3, 1, 2)
    plt.plot(episodes, win_rates)
    plt.xlabel("Episode")
    plt.ylabel("Win Rate")
    plt.title("Win Rate during Training")

    plt.subplot(3, 1, 3)
    plt.plot(episodes, episode_lengths)
    plt.xlabel("Episode")
    plt.ylabel("Episode Length")
    plt.title("Episode Length during Training")

    plt.tight_layout()
    plt.show()

# Optional visualization functions can be added here later
def visualize_backpack(backpack_state):
    """
    Visualizes the agent's backpack state.

    Args:
        backpack_state (list): A 2D list representing the backpack grid.
                               Each element could represent an item or be None.
    """
    rows = len(backpack_state)
    cols = len(backpack_state[0]) if rows > 0 else 0

    fig, ax = plt.subplots(figsize=(cols, rows))
    ax.set_xlim([0, cols])
    ax.set_ylim([0, rows])
    ax.set_xticks(range(cols + 1))
    ax.set_yticks(range(rows + 1))
    ax.grid(True)

    # Invert the y-axis to have (0,0) at the top-left
    ax.invert_yaxis()

    for r in range(rows):
        for c in range(cols):
            item = backpack_state[r][c]
            if item:
                # You would need a way to get item names or representations
                # For now, just put a placeholder text
                ax.text(c + 0.5, r + 0.5, "Item", ha="center", va="center", color="white")
                ax.add_patch(plt.Rectangle((c, r), 1, 1, fill=True, color="blue"))
            else:
                 ax.add_patch(plt.Rectangle((c, r), 1, 1, fill=False, edgecolor="black"))


    plt.title("Backpack State")
    plt.show()
def visualize_action_sequence(actions):
    """
    Visualizes the sequence of actions taken by the agent.

    Args:
        actions (list): A list of actions taken by the agent.
    """
    print("Action Sequence:")
    for i, action in enumerate(actions):
        print(f"Step {i+1}: {action}")
def display_combat_outcome(outcome):
    """
    Displays the outcome of a combat simulation.

    Args:
        outcome (dict): A dictionary containing combat results (e.g., winner, damage dealt).
    """
    print("Combat Outcome:")
    for key, value in outcome.items():
        print(f"{key}: {value}")